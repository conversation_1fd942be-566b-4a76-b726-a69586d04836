

--DEMON OF STATIC by MitchOfTarcoola


local maxHP = 20 --health for static crack effect. Editable.

--make table of all groups. Makes referencing ModelParts a lot less fiddly.
--Not my code, but slightly modified.
--Was made before the contest
groups = {}
do
  local function groupIndex(m, t)
    
    if not t then t = {} end
    local c = m:getChildren()
    
    for _, p in ipairs(c) do
        if p:getType() == "GROUP" then
            t[p:getName()] = p
            groupIndex(p, t)
        end
    end
    
    return t
    
  end
  groups = groupIndex(models.model)
end


--================================================================  ACTION WHEEL  =================================================================
local mainPage = action_wheel:newPage() --action wheel
action_wheel:setPage(mainPage)



function pings.transform(state) --transformation action
  if isDemon == state then
    transformState = 1
    staticState = "in"
  end
  
end

act_transform = mainPage:newAction()
  :setColor(1,0,0)
  :setHoverColor(1,0.5,0)
  :setItem("minecraft:player_head{SkullOwner:".. avatar:getEntityName().."}")
  :setTitle("Transform")
  :setOnLeftClick(function () --transform
    if transformState == 0 then
      pings.transform(isDemon)
    end
  end)



s_sfx_on = true
s_sfx_state = 0 --0: on. 1: off for host. 2: off
function pings.staticsfx(state) --static SFX toggle
  if host:isHost() then
    s_sfx_on = state == 0
  else
    s_sfx_on = state ~= 2
  end
end

function act_staticSfx_click()
  s_sfx_state = s_sfx_state + 1
    if s_sfx_state == 3 then
      s_sfx_state = 0
    end

    pings.staticsfx(s_sfx_state)

    if s_sfx_state == 0 then
      host:setActionbar("Static SFX: ON")
      act_static_sfx:setTitle("Static SFX: ON"):setItem("minecraft:white_wool")
    elseif s_sfx_state == 1 then
      host:setActionbar("Static SFX: ON FOR OTHERS")
      act_static_sfx:setTitle("Static SFX: ON FOR OTHERS"):setItem("minecraft:gray_wool")
    elseif s_sfx_state == 2 then
      host:setActionbar("Static SFX: OFF")
      act_static_sfx:setTitle("Static SFX: OFF"):setItem("minecraft:black_wool")
    end
end

act_static_sfx = mainPage:newAction()
  :setColor(0.5,0.5,0.5)
  :setHoverColor(1,1,1)
  :setTitle("Static SFX: ON")
  :setItem("minecraft:white_wool")
  :setOnLeftClick(act_staticSfx_click)


local blahajState = 0 -- 0: off. 1: standard. 2: hellshark.

function pings.blahaj(state) --???
  if state == 0 then
    groups.s_model_blahaj:setVisible(false)
    groups.s_static_blahaj:setVisible(false)
    groups.b_blahaj1:setVisible(false)
    groups.b_blahaj2:setVisible(false)
    groups.b_static_blahaj1:setVisible(false)
    groups.b_static_blahaj2:setVisible(false)
  else
    groups.s_model_blahaj:setVisible(true)
    groups.s_static_blahaj:setVisible(true)
    groups.b_blahaj1:setVisible(true)
    groups.b_blahaj2:setVisible(true)
    groups.b_static_blahaj1:setVisible(true)
    groups.b_static_blahaj2:setVisible(true)
    if state == 1 then
      groups.s_model_blahaj:setPrimaryTexture("custom", textures.blahaj)
      groups.b_blahaj1:setPrimaryTexture("custom", textures.blahaj)
      groups.b_blahaj2:setPrimaryTexture("custom", textures.blahaj)
      groups.s_model_blahaj:setSecondaryTexture()
      groups.b_blahaj1:setSecondaryTexture()
      groups.b_blahaj2:setSecondaryTexture()
    else
      groups.s_model_blahaj:setPrimaryTexture("custom", textures.hellshark)
      groups.b_blahaj1:setPrimaryTexture("custom", textures.hellshark)
      groups.b_blahaj2:setPrimaryTexture("custom", textures.hellshark)
      groups.s_model_blahaj:setSecondaryTexture("custom", textures.hellshark)
      groups.b_blahaj1:setSecondaryTexture("custom", textures.hellshark)
      groups.b_blahaj2:setSecondaryTexture("custom", textures.hellshark)
    end
  end
end

function act_blahaj_click()
  blahajState = blahajState + 1
    if blahajState >= 3 then
      blahajState = 0
    end
    pings.blahaj(blahajState)

    act_blahaj:setColor(0.2,0.2,1):setHoverColor(0.5,0.5,1)
    
    if blahajState == 0 then
      host:setActionbar("Blahaj Weapons: OFF")
      act_blahaj:setTitle("Blahaj Weapons: OFF")
      act_blahaj:setItem("minecraft:bucket")
    elseif blahajState == 1 then
      host:setActionbar("Blahaj Weapons: ON")
      act_blahaj:setTitle("Blahaj Weapons: ON")
      act_blahaj:setItem("minecraft:water_bucket")
    elseif blahajState == 2 then
      host:setActionbar("Blahaj Weapons: HELLSHARK")
      act_blahaj:setTitle("Blahaj Weapons: HELLSHARK")
      act_blahaj:setItem("minecraft:lava_bucket")
    end
end

act_blahaj = mainPage:newAction()
  :setHoverColor(0,0,0)
  :setOnLeftClick(act_blahaj_click)


keybinds:newKeybind("Transform","key.keyboard.z"):setOnPress(function ()
  if transformState == 0 then
    pings.transform(isDemon)
  end
end)

keybinds:newKeybind("Static SFX","key.keyboard.x"):setOnPress(function ()
  act_staticSfx_click()
end)

keybinds:newKeybind("?????","key.keyboard.c"):setOnPress(function ()
  act_blahaj_click()
end)



--================================================================  SETUP MODEL  =================================================================

groups.hedPiv.Hat:setPrimaryTexture("custom", textures.Skin) --skin hat layer
groups.Body.Jacket:setPrimaryTexture("custom", textures.Skin)
groups.LeftArm.piv2["Left Sleeve"]:setPrimaryTexture("custom", textures.Skin)
groups.RightArm.piv["Right Sleeve"]:setPrimaryTexture("custom", textures.Skin)
groups.LeftLeg["Left Pants"]:setPrimaryTexture("custom", textures.Skin)
groups.RightLeg["Right Pants"]:setPrimaryTexture("custom", textures.Skin)

groups.s_model:setPrimaryTexture("custom", textures.scythe)
groups.b_model:setPrimaryTexture("custom", textures.bow)
groups.a_model2:setPrimaryTexture("custom", textures.arrow)
groups.s_model:setVisible(false)
groups.b_model:setVisible(false)

--???
groups.s_model_blahaj:setPrimaryTexture("custom", textures.blahaj)
groups.b_blahaj1:setPrimaryTexture("custom", textures.blahaj)
groups.b_blahaj2:setPrimaryTexture("custom", textures.blahaj)

groups.s_model_blahaj:setVisible(false)
groups.s_static_blahaj:setVisible(false)
groups.b_blahaj1:setVisible(false)
groups.b_blahaj2:setVisible(false)
groups.b_static_blahaj1:setVisible(false)
groups.b_static_blahaj2:setVisible(false)



--================================================================  UPDATE UVS WHEN TRANSFORMING  =================================================================
function setSkinState(isDemon) --toggles between demon skin and normal skin
  models.model:setPrimaryTexture("custom", isDemon and textures.demon or textures.Skin) --apply tex
  if isDemon then
    groups.hedPiv.Head:setSecondaryTexture("custom", textures.demon_e) --emissive
  else
  groups.hedPiv.Head:setSecondaryTexture()
  end

  groups.LArm2.LeftArm:setUVPixels(isDemon and vec(16,0) or vec(0,0)) --update UVs
  groups.RArm2.RightArm:setUVPixels(isDemon and vec(0,14) or vec(0,0))
  groups.RArm2.RightArm:setUVPixels(isDemon and vec(0,14) or vec(0,0))
  groups.LeftLeg.LeftLeg:setUVPixels(isDemon and vec(0,-11) or vec(0,0))
  groups.LLeg2.LeftLeg:setUVPixels(isDemon and vec(-16,4) or vec(0,0))
  groups.RLeg2.RightLeg:setUVPixels(isDemon and vec(0,7) or vec(0,0))
  groups.RLeg3.RightLeg:setUVPixels(isDemon and vec(0,18) or vec(0,0))

  --animations.model.pose_transformed:setPlaying(isDemon)
end


--================================================================  STATIC EFFECT FOR DEMON  =================================================================
staticGroups = {} --static overlay effect
for i = 1, 15, 1 do --autofill groups
  staticGroups[i] = {}
  staticGroups[i].part = groups["static"..i]
  groups["static"..i]:setPrimaryTexture("custom", textures.static_in_1)
end

--values for static overlay anim.
--inTime: time to start moving UV of part when transforming
--outTime: time to start moving UV when transforming back
--size:  total distance to move UV

--loosely ordered from lowest group to highest

staticGroups[10].size = 14 --left leg, bottom part
staticGroups[10].inTime = 0
staticGroups[10].outTime = 0

staticGroups[13].size = 14 --right leg, bottom part
staticGroups[13].inTime = 0
staticGroups[13].outTime = 0

staticGroups[9].size = 14 --right leg, middle part
staticGroups[9].inTime = 5
staticGroups[9].outTime = 4

staticGroups[12].size = 14 --left leg, middle part
staticGroups[12].inTime = 5
staticGroups[12].outTime = 4

staticGroups[8].size = 14 --right leg, top part
staticGroups[8].inTime = 5
staticGroups[8].outTime = 10

staticGroups[11].size = 14 --left leg, top part
staticGroups[11].inTime = 5
staticGroups[11].outTime = 10

staticGroups[7].size = 17 --right arm, bottom part
staticGroups[7].inTime = 12
staticGroups[7].outTime = 13

staticGroups[5].size = 17 --left arm, bottom part
staticGroups[5].inTime = 12
staticGroups[5].outTime = 13

staticGroups[6].size = 17 --right arm, top part
staticGroups[6].inTime = 14
staticGroups[6].outTime = 22

staticGroups[4].size = 17 --left arm, top part
staticGroups[4].inTime = 14
staticGroups[4].outTime = 22

staticGroups[15].size = 10 --body, bottom part
staticGroups[15].inTime = 12
staticGroups[15].outTime = 17

staticGroups[14].size = 19 --body, top part
staticGroups[14].inTime = 12
staticGroups[14].outTime = 20

staticGroups[1].size = 19 --head, main part
staticGroups[1].inTime = 22
staticGroups[1].outTime = 28

staticGroups[2].size = 15 --head, demon snout
staticGroups[2].inTime = 22
staticGroups[2].outTime = 24

staticGroups[3].size = 10 --head, demon jaw
staticGroups[3].inTime = 22
staticGroups[3].outTime = 22

function updStatic(dist, isDemon) --apply static overlay. dist: how far across model. isDemon: whether avatar is currently in demon form
  for _, gr in ipairs(staticGroups) do
    local uv = 0
    if isDemon then --different start vals depending on current form
      uv = math.clamp(dist-gr.outTime, 0, gr.size) --UV is dist - start time of group, clamped to between 0 and the group's size val
    else
      uv = math.clamp(dist-gr.inTime, 0, gr.size)
    end
    gr.part:setUVPixels(0,uv)
  end
end

transformStaticVal = 0
transformState = 0 --0: not transforming  1: starting transformation 2: mid-transform 3: ending transformation
isDemon = false

staticState = "in" --which static textures to use. "in": used for when static is being added, and for no static. "out": used when static is being removed
curStaticTex = 1 --which variant is in use this frame. Used for repetition protection




--================================================================  STATIC EFFECT FOR SCYTHE  =================================================================
--scythe
scytheStaticG = {} --static overlay effect for scythe weapon
for i = 1, 8, 1 do --autofill groups
  scytheStaticG[i] = {}
  scytheStaticG[i].part = groups["s_static"..i]
  groups["s_static"..i]:setPrimaryTexture("custom", textures.static_in_1)

end

function updScytheStatic(dist) --apply static overlay. dist: how far across model. isDemon: whether avatar is currently in demon form
  for _, gr in ipairs(scytheStaticG) do
     local uv = math.clamp(dist-gr.inTime, 0, gr.size)
    gr.part:setUVPixels(0,uv)
  end
end

--values for static overlay anim.
--inTime: time to start moving UV of part when transforming
--size:  total distance to move UV

--loosely ordered from lowest group to highest

scytheStaticG[1].size = 26 --handle, lower
scytheStaticG[1].inTime = 0

scytheStaticG[2].size = 26 --handle, upper (its split into 2 so as to fit in the static texture)
scytheStaticG[2].inTime = 22

scytheStaticG[4].size = 26 --blade, base
scytheStaticG[4].inTime = 35

scytheStaticG[3].size = 26 --blade, tip (also split in 2 to fit in static texture)
scytheStaticG[3].inTime = 47

scytheStaticG[5].size = 20 --???, 1
scytheStaticG[5].inTime = 35

scytheStaticG[6].size = 20 --???, 2
scytheStaticG[6].inTime = 40

scytheStaticG[7].size = 20 --???, 3
scytheStaticG[7].inTime = 45

scytheStaticG[8].size = 20 --???, 4
scytheStaticG[8].inTime = 50

scytheState = 0 --0: no change. 1: static in. 2: static out
isScytheActive = false

scytheStaticVal = 0
scytheStaticState = "in"

isHoldingSword = false --is player holding sword
scytheAtkTick = 0 --time since last attack. used for attack chaining
scytheAtkState = 0 --chain counter
scytheIdleTick = 45 --time holding scythe and not moving. Used for idle pose


--================================================================  STATIC EFFECT FOR BOW  =================================================================
--bow
bowStaticG = {} --static overlay effect for bow weapon
for i = 1, 9, 1 do --autofill groups
  bowStaticG[i] = {}
  bowStaticG[i].part = groups["b_static"..i]
  groups["b_static"..i]:setPrimaryTexture("custom", textures.static_in_1)
end

function updBowStatic(dist) --apply static overlay. dist: how far across model. isDemon: whether avatar is currently in demon form
  for _, gr in ipairs(bowStaticG) do
     local uv = math.clamp(dist-gr.inTime, 0, gr.size)
    gr.part:setUVPixels(0,uv)
  end
end

--values for static overlay anim.
--inTime: time to start moving UV of part when transforming
--size:  total distance to move UV

--loosely ordered from lowest group to highest

bowStaticG[4].size = 16 --handle, lower
bowStaticG[4].inTime = 0

bowStaticG[5].size = 16 --string, lower
bowStaticG[5].inTime = 0

bowStaticG[1].size = 12 --handle, middle
bowStaticG[1].inTime = 12

bowStaticG[2].size = 16 --handle, upper
bowStaticG[2].inTime = 19

bowStaticG[3].size = 16 --string, upper
bowStaticG[3].inTime = 13

bowStaticG[6].size = 21 --arrow, projectile
bowStaticG[6].inTime = 10

bowStaticG[7].size = 21 --arrow, draw anim
bowStaticG[7].inTime = 10

bowStaticG[8].size = 20 --???, lower
bowStaticG[8].inTime = 0

bowStaticG[9].size = 20 --???, upper
bowStaticG[9].inTime = 15

bowState = 0 --0: no change. 1: static in. 2: static out
isBowActive = false

bowStaticVal = 0
bowStaticState = "in"

isHoldingBow = false --is player holding bow



--================================================================  MISC INIT  =================================================================


local demonHP = maxHP
local _health = 0 --damage detection, init
function events.entity_init() 
  _health=player:getHealth()
end
crackState = 0
--hide vanilla model
vanilla_model.PLAYER:setVisible(false)




--#################################################################  TICK  #######################################################################

function events.tick()
  --code goes here


  --================================================================  DEMON TRANSFORM STATIC/ANIMATION  =================================================================

  if transformState == 1 then --transforming
    transformStaticVal = transformStaticVal+2 --increase static
    updStatic(transformStaticVal, isDemon) --apply static
    if transformStaticVal >= (isDemon and 42 or 36) then --when fully static: transform
      isDemon = not isDemon --state bool flip
      crackState = 0
      animations.model.transform_human:setPlaying(not isDemon) --transformation anims
      animations.model.transform_demon:setPlaying(isDemon)
      setSkinState(isDemon) --update texture
      transformStaticVal = 0
      staticState = "out"
      updStatic(transformStaticVal, isDemon) --apply static
      transformState = 2 --increment state
    end
  elseif transformState == 3 then --transform state 3 is reached when transform anim finishes
    transformStaticVal = transformStaticVal+2 --decrease static
    updStatic(transformStaticVal, isDemon) --apply static
    if transformStaticVal >= (isDemon and 42 or 36) then
      transformStaticVal = 0
      staticState = "in"
      updStatic(transformStaticVal, isDemon) --apply static
      transformState = 0 --transformation finished
      demonHP = maxHP
    end
  end

  --================================================================  DAMAGE CRACK EFFECT  =================================================================


  local health = player:getHealth() --damage detection
  if health < _health and isDemon and transformState == 0 then
    demonHP = demonHP - 1
    local stateHP = maxHP/4
    crackState = 4 - math.ceil(demonHP/stateHP)
    if crackState ~= 4 - math.ceil((demonHP+1)/stateHP) then
      
      if crackState == 4 then
        sounds:playSound("entity.zombie_villager.cure", player:getPos(), 1, 1)
      else
      sounds:playSound("entity.zombie_villager.cure", player:getPos(), 1, 3)
      end
    end
    if crackState == 4 then --demon form fully destabilized, instant transform back
      isDemon = false
      transformStaticVal = 0
      staticState = "out"
      setSkinState(false) --update texture
      animations.model.transform_human:setPlaying(true) --transformation anims
      animations.model.transform_demon:setPlaying(false)
      transformState = 2 --increment state
      crackState = 0
    elseif crackState ~= 0 then
      staticState = "crack"..crackState
    end
  end
  _health = health


  --================================================================  SCYTHE SUMMON STATIC  =================================================================

  --scythe static anim
  if scytheState == 1 then --static in
    scytheStaticVal = scytheStaticVal+4 --increase static
    updScytheStatic(scytheStaticVal)
    if scytheStaticVal >= 62 then --when fully static: summon/dismiss
      isScytheActive = not isScytheActive
      groups["s_model"]:setVisible(isScytheActive)
      scytheStaticVal = 0
      scytheStaticState = "out"
      updScytheStatic(scytheStaticVal) --apply static
      scytheState = 2 --increment state
    end
  elseif scytheState == 2 then --static out
    scytheStaticVal = scytheStaticVal+4 --decrease static
    updScytheStatic(scytheStaticVal) --apply static
    if scytheStaticVal >= 62 then
      scytheStaticVal = 0
      scytheStaticState = "in"
      updScytheStatic(scytheStaticVal) --apply static
      scytheState = 0 --transformation finished
    end
  end

  --================================================================  BOW SUMMON STATIC  =================================================================
  --bow static anim
  if bowState == 1 then --static in
    bowStaticVal = bowStaticVal+3 --increase static
    updBowStatic(bowStaticVal)
    if bowStaticVal >= 30 then --when fully static: summon/dismiss
      isBowActive = not isBowActive
      groups["b_model"]:setVisible(isBowActive)
      groups["a_model"]:setVisible(isBowActive)
      groups["a_model_vanilla"]:setVisible(not isBowActive)
      bowStaticVal = 0
      bowStaticState = "out"
      updBowStatic(bowStaticVal) --apply static
      bowState = 2 --increment state
    end
  elseif bowState == 2 then --static out
    bowStaticVal = bowStaticVal+3 --decrease static
    updBowStatic(bowStaticVal) --apply static
    if bowStaticVal >= 30 then
      bowStaticVal = 0
      bowStaticState = "in"
      updBowStatic(bowStaticVal) --apply static
      bowState = 0 --transformation finished
    end
  end



  --================================================================  STATIC TEXTURE ANIMATION  =================================================================

  --animated static
  local newStaticTex = math.random(3)
  if newStaticTex >= curStaticTex then --choose new static frame, with repetition protection
    newStaticTex = newStaticTex + 1
  end
  curStaticTex = newStaticTex

  for _, part in ipairs(staticGroups) do --apply static frame
    part.part:setPrimaryTexture("custom", textures["static_" .. staticState .. "_" .. curStaticTex])
  end

  for _, part in ipairs(scytheStaticG) do --apply static frame
    part.part:setPrimaryTexture("custom", textures["static_" .. scytheStaticState .. "_" .. curStaticTex])
  end

  for _, part in ipairs(bowStaticG) do --apply static frame
    part.part:setPrimaryTexture("custom", textures["static_" .. bowStaticState .. "_" .. curStaticTex])
  end

  --log(transformStaticVal)

  --================================================================  SCYTHE ANIMATION CONTROL  =================================================================

  --summon/dismiss
  local _isHoldingSword = (string.find(player:getHeldItem():getID(), "sword", 1, true) ~= nil) and isDemon and (player:getActiveItem():getID() == "minecraft:air") --true when transformed and holding sword, and not using an item
  if _isHoldingSword ~= isHoldingSword then --on variable change:
    isHoldingSword = _isHoldingSword
    if isHoldingSword then --draw scythe
      scytheState = 1 --start transform anim
      isScytheActive = false
      animations.model.scythe_summon:play()
      scytheAtkTick = 30 --set initial values
      scytheAtkState = 0
      scytheIdleTick = 45
    else
      scytheState = 1 --put away scythe
      isScytheActive = true
      animations.model.scythe_summon:stop() --stop all scythe anims
      animations.model.scythe_hold:stop()
      animations.model.scythe_idle:stop()
      animations.model.scythe_attack_1:stop()
      animations.model.scythe_attack_2:stop()
      animations.model.scythe_attack_3:stop()
      scytheIdleTick = 45
    end
  end
  --attack
  if isHoldingSword and isScytheActive == true then --has scythe
    if (12 < vanilla_model.RIGHT_ARM:getOriginRot().y or vanilla_model.RIGHT_ARM:getOriginRot().y < -12) and player:isSwingingArm() then --swinging arm
      animations.model.scythe_summon:stop()
      animations.model.scythe_hold:stop() --stop idle anims
      animations.model.scythe_idle:stop()
      
      if scytheAtkTick < 10 then --too early
        
      elseif scytheAtkTick < 25 then --chain off last swing
        sounds:playSound("entity.player.attack.sweep", player:getPos(), 1, 0.4)
        scytheAtkState = scytheAtkState + 1
        animations.model.scythe_attack_1:stop()
        animations.model.scythe_attack_3:setPlaying(scytheAtkState%2==0)
        animations.model.scythe_attack_2:setPlaying(scytheAtkState%2==1)
        scytheAtkTick = 0
      else --chain ended, start new chain
        sounds:playSound("entity.player.attack.sweep", player:getPos(), 1, 0.4)
        scytheAtkState = 0
        animations.model.scythe_attack_1:stop()
        animations.model.scythe_attack_2:stop()
        animations.model.scythe_attack_3:stop()
        animations.model.scythe_attack_1:play()
        scytheAtkTick = 0
      end
      scytheIdleTick = 0 --attacking = not idling
    end


    --movement check
    if player:getVelocity():length() > 0.05 and scytheAtkTick > 25 then --moving = not idling
      if scytheIdleTick >= 30 then
        animations.model.scythe_hold:play() --play holding anim if needed
      end
      animations.model.scythe_summon:stop() --stop idle anims
      animations.model.scythe_idle:stop()
      scytheIdleTick = 0
    end
    --tickers
    if scytheAtkTick < 30 then
      scytheAtkTick = scytheAtkTick + 1 --attack timer
    end

    if scytheIdleTick < 45 then
      scytheIdleTick = scytheIdleTick + 1 --idle timer
    end
    --idle
    if scytheIdleTick >= 30 and not animations.model.scythe_summon:isPlaying() then --idling
      animations.model.scythe_attack_1:stop()
      animations.model.scythe_attack_2:stop()
      animations.model.scythe_attack_3:stop() --stop non-idle scythe anims, play idle
      animations.model.scythe_hold:stop()
      animations.model.scythe_idle:play()
    end

  end

  --================================================================  BOW ANIMATION CONTROL  =================================================================


  
  --summon/dismiss
  local _isHoldingBow = (player:getHeldItem(true):getID() == "minecraft:bow") and isDemon and scytheIdleTick >= 45 --true when transformed and holding bow in offhand.
  if isHoldingBow ~= _isHoldingBow then --on variable change:
    isHoldingBow = _isHoldingBow
    if isHoldingBow then --draw bow
      bowState = 1 --start transform anim
      isBowActive = false
      animations.model.bow_summon:play()
    else
      bowState = 1  --put away bow
      isBowActive = true
      animations.model.bow_summon:stop()
      animations.model.bow_idle:stop()
    end
  end
  --using bow
  isUsingBow = (player:getActiveItem():getID() == "minecraft:bow") and isHoldingBow -- using a bow, while bow is active
  animations.model.bow_use:setPlaying(isUsingBow)
  if isUsingBow then 
    animations.model.bow_summon:stop() --swaps summon anim for idle (which is the hold frame of summon)
    animations.model.bow_idle:play()
  end

  groups["a_model2"]:setVisible(isUsingBow)
  groups["b_static7"]:setVisible(isUsingBow)


    --================================================================  VANILLA HELD ITEM CONTROL  =================================================================


  vanilla_model.LEFT_ITEM:setVisible( --is hidden when holding the bow, and when the scythe is held in both hands
    not isDemon or
    not isHoldingBow and
    (scytheIdleTick <= 0 or scytheIdleTick >= 45)
  )

  vanilla_model.RIGHT_ITEM:setVisible( --hidden when holding the scythe or using the bow
    not isDemon or
    not isHoldingSword and
    not isUsingBow
  )

    --================================================================  STATIC SFX  =================================================================

  --ambient static sounds
  local staticVol = math.max(
    crackState*0.1, --crack state
    (transformState == 3 or transformState == 2) and 1-transformStaticVal/35 or transformStaticVal/35, --demon transformation
    scytheState == 2 and 1-scytheStaticVal/60 or scytheStaticVal/60, --scythe summon/dismiss
    bowState == 2 and 1-bowStaticVal/30 or bowStaticVal/30 --bow summon/dismiss
  )
  if staticVol > 0 and s_sfx_on then
    sounds:playSound("entity.sniffer.sniffing", player:getPos(), staticVol, 2.3) --(yes, the ambient static sfx is a sniffer sound playing every tick.)
  end
  



end


--================================================================  SKULL POSITION FIX  =================================================================

--render event, called every time your avatar is rendered
function events.render(delta, context)
  --code goes here
  groups.hedPiv:setPos()
end

function events.skull_render(delta, context)
  --code goes here
  groups.hedPiv:setPos(-groups.hedPiv:getAnimPos()) --stop head position offset from demon form animation applying to skulls
end


--================================================================  MULTIPLAYER SYNC  =================================================================

--snippet was originally made prior to contest start
local list = {}
local send_update_ping = true
local update_ping = function()
  pings.blahaj(blahajState)
  pings.transform(not isDemon)
  pings.staticsfx(s_sfx_state)
 end
events.TICK:register(function()
    if host:isHost() then
        
        for player_name in pairs(world.getPlayers()) do
            if not list[player_name] then
              send_update_ping = true
            end
            list[player_name] = 2
          end
          for i, v in pairs(list) do
            list[i] = v - 1
            if v < 0 then list[i] = nil end
          end
          if send_update_ping  and (world.getTime()) % 4 == 0 then
            send_update_ping = false
            update_ping()
          end
    end
  
end)


