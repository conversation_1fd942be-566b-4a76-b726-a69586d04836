-- Auto generated script file --

--renderer:setPostEffect("bumpy")

animations.plane.idle:play()
models.plane.World:setScale(3,3,3)
models.plane.World:setVisible(false)
models.bomb.World:setVisible(false)
models.sphere.GUI:setOpacity(0)

local corutines = {}
local isSequencePlaying = false
local isBombFalling = false
local anchorPos = vec(0,0,0)
local plantSound = sounds["plant"]
plantSound:loop(false)

local function startCorutine(func,delay) table.insert(corutines,{t= delay + world.getTime(),n=func}) end


local Page = action_wheel:newPage()
action_wheel:setPage(Page)

function events.entity_init()
    anchorPos = player:getPos()


    
    

    models.bomb.World:setPos((player:getPos() + vec(50,15,0)) * 16)
end

events.tick:register(function()
    for i, t in pairs(corutines) do
      if world.getTime() >= t.t then
      t.n()
      corutines[i] = nil
      end end  
  end)

models.indev:setPrimaryRenderType("EMISSIVE")
local opacity = 1

function pings.nuke(pos)
    host:sendChatCommand("gamerule sendCommandFeedback false")
    host:sendChatCommand("/summon minecraft:fireball " .. pos.x .. " " .. pos.y .. " " .. pos.z .. " {ExplosionPower:127,direction:[0.1,-10.0,0.0],power:[0.0,-100.0,0.0]}")
    
    host:sendChatCommand("/summon minecraft:fireball " .. pos.x -5 .. " " .. pos.y .. " " .. pos.z -5 .. " {ExplosionPower:127,direction:[0.1,-10.0,0.0],power:[0.0,-100.0,0.0]}")
    host:sendChatCommand("/summon minecraft:fireball " .. pos.x -5 .. " " .. pos.y .. " " .. pos.z +5 .. " {ExplosionPower:127,direction:[0.1,-10.0,0.0],power:[0.0,-100.0,0.0]}")
    host:sendChatCommand("/summon minecraft:fireball " .. pos.x +5 .. " " .. pos.y .. " " .. pos.z -5 .. " {ExplosionPower:127,direction:[0.1,-10.0,0.0],power:[0.0,-100.0,0.0]}")
    host:sendChatCommand("/summon minecraft:fireball " .. pos.x +5 .. " " .. pos.y .. " " .. pos.z +5 .. " {ExplosionPower:127,direction:[0.1,-10.0,0.0],power:[0.0,-100.0,0.0]}")
end

function pings.dropbomb()
    models.bomb.World:setPos((anchorPos * vec(1,0,1) + vec(0,200,0)) * 16)
end

local length = 0
local bombHeight = 200
local acceleration = 0

local function HandleBlindness()
    models.sphere.GUI:setOpacity(1)

    for i = 1, 200 do 
        startCorutine(function() models.sphere.GUI:setOpacity(1/i) end, 30 + i)
    end
end
function pings.HandleExplosion()
    models.indev.World:setOpacity(1)
    models.indev.World:setPos((anchorPos + vec(0,-20, 0)) * 16)
    for i = 1, 300 do 
        startCorutine(function() models.indev.World:setScale((i/300) * 16,(i/300) * 16, (i /300) * 16) end, i)
    end
    for i = 1, 200 do 
        startCorutine(function() models.indev.World:setOpacity(1/i) end, 400 + i)
    end
end

function events.tick()
    
    if(isSequencePlaying) then
    models.plane.World:setPos((anchorPos * vec(1,0,1) + vec(0,200,-length)) * 16)
    length = length + 1
    if(length == 0) then isBombFalling = true models.bomb.World:setVisible(true) models.bomb.World:setPos((anchorPos * vec(1,0,1) + vec(0,200,0)) * 16) end
    if(length == 300) then models.plane.World:setVisible(false) isSequencePlaying = false end
    
    if(isBombFalling) then
    if(bombHeight > anchorPos.y) then bombHeight = bombHeight - acceleration models.bomb.World:setPos((anchorPos * vec(1,0,1) + vec(0,bombHeight,0)) * 16)
    else pings.HandleExplosion() pings.nuke(anchorPos) HandleBlindness() models.bomb.World:setVisible(false) models.computer.World:setVisible(false) isBombFalling = false end
    acceleration = acceleration + 0.04
    
    
end
end

end


--startCorutine(function() host:sendChatCommand("say fuck") end,600)


function pings.allthestuff()
    if(not isSequencePlaying) then
    plantSound:play()
    acceleration = 0
    bombHeight = 200
    models.plane.World:setVisible(true)
    models.computer.World:setVisible(true)
    isSequencePlaying = true
    length = -300

    anchorPos = player:getPos()

    --models.plane.World:setPos((player:getPos() * vec(1,0,1) + vec(0,200,200)) * 16)
    models.computer.World:setPos(player:getPos()*16 + vec(0,0,0))
    
    animations.computer.spin:play()
    --startCorutine(function() animations.anchor.boot:setPlaying(true) end,100)
    end
end

Page:newAction():title("I Want To Explode"):onLeftClick(pings.allthestuff)