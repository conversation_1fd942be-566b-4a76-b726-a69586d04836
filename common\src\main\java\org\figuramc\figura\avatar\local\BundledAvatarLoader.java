package org.figuramc.figura.avatar.local;

import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.packs.resources.Resource;
import net.minecraft.server.packs.resources.ResourceManager;
import org.figuramc.figura.FiguraMod;
import org.figuramc.figura.avatar.AvatarMetadataParser;
import org.figuramc.figura.gui.widgets.avatar.CardBackground;
import org.figuramc.figura.utils.FiguraIdentifier;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.util.*;

/**
 * Loads avatars that are bundled with the mod from the resources directory
 */
public class BundledAvatarLoader {
    
    private static final String BUNDLED_AVATARS_PATH = "bundled_avatars";
    private static final List<BundledAvatarPath> BUNDLED_AVATARS = new ArrayList<>();
    private static boolean loaded = false;
    
    public static void init() {
        // This will be called during mod initialization
        FiguraMod.debug("Initializing BundledAvatarLoader");
    }
    
    /**
     * Loads all bundled avatars from the mod's resources
     */
    public static void loadBundledAvatars(ResourceManager resourceManager) {
        BUNDLED_AVATARS.clear();
        loaded = false;
        
        FiguraMod.debug("Loading bundled avatars from resources...");
        
        try {
            // Get all resources in the bundled_avatars directory
            Map<ResourceLocation, Resource> avatarResources = resourceManager.listResources(
                BUNDLED_AVATARS_PATH, 
                location -> location.getNamespace().equals(FiguraMod.MOD_ID)
            );
            
            // Group resources by avatar folder
            Map<String, List<ResourceLocation>> avatarFolders = new HashMap<>();
            
            for (ResourceLocation location : avatarResources.keySet()) {
                String path = location.getPath();
                // Remove the bundled_avatars prefix
                String relativePath = path.substring(BUNDLED_AVATARS_PATH.length() + 1);
                
                // Skip if it's just the directory itself or README
                if (relativePath.isEmpty() || relativePath.equals("README.md")) {
                    continue;
                }
                
                // Get the avatar folder name (first part of the path)
                String[] pathParts = relativePath.split("/");
                if (pathParts.length > 0) {
                    String avatarName = pathParts[0];
                    avatarFolders.computeIfAbsent(avatarName, k -> new ArrayList<>()).add(location);
                }
            }
            
            // Create BundledAvatarPath for each avatar folder
            for (Map.Entry<String, List<ResourceLocation>> entry : avatarFolders.entrySet()) {
                String avatarName = entry.getKey();
                List<ResourceLocation> resources = entry.getValue();
                
                // Check if this folder has an avatar.json file
                boolean hasMetadata = resources.stream()
                    .anyMatch(loc -> loc.getPath().endsWith(avatarName + "/avatar.json"));
                
                if (hasMetadata) {
                    BundledAvatarPath avatarPath = new BundledAvatarPath(avatarName, resources, resourceManager);
                    BUNDLED_AVATARS.add(avatarPath);
                    FiguraMod.debug("Found bundled avatar: " + avatarName);
                }
            }
            
            loaded = true;
            FiguraMod.debug("Loaded {} bundled avatars", BUNDLED_AVATARS.size());
            
        } catch (Exception e) {
            FiguraMod.LOGGER.error("Failed to load bundled avatars", e);
        }
    }
    
    /**
     * Gets all loaded bundled avatars
     */
    public static List<BundledAvatarPath> getBundledAvatars() {
        return new ArrayList<>(BUNDLED_AVATARS);
    }
    
    /**
     * Checks if bundled avatars are loaded
     */
    public static boolean isLoaded() {
        return loaded;
    }
    
    /**
     * Represents a bundled avatar that exists in the mod's resources
     */
    public static class BundledAvatarPath extends LocalAvatarFetcher.AvatarPath {
        private final String avatarName;
        private final List<ResourceLocation> resources;
        private final ResourceManager resourceManager;
        private final Map<String, ResourceLocation> resourceMap;
        
        public BundledAvatarPath(String avatarName, List<ResourceLocation> resources, ResourceManager resourceManager) {
            super(null, null); // We don't use filesystem paths for bundled avatars
            
            this.avatarName = avatarName;
            this.resources = resources;
            this.resourceManager = resourceManager;
            this.resourceMap = new HashMap<>();
            
            // Create a map for quick resource lookup
            for (ResourceLocation resource : resources) {
                String path = resource.getPath();
                String relativePath = path.substring(BUNDLED_AVATARS_PATH.length() + 1);
                resourceMap.put(relativePath, resource);
            }
            
            loadMetadata();
        }
        
        private void loadMetadata() {
            try {
                // Load metadata from avatar.json
                ResourceLocation metadataLocation = resourceMap.get(avatarName + "/avatar.json");
                if (metadataLocation != null) {
                    Resource metadataResource = resourceManager.getResource(metadataLocation).orElse(null);
                    if (metadataResource != null) {
                        try (InputStream stream = metadataResource.open()) {
                            String metadataJson = new String(stream.readAllBytes(), StandardCharsets.UTF_8);
                            AvatarMetadataParser.Metadata metadata = AvatarMetadataParser.read(metadataJson);
                            
                            this.name = metadata.name != null && !metadata.name.isBlank() ? metadata.name : avatarName;
                            this.description = metadata.description != null ? metadata.description : "";
                            this.background = CardBackground.parse(metadata.background);
                        }
                    }
                }
                
                // Set default values if metadata loading failed
                if (this.name == null) {
                    this.name = avatarName;
                }
                if (this.description == null) {
                    this.description = "Bundled Avatar";
                }
                
            } catch (Exception e) {
                FiguraMod.LOGGER.error("Failed to load metadata for bundled avatar: " + avatarName, e);
                this.name = avatarName;
                this.description = "Bundled Avatar";
            }
        }
        
        @Override
        public Path getTheActualPathForThis() {
            // Return a virtual path for bundled avatars
            return Paths.get("bundled://" + avatarName);
        }
        
        @Override
        public boolean search(String query) {
            if (query == null || query.isEmpty()) {
                return true;
            }
            
            String lowerQuery = query.toLowerCase();
            return name.toLowerCase().contains(lowerQuery) || 
                   description.toLowerCase().contains(lowerQuery) ||
                   avatarName.toLowerCase().contains(lowerQuery);
        }
        
        /**
         * Gets a resource from this bundled avatar
         */
        public InputStream getResource(String relativePath) throws IOException {
            ResourceLocation location = resourceMap.get(avatarName + "/" + relativePath);
            if (location != null) {
                Resource resource = resourceManager.getResource(location).orElse(null);
                if (resource != null) {
                    return resource.open();
                }
            }
            return null;
        }
        
        /**
         * Checks if a resource exists in this bundled avatar
         */
        public boolean hasResource(String relativePath) {
            return resourceMap.containsKey(avatarName + "/" + relativePath);
        }
        
        /**
         * Gets all resource paths for this bundled avatar
         */
        public Set<String> getResourcePaths() {
            Set<String> paths = new HashSet<>();
            for (String path : resourceMap.keySet()) {
                if (path.startsWith(avatarName + "/")) {
                    paths.add(path.substring(avatarName.length() + 1));
                }
            }
            return paths;
        }
        
        public String getAvatarName() {
            return avatarName;
        }
        
        public ResourceManager getResourceManager() {
            return resourceManager;
        }
    }
}
