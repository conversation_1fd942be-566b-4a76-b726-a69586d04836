package org.figuramc.figura.avatar.local;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Simple test to verify BundledAvatarLoader compiles correctly
 */
public class BundledAvatarLoaderTest {
    
    @Test
    public void testBundledAvatarLoaderExists() {
        // This test just verifies that the class exists and can be instantiated
        assertDoesNotThrow(() -> {
            BundledAvatarLoader.init();
        });
    }
    
    @Test
    public void testBundledAvatarLoaderMethods() {
        // Test that the methods exist and return expected types
        assertNotNull(BundledAvatarLoader.getBundledAvatars());
        assertFalse(BundledAvatarLoader.isLoaded()); // Should be false initially
    }
}
