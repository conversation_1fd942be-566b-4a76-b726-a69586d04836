# Bundled Avatars Directory

This directory is for pre-including avatars with the Figura mod distribution.

## How to Add Avatars

1. **Avatar Structure**: Each avatar should be in its own folder with the following structure:
   ```
   avatar_name/
   ├── avatar.json     (metadata file)
   ├── avatar.png      (optional icon)
   ├── script.lua      (optional main script)
   ├── models/         (optional models folder)
   │   └── model.bbmodel
   └── textures/       (optional textures folder)
       └── texture.png
   ```

2. **Required Files**:
   - `avatar.json` - Contains avatar metadata (name, description, etc.)
   - At least one model file or script file

3. **Example avatar.json**:
   ```json
   {
     "name": "Example Avatar",
     "description": "A sample bundled avatar",
     "authors": ["Author Name"],
     "version": "1.0.0"
   }
   ```

## Avatar Loading

Currently, these bundled avatars are included in the mod's resources but are not automatically loaded into the user's avatar directory. 

To implement automatic loading, you would need to:
1. Modify `LocalAvatarFetcher.java` to check for bundled avatars
2. Copy bundled avatars to the user's local avatar directory on first run
3. Add configuration options for enabling/disabling bundled avatars

## Notes

- Avatars placed here will be included in the mod JAR file
- Consider file size when adding multiple avatars
- Ensure all bundled avatars follow Figura's avatar format specifications
- Test avatars thoroughly before including them in the distribution
