# Bundled Avatars Directory

This directory contains pre-included avatars that are bundled with the Figura mod distribution.

## Current Status: ✅ ACTIVE

The mod has been modified to automatically load avatars from this directory instead of requiring external avatar folders.

## Available Avatars

The following avatars are currently bundled with the mod:
- Avatar Contest
- <PERSON><PERSON>
- Demon of Static
- F14 Tomcat
- FBI_ATOMIC
- GOJO
- Icarus Angel Model
- Ka<PERSON>'tsit
- <PERSON><PERSON><PERSON> Eyes
- Sadko
- Selfie Cam v2
- my work

## How It Works

1. **Automatic Loading**: The mod automatically discovers and loads all avatars from this directory
2. **Resource-Based**: Avatars are loaded directly from the mod's JAR resources
3. **No External Dependencies**: No need for `.minecraft/figura/avatars/` folder
4. **Integrated UI**: Bundled avatars appear in the normal avatar selection interface

## Avatar Structure

Each avatar folder contains:
- `avatar.json` - Required metadata file
- `avatar.png` - Optional icon
- `script.lua` - Optional main script
- Model files (`.bbmodel`)
- Texture files (`.png`)
- Sound files (`.ogg`)
- Additional Lua scripts

## Technical Implementation

The mod uses a new `BundledAvatarLoader` system that:
- Scans the bundled_avatars resource directory
- Creates virtual avatar paths for each bundled avatar
- Integrates with the existing avatar loading system
- Provides the same functionality as external avatars
