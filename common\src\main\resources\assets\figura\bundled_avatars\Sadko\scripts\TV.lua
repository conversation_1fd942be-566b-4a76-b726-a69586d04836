	-- 6/14/2024
	-- Created by <PERSON><PERSON><PERSON>!
	
	-- "I'd likely continue working on this if it didn't try to implode my brain each time I tried to add more bricks to the script."
	-- "I've said this on another model before but scripting demolishes any drive to keep developing it. Something about it is just like oil and water to my mental state DX"
	-- "This did help me learn a bit more about scripting--
	-- "Anyways, enough blathering from me."
	-- "Thanks for taking time to check out this avatar!"
	


	-- Model Roots
	
local Okiku = models.Okiku
local TV 	= Okiku.TV
local Ring 	= Okiku.Ring
local ATV 	= animations.Okiku
local VM	= vanilla_model


	-- Initialization
	
VM.PLAYER:setVisible(true)
VM.ARMOR:setVisible(true)
VM.ELYTRA:setVisible(true)
VM.HELD_ITEMS:setVisible(true)
TV:setVisible(false)
Ring:setVisible(false)
Ring.Noggle.RANGO:setVisible(false)

TV:setParentType("World")
Ring:setParentType("World")
TV.Housing.Screen:secondaryRenderType(context == "EMISSIVE" or "EYES")
Ring.Noggle.Neck.NogginBits.Eyes:secondaryRenderType(context == "EMISSIVE" or "EYES")



	-- TV/Ring Coord Setting
	
local anchorPos = vectors.vec3()
local anchorYaw = 0
local RanchorPos = vectors.vec3()
local RanchorYaw = 0

function setTVPos()

	TV:setVisible(true)
	Ring:setVisible(false)
	anchorPos = player:getPos() * 16
	anchorYaw = -player:getBodyYaw() + 180
	RanchorPos = player:getPos() * 16
	RanchorYaw = -player:getBodyYaw() + 180
	sounds:playSound("block.glass.place", player:getPos(), 0.5, .6)
	
end
pings.setTVPos = setTVPos


	-- ActionWheel Dialogue Part 2

local RING 	= action_wheel:newPage("TheRingPage")
	
	RING:newAction()
		:title("You've got free roam as the Ring.")
		:item("lectern")
	RING:newAction()
		:title("Have fun!")
		:item("sculk_catalyst")



	-- Ring / Animation Setup
function Control()


	-- Initial Swap
	
	Ring.Noggle.RANGO:setVisible(true)
	sounds:playSound("entity.zombie_villager.converted", player:getPos(), 0.5, .8, false)
	VM.PLAYER:setVisible(false)
	VM.ARMOR:setVisible(false)
	VM.ELYTRA:setVisible(false)
	VM.HELD_ITEMS:setVisible(false)
	ATV.Freedom:play()
	
	
	-- Squishy Bits
	
	local squapi = require("lib.SquAPI")
	local Noggin = Ring.Noggle.Neck.NogginBits
	squapi.smoothHeadNeck(Noggin, Ring.Noggle.Neck, false)
	squapi.eye(Noggin.Eyes.RightEye,1,.25,.45,.45)
	squapi.eye(Noggin.Eyes.LeftEye,.25,1,.45,.45)
	
	-- Jimmy Bits
	
	local anims = require("lib.JimmyAnims")
	anims.blendTime = 6
	anims(ATV)
	action_wheel:setPage(RING)


	-- GS AnimBlending
	
require("lib.GSAnimBlend")

local blendAnims = {

	--TV Blending
    { anim = ATV.AntennaUp,  ticks = 5 },
    { anim = ATV.AntennaDown,  ticks = 5 },
    { anim = ATV.ShakeyShakey,  ticks = 5 },
    { anim = ATV.Uppies,  ticks = 5 },

	-- Ring Blending
    { anim = ATV.idle,  ticks = 8 },
    { anim = ATV.walk,  ticks = 8 },
    { anim = ATV.walkback,  ticks = 8 },
    { anim = ATV.jumpup,  ticks = 5 },
    { anim = ATV.jumpdown,  ticks = 8 },
    { anim = ATV.fall,  ticks = 10 },
    { anim = ATV.sprint,  ticks = 10 },
    { anim = ATV.crouch,  ticks = 10 },
    { anim = ATV.crouchwalk,  ticks = 10 },
    { anim = ATV.crouchwalkback,  ticks = 10 },
    { anim = ATV.water,  ticks = 10 },
    { anim = ATV.fly,  ticks = 10 },
    { anim = ATV.attackR,  ticks = 8 },
    { anim = ATV.mineR,  ticks = 5 },
    { anim = ATV.useR,  ticks = 5 },
}

	for _, blend in ipairs(blendAnims) do
		blend.anim:blendTime(blend.ticks):onBlend("easeOutQuad")
	end
	
end
pings.Control = Control


	-- Setting TV/Ring Location
	
function events.RENDER(delta, context)

	local TVAnimPos = TV:getAnimPos()
	local TVmodPos	= vectors.rotateAroundAxis(anchorYaw, TVAnimPos, vec(0, 1, 0))
	local RingAnimPos = Ring:getAnimPos()
	local RingmodPos	= vectors.rotateAroundAxis(RanchorYaw, RingAnimPos, vec(0, 1, 0))

	TV:pos(anchorPos + TVmodPos - TVAnimPos)
		:rot(0, anchorYaw, 0)

	if Ring.Noggle.RANGO:getVisible(true) then
	
		Ring:pos(0, -10.5, -0.75):rot(0, 0, 0)
		Ring:setParentType("None")
		
	else
		Ring:pos(RanchorPos + RingmodPos - RingAnimPos)
		:rot(0, RanchorYaw, 0)
	end

end

	--Various Pings

	-- ITS ABOUT THE METS BABY, GET A HOME RUN, GO METS-
function pings.WIFI(METS)

	if ATV.AntennaUp:isPlaying(true) then
		TV.Housing.Screen.METS:setVisible(METS)
		ATV.Flicker:setPlaying(not METS)
		ATV.THEMETS:setPlaying(METS)
	end
	
end

	-- Turns the TV off
function pings.TVoff()

	ATV.AntennaDown:play()
	ATV.Flicker:stop()
	ATV.AntennaUp:stop()
	TV.Housing.Screen.METS:setVisible(false)
	
end	

	-- Turns the TV on
function pings.TVon()

	ATV.AntennaDown:stop()
	ATV.Flicker:play()
	ATV.AntennaUp:play()
	
end

	-- Shows Ring
function pings.Okiku()

	ATV.Uppies:play()
	
end



	-- ActionWheel Dialogue

local START = action_wheel:newPage("StartupPage")
local awTV 	= action_wheel:newPage("AlmostTVPage")
local AWTV 	= action_wheel:newPage("TVPage")
local ALM 	= action_wheel:newPage("AlmostRingPage")
local BAR 	= action_wheel:newPage("BarelyRingPage")


if host:isHost() then
	action_wheel:setPage(START)
	
	--===== Page 1 =====
	
	START:newAction()
		:title("Now where'd the remote go..")
		:item("jigsaw")
		:onToggle(pings.setTVPos)
		.leftClick = function()
			sounds:playSound("block.anvil.land", player:getPos(), 0.5, .6)
			TV.Housing.Screen.METS:setVisible(false)
			TV.Housing.Screen.ScreenWell:setVisible(false)
			action_wheel:setPage(awTV)
		end
	START:newAction()
		:title("Not much to see around these parts...\nGood thing you brought your 70lb box tv along!")
		:item("lectern")

	--===== Page 2 =====
	
	awTV:newAction()
		:title("One might be able to tell that this is not a 4K OLED at your feet.\nBut beyond that, somehow it can operate without any external power.\n\nPerhaps it is a technological quirk of tubular tvs built without the liberty of logic...")
		:item("lectern")
	awTV:newAction()
		:title("'Reposition?'")
		:item("fishing_rod")
		:onLeftClick(pings.setTVPos)
	awTV:newAction()
		:title("What's on today..")
		:item("jigsaw")
		:onToggle(pings.TVon)
		.leftClick = function()
			action_wheel:setPage(AWTV)
		end

	--===== Page 3 =====
	
	AWTV:newAction()
		:title("You know it's not about the money...")
		:item("leather_helmet")
		:toggleTitle("It's ABOUT THE METS BABY")
		:toggleItem("tropical_fish_bucket")
		:onToggle(pings.WIFI)
	AWTV:newAction()
		:title("'Reposition?'")
		:item("fishing_rod")
		:onLeftClick(pings.setTVPos)
	AWTV:newAction()
		:title("Channel surfing is also an option.\nYou've got like 7. Maybe.")
		:item("jigsaw")
		:onLeftClick(pings.TVChan)
		.leftClick = function()
			TV.Housing.Screen.ScreenWell:setVisible(true)
			TV.Housing.Screen.METS:setVisible(false)
			TV.Housing.Screen.Screen1:setVisible(false)
			TV.Housing.Screen.ScreenDark:setVisible(false)
			ATV.Flicker:stop()
			ATV.WellWellWell:play()
			ATV.ShakeyShakey:play()
			action_wheel:setPage(ALM)
		end
	AWTV:newAction()
		:title("Or maybe you aren't feeling cable right now.")
		:item("light")
		:toggleTitle("But you lugged that old heap all the way here...\n\nNot like there's much else to do at the moment anyways.")
		:toggleItem("black_concrete")
		:onToggle(pings.TVoff)
		:onUntoggle(pings.TVon)
	
	--===== Page 4 =====
	
	ALM:newAction()
		:title("Let it in...")
		:item("jigsaw")
		:onToggle(pings.Okiku)
		.leftClick = function()
			action_wheel:setPage(BAR)
		end
	ALM:newAction()
		:title("Or just 1.")
		:item("soul_torch")
	
		--===== Page 5 =====
	
	function events.TICK()
		if ATV.Uppies:getTime() >= 7 and Okiku.RingEyes:getVisible(true) then
			
			Okiku.RingEyes:setVisible(false)
			BAR:newAction()
			:title("Assume Control?")
			:item("sculk")
			:onLeftClick(pings.Control)
			
		end
	end
	
	BAR:newAction()
		:title("Just a moment more..")
		:item("structure_block")
		
end

