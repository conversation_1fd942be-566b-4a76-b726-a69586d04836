package org.figuramc.figura.gui.cards;

import java.util.Locale;

public enum CardBackground {
    DEFAULT,
    CHEESE,
    <PERSON><PERSON>OUDS,
    <PERSON>OKIE,
    RAINBOW,
    INSCRYP<PERSON><PERSON>,
    SPACE,
    FADE;

    public static CardBackground parse(String string) {
        try {
            return CardBackground.valueOf(string.toUpperCase(Locale.US));
        } catch (Exception ignored) {
            return DEFAULT;
        }
    }
}
