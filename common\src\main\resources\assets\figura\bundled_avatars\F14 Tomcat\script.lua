models.model.root.F14.MiniPlayer:setPrimaryTexture("SKIN")
models.model.root.Player:setPrimaryTexture("SKIN")
vanilla_model.ELYTRA:setVisible(false)
vanilla_model.PLAYER:setVisible(false)

FlightSpeed = 2
toggle = 0
local Fly = keybinds:newKeybind("Dash", "key.keyboard.left.control")


local MainPage = action_wheel:newPage()
action_wheel:setPage(MainPage)
local toMain = MainPage:newAction()
    :title("Flight Speed")
    :item("minecraft:firework_rocket")
    :onLeftClick(function()
        FlightSpeed = 2
        print("Flight Speed: Normal")
end)
    :onRightClick(function()
        FlightSpeed = .2
        print("Flight Speed: Fast")
end)
local toMain = MainPage:newAction()
    :title("Flight Mode")
    :item("minecraft:jigsaw")
    :onLeftClick(function()
        toggle = 0
        print("Flight Mode: Normal")
end)
    :onRightClick(function()
        toggle = 1
        print("Flight Mode: Compound")
end)



-----------------------------------------------------------
function events.tick()
    if player:isLoaded(true) then
        models.model.root.WORLD:setScale(5.5)
        models.model.root.WORLD:setPos(player:getPos())
        models.model.root.WORLD:setVisible(true)
        Velocity = player:getVelocity() * toggle
        VX = player:getVelocity().x
        VY = player:getVelocity().y
        VZ = player:getVelocity().z
        VMagnitude = math.sqrt(VX^2 + VY^2 + VZ^2)
        LookV = player:getLookDir().xyz
        Pos = player:getPos()
        WingAngle = math.clamp((VMagnitude * 25), 0, 55)
        models.model.root.F14.LeftWingBase.LeftWing:setRot(0, WingAngle, 0)
        models.model.root.F14.RightWingBase.RightWing:setRot(0, -WingAngle, 0)
        --print(player:getLookDir().xyz, VMagnitude, WingAngle)
        models.model.root.F14.MiniPlayer.MiniHead:setRot(0, (((vanilla_model.HEAD:getOriginRot()+180)%360-180).y), 0)
        animations.model.flight:setPlaying(player:isGliding())
        animations.model.noflight:setPlaying(not player:isGliding())
        vanilla_model.HELD_ITEMS:setVisible(not player:isGliding())
        --print(models.model.root:getOriginPos())
        if player:isGliding(true) then
            particles:newParticle("minecraft:cloud", (models.model.root.F14.RightWingBase.RightEngineSmoke:partToWorldMatrix():apply())):setScale(2)
            particles:newParticle("minecraft:cloud", (models.model.root.F14.LeftWingBase.LeftEngineSmoke:partToWorldMatrix():apply())):setScale(2)
    
            if VMagnitude >= 2 then
                if player:getLookDir().y >= .5 then
                    particles:newParticle("minecraft:cloud", (models.model.root.F14.RightWingBase.RightWing.RightWingVacuum1:partToWorldMatrix():apply()))
                    particles:newParticle("minecraft:cloud", (models.model.root.F14.RightWingBase.RightWing.RightWingVacuum2:partToWorldMatrix():apply()))
                    particles:newParticle("minecraft:cloud", (models.model.root.F14.RightWingBase.RightWing.RightWingVacuum3:partToWorldMatrix():apply()))
                    particles:newParticle("minecraft:cloud", (models.model.root.F14.RightWingBase.RightWing.RightWingVacuum4:partToWorldMatrix():apply()))
                    particles:newParticle("minecraft:cloud", (models.model.root.F14.RightWingBase.RightWing.RightWingVacuum5:partToWorldMatrix():apply()))
                    particles:newParticle("minecraft:cloud", (models.model.root.F14.LeftWingBase.LeftWing.LeftWingVacuum1:partToWorldMatrix():apply()))
                    particles:newParticle("minecraft:cloud", (models.model.root.F14.LeftWingBase.LeftWing.LeftWingVacuum2:partToWorldMatrix():apply()))
                    particles:newParticle("minecraft:cloud", (models.model.root.F14.LeftWingBase.LeftWing.LeftWingVacuum3:partToWorldMatrix():apply()))
                    particles:newParticle("minecraft:cloud", (models.model.root.F14.LeftWingBase.LeftWing.LeftWingVacuum4:partToWorldMatrix():apply()))
                    particles:newParticle("minecraft:cloud", (models.model.root.F14.LeftWingBase.LeftWing.LeftWingVacuum5:partToWorldMatrix():apply()))
                end
            end
            if VMagnitude >= 3 then
                particles:newParticle("minecraft:flame", (Pos + (LookV * -2) + vec(1, .5, 0)))
                particles:newParticle("minecraft:flame", (Pos + (LookV * -2) + vec(-1, .5, 0)))
            end
            if Fly:isPressed() == true then
                particles:newParticle("minecraft:end_rod", (models.model.root.F14.RightWingBase.RightEngineSmoke:partToWorldMatrix():apply())):setScale(1)
                particles:newParticle("minecraft:end_rod", (models.model.root.F14.LeftWingBase.LeftEngineSmoke:partToWorldMatrix():apply())):setScale(1)
                goofy:setVelocity(player:getLookDir().xyz:normalized():add(Velocity):add((player:getLookDir().x/FlightSpeed),(player:getLookDir().y/FlightSpeed),(player:getLookDir().z/FlightSpeed)))
            end
        end
    end
end

