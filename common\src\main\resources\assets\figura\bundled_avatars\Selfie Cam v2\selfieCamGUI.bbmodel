{"meta": {"format_version": "4.5", "model_format": "free", "box_uv": false}, "name": "selfieCamGUI", "model_identifier": "", "visible_box": [1, 1, 0], "variable_placeholders": "", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 16, "height": 16}, "elements": [{"name": "cube", "box_uv": false, "rescale": false, "locked": false, "from": [-25.242865775000013, -49.44585682500006, -1.3782885750000085], "to": [25.16499872499996, 48.00934787500003, 8.70328432499999], "autouv": 0, "color": 2, "origin": [206.79645672500007, -49.44585682500006, 133.04268342499992], "faces": {"north": {"uv": [0, 0, 3, 6], "texture": 0}, "east": {"uv": [0, 6, 0.5, 12], "texture": 0}, "south": {"uv": [3, 0, 6, 6], "texture": 0}, "west": {"uv": [6, 0, 6.5, 6], "texture": 0}, "up": {"uv": [4, 6.5, 1, 6], "texture": 0}, "down": {"uv": [7, 6, 4, 6.5], "texture": 0}}, "type": "cube", "uuid": "e9ab82eb-8acc-8858-deef-9d7ffee8af1f"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "from": [-1.719195675000016, 21.12515347499997, -3.0585507249999977], "to": [20.124212274999984, 42.968561424999976, -1.3782885750000085], "autouv": 0, "color": 0, "origin": [206.79645672500007, -49.44585682500006, 133.04268342499992], "faces": {"north": {"uv": [0.7, 6.699999999999999, 1.3500000000000003, 7.35], "texture": 0}, "east": {"uv": [0.7, 6.699999999999999, 0.7499999999999998, 7.35], "texture": 0}, "south": {"uv": [0.7, 6.699999999999999, 1.3500000000000003, 7.35], "texture": 0}, "west": {"uv": [0.7, 6.699999999999999, 0.7499999999999998, 7.35], "texture": 0}, "up": {"uv": [0.7, 6.699999999999999, 1.3500000000000003, 6.75], "texture": 0}, "down": {"uv": [0.7, 6.699999999999999, 1.3500000000000003, 6.75], "texture": 0}}, "type": "cube", "uuid": "0542a8c8-14d0-37ab-29d5-e27de94b3efc"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "from": [10.042639374999993, 32.88698852499999, -4.738812874999989], "to": [18.443950125000008, 41.288299275000064, -3.0585507249999977], "autouv": 0, "color": 0, "origin": [206.79645672500007, -49.44585682500006, 133.04268342499992], "faces": {"north": {"uv": [0.7, 6.199999999999999, 0.9500000000000008, 6.45], "texture": 0}, "east": {"uv": [1.65, 4.85, 1.6999999999999997, 5.5], "texture": 0}, "south": {"uv": [0.7, 6.699999999999999, 1.3500000000000003, 7.35], "texture": 0}, "west": {"uv": [1.25, 4.550000000000001, 1.2999999999999998, 5.200000000000001], "texture": 0}, "up": {"uv": [1.65, 4.85, 2.3000000000000016, 4.9], "texture": 0}, "down": {"uv": [1.25, 4.550000000000001, 1.9000000000000008, 4.600000000000001], "texture": 0}}, "type": "cube", "uuid": "b3338256-12c9-0054-1355-9d83877d17fe"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "from": [-0.03893352499999736, 22.805415625000013, -4.738812874999989], "to": [8.362377225000031, 31.206726375000017, -3.0585507249999977], "autouv": 0, "color": 0, "origin": [206.79645672500007, -49.44585682500006, 133.04268342499992], "faces": {"north": {"uv": [0.7, 6.199999999999999, 0.9500000000000008, 6.45], "texture": 0}, "east": {"uv": [1.65, 4.85, 1.6999999999999997, 5.5], "texture": 0}, "south": {"uv": [0.7, 6.699999999999999, 1.3500000000000003, 7.35], "texture": 0}, "west": {"uv": [1.25, 4.550000000000001, 1.2999999999999998, 5.200000000000001], "texture": 0}, "up": {"uv": [1.65, 4.85, 2.3000000000000016, 4.9], "texture": 0}, "down": {"uv": [1.25, 4.550000000000001, 1.9000000000000008, 4.600000000000001], "texture": 0}}, "type": "cube", "uuid": "4ca018f3-a51c-3238-9341-b7b77d24ffd8"}], "outliner": [{"name": "phone", "origin": [0, 0, 0], "rotation": [0, -180, 0], "color": 0, "uuid": "6eafee00-f6b9-845e-696a-44f9f53e9eeb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["e9ab82eb-8acc-8858-deef-9d7ffee8af1f", "0542a8c8-14d0-37ab-29d5-e27de94b3efc", "b3338256-12c9-0054-1355-9d83877d17fe", "4ca018f3-a51c-3238-9341-b7b77d24ffd8"]}], "textures": [{"path": "C:\\Users\\<USER>\\AppData\\Roaming\\.minecraft\\figura\\avatars\\Selfie Cam\\textures\\phone.png", "name": "phone.png", "folder": "", "namespace": "", "id": "0", "particle": false, "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "mode": "bitmap", "saved": true, "uuid": "0a657cc2-858e-1b18-88f3-6c6ab5e1b6b3", "relative_path": "../textures/phone.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAZ5JREFUWEftlr1uwjAUhY8jCEE4Is4A9DG6tEuXTn3mTh06tRNv0R+pIchJQYC41b1WkrbqAJKbLPGAAVm5n889Po6amhlBhqo/+Y/PIpPf+/1e5uFwKHM40iiLlVvsYahpMiMoBaWaZxKRAAwGA2w2GykeRRGstZho4xnAzImLCwARePcMsF69YTweCwSPw+EgMDpOUdjMnwKJAARNFwhgjDx7RRzHdfEKYhTFfgFMeuE8wHsiAreDjkeURSay8855sBLb7Rb/ALAQgNvrS4RhiPvHZxA5gEr+amYPeG+BSRdiwrubK+x2Ozw8LesWtOMBBpAO/PTVKntpxwNJuqCmtPvGJiztRzseYAA2n5wEOYnOkwzwpwe0QeE1iDgJCQiCoM4ABmgtB6ZmLgpU0ldeWOfvdfx+j2PvUTzRCZVFDq2NZEAD45Bqe0pYcUo5WF9tUFobUoErbK2/S+bUe0rpOCXg6HZlc28ZfxaAz8vl1MLVOlGgB+hWAW3I15E6t/9yB/Ue6FyBiTbk8zX7XB+0nny/AXuAXoFegc4V+AJhqP0h/+zNMAAAAABJRU5ErkJggg=="}], "animations": [{"uuid": "c9e1cd94-5f8b-e61b-6466-6a5c382f30eb", "name": "getPhone", "loop": "hold", "override": false, "length": 1, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"6eafee00-f6b9-845e-696a-44f9f53e9eeb": {"name": "phone", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "928fcaea-a636-6318-6e7c-21c9ef1994ae", "time": 0.375, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "edfb726c-c4b2-5b95-f7d5-62a3bd7194cf", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": -90}], "uuid": "7a0284d6-a0d8-aabc-6ca9-876c53a5d0ac", "time": 0.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -493, "z": "0"}], "uuid": "efc258fd-d31d-c088-b14e-5df35bf4613f", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "ccaf222e-ae74-6bcc-b7cc-ae923600dc8c", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "0ecd138d-71bd-afbb-1f30-7865f0805379", "time": 0.875, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 1156, "z": 0}], "uuid": "ff161e5e-cdfd-a14d-954b-559209cb1922", "time": 1, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "3a12f200-757f-cef0-2b88-22152faac113", "time": 0.625, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "3", "y": "3", "z": "3"}], "uuid": "0c1112ee-5929-65f8-2213-7065c8b45c82", "time": 0, "color": -1, "uniform": true, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": 3, "y": 3, "z": 3}], "uuid": "3e45aebd-1493-ce7e-c27a-f317c1fd9873", "time": 0.25, "color": -1, "uniform": true, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "15", "y": "15", "z": "15"}], "uuid": "c4de1fe1-e0bd-70f7-0442-8f56d108edde", "time": 0.625, "color": -1, "uniform": true, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}}}, {"uuid": "246f0be6-fcba-0556-1375-8fd5511e0d24", "name": "pocketPhone", "loop": "hold", "override": false, "length": 1, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"6eafee00-f6b9-845e-696a-44f9f53e9eeb": {"name": "phone", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "928fcaea-a636-6318-6e7c-21c9ef1994ae", "time": 0.625, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "edfb726c-c4b2-5b95-f7d5-62a3bd7194cf", "time": 1, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": -90}], "uuid": "7a0284d6-a0d8-aabc-6ca9-876c53a5d0ac", "time": 0.375, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -493, "z": "0"}], "uuid": "efc258fd-d31d-c088-b14e-5df35bf4613f", "time": 1, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "ccaf222e-ae74-6bcc-b7cc-ae923600dc8c", "time": 0.75, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "0ecd138d-71bd-afbb-1f30-7865f0805379", "time": 0.125, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 1156, "z": 0}], "uuid": "ff161e5e-cdfd-a14d-954b-559209cb1922", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "3a12f200-757f-cef0-2b88-22152faac113", "time": 0.375, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "3", "y": "3", "z": "3"}], "uuid": "0c1112ee-5929-65f8-2213-7065c8b45c82", "time": 1, "color": -1, "uniform": true, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": 3, "y": 3, "z": 3}], "uuid": "3e45aebd-1493-ce7e-c27a-f317c1fd9873", "time": 0.75, "color": -1, "uniform": true, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "15", "y": "15", "z": "15"}], "uuid": "c4de1fe1-e0bd-70f7-0442-8f56d108edde", "time": 0.375, "color": -1, "uniform": true, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}}}]}