{"meta": {"format_version": "4.10", "model_format": "free", "box_uv": false}, "name": "sphere", "model_identifier": "", "visible_box": [1, 1, 0], "variable_placeholders": "", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 16, "height": 16}, "elements": [{"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-944, -894, -1024], "to": [1056, 1106, 976], "autouv": 1, "color": 6, "origin": [-943, -894, -1023], "faces": {"north": {"uv": [0, 0, 16, 16], "texture": 0}, "east": {"uv": [0, 0, 16, 16], "texture": 0}, "south": {"uv": [0, 0, 16, 16], "texture": 0}, "west": {"uv": [0, 0, 16, 16], "texture": 0}, "up": {"uv": [0, 0, 16, 16], "texture": 0}, "down": {"uv": [0, 0, 16, 16], "texture": 0}}, "type": "cube", "uuid": "f29a7460-3bd2-5d90-9132-d3f5c010639c"}], "outliner": [{"name": "GUI", "origin": [0, 60, -1023], "color": 0, "uuid": "19a66850-e00c-4363-9b55-48d0099493d6", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["f29a7460-3bd2-5d90-9132-d3f5c010639c"]}], "textures": [{"path": "", "name": "texture_e", "folder": "block", "namespace": "", "id": "0", "width": 16, "height": 16, "uv_width": 16, "uv_height": 16, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "6f9c2d6f-2a2b-a788-6748-87829e9b7f22", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAB5JREFUOE9j/P///38GCgDjqAEMo2HAMBoGDMMiDAAlHz/RG+BMbgAAAABJRU5ErkJggg=="}]}