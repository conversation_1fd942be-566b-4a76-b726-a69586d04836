package org.figuramc.figura.avatar.local;

import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.nbt.Tag;
import org.figuramc.figura.FiguraMod;
import org.figuramc.figura.avatar.AvatarMetadataParser;
import org.figuramc.figura.avatar.UserData;
import org.figuramc.figura.model.BlockbenchModelParser;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Set;

/**
 * Loads avatar data from bundled resources and converts it to NBT format
 */
public class BundledAvatarResourceLoader {
    
    /**
     * Loads a bundled avatar and converts it to NBT format for the avatar system
     */
    public static void loadBundledAvatar(BundledAvatarLoader.BundledAvatarPath bundledAvatar, UserData target) {
        LocalAvatarLoader.async(() -> {
            try {
                FiguraMod.debug("Loading bundled avatar: " + bundledAvatar.getAvatarName());
                
                CompoundTag nbt = new CompoundTag();
                
                // Load scripts
                loadScripts(bundledAvatar, nbt);
                
                // Load sounds
                loadSounds(bundledAvatar, nbt);
                
                // Load models and textures
                CompoundTag textures = new CompoundTag();
                ListTag animations = new ListTag();
                BlockbenchModelParser modelParser = new BlockbenchModelParser();
                
                CompoundTag models = loadModels(bundledAvatar, modelParser, textures, animations);
                models.putString("name", "models");
                
                // Load metadata
                loadMetadata(bundledAvatar, nbt);
                
                // Add models, textures, and animations to NBT
                nbt.put("models", models);
                nbt.put("textures", textures);
                nbt.put("animations", animations);
                
                // Load the avatar
                target.loadAvatar(nbt);
                
                FiguraMod.debug("Successfully loaded bundled avatar: " + bundledAvatar.getAvatarName());
                
            } catch (Exception e) {
                FiguraMod.LOGGER.error("Failed to load bundled avatar: " + bundledAvatar.getAvatarName(), e);
            }
        });
    }
    
    private static void loadScripts(BundledAvatarLoader.BundledAvatarPath bundledAvatar, CompoundTag nbt) {
        CompoundTag scripts = new CompoundTag();
        
        try {
            Set<String> resourcePaths = bundledAvatar.getResourcePaths();
            
            for (String path : resourcePaths) {
                if (path.endsWith(".lua")) {
                    try (InputStream stream = bundledAvatar.getResource(path)) {
                        if (stream != null) {
                            String scriptContent = new String(stream.readAllBytes(), StandardCharsets.UTF_8);
                            
                            // Convert path to script name (remove .lua extension)
                            String scriptName = path;
                            if (scriptName.endsWith(".lua")) {
                                scriptName = scriptName.substring(0, scriptName.length() - 4);
                            }
                            
                            scripts.putString(scriptName, scriptContent);
                            FiguraMod.debug("Loaded script: " + path);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            FiguraMod.LOGGER.error("Failed to load scripts for bundled avatar: " + bundledAvatar.getAvatarName(), e);
        }
        
        if (!scripts.isEmpty()) {
            nbt.put("scripts", scripts);
        }
    }
    
    private static void loadSounds(BundledAvatarLoader.BundledAvatarPath bundledAvatar, CompoundTag nbt) {
        CompoundTag sounds = new CompoundTag();
        
        try {
            Set<String> resourcePaths = bundledAvatar.getResourcePaths();
            
            for (String path : resourcePaths) {
                if (path.endsWith(".ogg")) {
                    try (InputStream stream = bundledAvatar.getResource(path)) {
                        if (stream != null) {
                            byte[] soundData = stream.readAllBytes();
                            
                            // Convert path to sound name (remove .ogg extension)
                            String soundName = path;
                            if (soundName.endsWith(".ogg")) {
                                soundName = soundName.substring(0, soundName.length() - 4);
                            }
                            
                            sounds.putByteArray(soundName, soundData);
                            FiguraMod.debug("Loaded sound: " + path);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            FiguraMod.LOGGER.error("Failed to load sounds for bundled avatar: " + bundledAvatar.getAvatarName(), e);
        }
        
        if (!sounds.isEmpty()) {
            nbt.put("sounds", sounds);
        }
    }
    
    private static CompoundTag loadModels(BundledAvatarLoader.BundledAvatarPath bundledAvatar, 
                                         BlockbenchModelParser modelParser, 
                                         CompoundTag textures, 
                                         ListTag animations) {
        CompoundTag models = new CompoundTag();
        
        try {
            Set<String> resourcePaths = bundledAvatar.getResourcePaths();
            
            // Load textures first
            CompoundTag texturesSrc = new CompoundTag();
            for (String path : resourcePaths) {
                if (path.endsWith(".png")) {
                    try (InputStream stream = bundledAvatar.getResource(path)) {
                        if (stream != null) {
                            byte[] textureData = stream.readAllBytes();
                            
                            // Convert path to texture name (remove .png extension)
                            String textureName = path;
                            if (textureName.endsWith(".png")) {
                                textureName = textureName.substring(0, textureName.length() - 4);
                            }
                            
                            texturesSrc.putByteArray(textureName, textureData);
                            FiguraMod.debug("Loaded texture: " + path);
                        }
                    }
                }
            }
            
            if (!texturesSrc.isEmpty()) {
                textures.put("src", texturesSrc);
            }
            
            // Load models
            for (String path : resourcePaths) {
                if (path.endsWith(".bbmodel")) {
                    try (InputStream stream = bundledAvatar.getResource(path)) {
                        if (stream != null) {
                            String modelJson = new String(stream.readAllBytes(), StandardCharsets.UTF_8);
                            
                            // Parse the model
                            CompoundTag modelData = modelParser.parseModel(
                                bundledAvatar.getAvatarName(), 
                                new ByteArrayInputStream(modelJson.getBytes(StandardCharsets.UTF_8)), 
                                textures, 
                                animations
                            );
                            
                            // Convert path to model name (remove .bbmodel extension)
                            String modelName = path;
                            if (modelName.endsWith(".bbmodel")) {
                                modelName = modelName.substring(0, modelName.length() - 8);
                            }
                            
                            models.put(modelName, modelData);
                            FiguraMod.debug("Loaded model: " + path);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            FiguraMod.LOGGER.error("Failed to load models for bundled avatar: " + bundledAvatar.getAvatarName(), e);
        }
        
        return models;
    }
    
    private static void loadMetadata(BundledAvatarLoader.BundledAvatarPath bundledAvatar, CompoundTag nbt) {
        try {
            if (bundledAvatar.hasResource("avatar.json")) {
                try (InputStream stream = bundledAvatar.getResource("avatar.json")) {
                    if (stream != null) {
                        String metadataJson = new String(stream.readAllBytes(), StandardCharsets.UTF_8);
                        AvatarMetadataParser.Metadata metadata = AvatarMetadataParser.read(metadataJson);
                        
                        // Add metadata to NBT
                        if (metadata.name != null) {
                            nbt.putString("name", metadata.name);
                        }
                        if (metadata.description != null) {
                            nbt.putString("description", metadata.description);
                        }
                        if (metadata.authors != null && !metadata.authors.isEmpty()) {
                            nbt.putString("authors", String.join(", ", metadata.authors));
                        }
                        if (metadata.version != null) {
                            nbt.putString("version", metadata.version);
                        }
                        
                        FiguraMod.debug("Loaded metadata for: " + bundledAvatar.getAvatarName());
                    }
                }
            }
        } catch (Exception e) {
            FiguraMod.LOGGER.error("Failed to load metadata for bundled avatar: " + bundledAvatar.getAvatarName(), e);
        }
    }
}
