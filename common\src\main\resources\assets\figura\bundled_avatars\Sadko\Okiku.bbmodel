{"meta": {"format_version": "4.10", "model_format": "figura", "box_uv": true}, "visible_box": [1, 1, 0], "variable_placeholders": "", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 96, "height": 96}, "elements": [{"name": "Box", "color": 6, "origin": [0, 4, -3.5], "rotation": [0, 0, 0], "export": true, "visibility": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "vertices": {"fwB8": [5, 8, 9], "rpVt": [6, 9, -1], "Mk9j": [5, -1, 9], "0UPl": [6, -2, -1], "Fvav": [-5, 8, 9], "WXMD": [-6, 9, -1], "a4wa": [-5, -1, 9], "7tyB": [-6, -2, -1]}, "faces": {"4dtAYEU0": {"uv": {"rpVt": [30, 31], "fwB8": [20, 32], "0UPl": [30, 42], "Mk9j": [20, 41]}, "vertices": ["rpVt", "fwB8", "0UPl", "Mk9j"], "texture": 0}, "H6MJlZK3": {"uv": {"Fvav": [10, 32], "WXMD": [0, 31], "a4wa": [10, 41], "7tyB": [0, 42]}, "vertices": ["Fvav", "WXMD", "a4wa", "7tyB"], "texture": 0}, "c2oXOvpv": {"uv": {"rpVt": [21, 22], "WXMD": [9, 22], "fwB8": [20, 32], "Fvav": [10, 32]}, "vertices": ["rpVt", "WXMD", "fwB8", "Fvav"], "texture": 0}, "P7AltjMr": {"uv": {"Mk9j": [20, 41], "a4wa": [10, 41], "0UPl": [21, 51], "7tyB": [9, 51]}, "vertices": ["Mk9j", "a4wa", "0UPl", "7tyB"], "texture": 0}, "ohetdats": {"uv": {"fwB8": [20, 32], "Fvav": [10, 32], "Mk9j": [20, 41], "a4wa": [10, 41]}, "vertices": ["fwB8", "Fvav", "Mk9j", "a4wa"], "texture": 0}}, "type": "mesh", "uuid": "9a3b6333-8e2e-050d-3b86-32d301b35caf"}, {"name": "Screen1", "color": 0, "origin": [0, 3.5, -4.4], "rotation": [0, 0, 0], "export": true, "visibility": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "vertices": {"cKqE": [5, 8.5, -1], "JzLp": [5, 0.5, -1], "62o1": [-5, 8.5, -1], "BczB": [-5, 0.5, -1], "urIK": [-5, 3.1666666666666656, -1.1], "qru8": [5, 3.1666666666666656, -1.1], "aPiU": [-5, 5.833333333333333, -1.1], "2Zkg": [5, 5.833333333333333, -1.1], "EKh5": [-1.6666666666666665, 5.833333333333333, -1.4000000000000004], "uWAJ": [-1.6666666666666665, 3.1666666666666656, -1.4000000000000004], "j389": [1.6666666666666665, 5.833333333333333, -1.4000000000000004], "zDFM": [1.6666666666666665, 3.1666666666666656, -1.4000000000000004], "LRWT": [-1.6666666666666665, 0.5, -1.1], "GBFd": [1.6666666666666665, 0.5, -1.1], "rcWJ": [-1.6666666666666665, 8.5, -1.1], "25oi": [1.6666666666666665, 8.5, -1.1]}, "faces": {"Y9mc7YAx": {"uv": {"2Zkg": [0, 3.0000000000000004], "25oi": [4, 0], "j389": [4, 3.0000000000000004], "cKqE": [0, 0]}, "vertices": ["2Zkg", "25oi", "j389", "cKqE"], "texture": 2}, "BKfUvAVw": {"uv": {"JzLp": [0, 9], "zDFM": [4, 6.000000000000001], "GBFd": [4, 9], "qru8": [0, 6.000000000000001]}, "vertices": ["JzLp", "zDFM", "GBFd", "qru8"], "texture": 2}, "VzpA3JAI": {"uv": {"qru8": [0, 6.000000000000001], "j389": [4, 3.0000000000000004], "zDFM": [4, 6.000000000000001], "2Zkg": [0, 3.0000000000000004]}, "vertices": ["qru8", "j389", "zDFM", "2Zkg"], "texture": 2}, "WDLzjsBc": {"uv": {"aPiU": [12, 3.0000000000000004], "EKh5": [8, 3.0000000000000004], "uWAJ": [8, 6.000000000000001], "urIK": [12, 6.000000000000001]}, "vertices": ["aPiU", "EKh5", "uWAJ", "urIK"], "texture": 2}, "eqr28iAE": {"uv": {"EKh5": [8, 3.0000000000000004], "j389": [4, 3.0000000000000004], "zDFM": [4, 6.000000000000001], "uWAJ": [8, 6.000000000000001]}, "vertices": ["EKh5", "j389", "zDFM", "uWAJ"], "texture": 2}, "z2qin8kD": {"uv": {"urIK": [12, 6.000000000000001], "uWAJ": [8, 6.000000000000001], "LRWT": [8, 9], "BczB": [12, 9]}, "vertices": ["urIK", "uWAJ", "LRWT", "BczB"], "texture": 2}, "SOrpDcoX": {"uv": {"uWAJ": [8, 6.000000000000001], "zDFM": [4, 6.000000000000001], "GBFd": [4, 9], "LRWT": [8, 9]}, "vertices": ["uWAJ", "zDFM", "GBFd", "LRWT"], "texture": 2}, "FMhw20xL": {"uv": {"62o1": [12, 0], "rcWJ": [8, 0], "EKh5": [8, 3.0000000000000004], "aPiU": [12, 3.0000000000000004]}, "vertices": ["62o1", "rcWJ", "EKh5", "aPiU"], "texture": 2}, "3mfP3AOY": {"uv": {"rcWJ": [8, 0], "25oi": [4, 0], "j389": [4, 3.0000000000000004], "EKh5": [8, 3.0000000000000004]}, "vertices": ["rcWJ", "25oi", "j389", "EKh5"], "texture": 2}}, "type": "mesh", "uuid": "33027ec2-73cf-1a6b-8119-82621a98061e"}, {"name": "SWORD", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.500000000000002, 0.25, 3.6500000000000004], "to": [-3.4999999999999996, 2.25, 4.65], "autouv": 0, "color": 0, "visibility": false, "origin": [0, 5, 7], "faces": {"north": {"uv": [5, 7, 6, 9], "texture": 0}, "east": {"uv": [4, 7, 5, 9], "texture": 0}, "south": {"uv": [7, 7, 8, 9], "texture": 0}, "west": {"uv": [6, 7, 7, 9], "texture": 0}, "up": {"uv": [6, 7, 5, 6], "texture": 0}, "down": {"uv": [7, 6, 6, 7], "texture": 0}}, "type": "cube", "uuid": "9238854b-d5ca-0df3-62e3-73308dc85253"}, {"name": "FIGHT", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0.3500000000000001, 3.75], "to": [4.499999999999998, 1.15, 4.550000000000001], "autouv": 0, "color": 0, "visibility": false, "origin": [0, 5, 7], "faces": {"north": {"uv": [8, 2, 0, 3], "texture": 0}, "east": {"uv": [7, 2, 8, 3], "texture": 0}, "south": {"uv": [0, 2, 8, 3], "texture": 0}, "west": {"uv": [0, 2, 1, 3], "texture": 0}, "up": {"uv": [0, 2, 8, 3], "texture": 0}, "down": {"uv": [0, 2, 8, 3], "texture": 0}}, "type": "cube", "uuid": "72441fb2-35e3-e956-0b61-c6459705ab96"}, {"name": "SWORD", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.25, 0.4500000000000002, 3.8499999999999996], "to": [4.749999999999998, 1.0499999999999998, 4.450000000000001], "autouv": 0, "color": 0, "visibility": false, "origin": [0, 5, 7], "faces": {"north": {"uv": [8, 3, 0, 4], "texture": 0}, "east": {"uv": [7, 3, 8, 4], "texture": 0}, "south": {"uv": [0, 3, 8, 4], "texture": 0}, "west": {"uv": [0, 3, 1, 4], "texture": 0}, "up": {"uv": [0, 3, 8, 4], "texture": 0}, "down": {"uv": [0, 3, 8, 4], "texture": 0}}, "type": "cube", "uuid": "8317ebc9-9de9-c9c4-14ec-1498d8f5ad6c"}, {"name": "FIGHT", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 0.5500000000000003, 3.9499999999999993], "to": [4.999999999999998, 0.9499999999999997, 4.350000000000001], "autouv": 0, "color": 0, "visibility": false, "origin": [0, 5, 7], "faces": {"north": {"uv": [8, 4, 0, 5], "texture": 0}, "east": {"uv": [7, 4, 8, 5], "texture": 0}, "south": {"uv": [0, 4, 8, 5], "texture": 0}, "west": {"uv": [0, 4, 1, 5], "texture": 0}, "up": {"uv": [0, 4, 8, 5], "texture": 0}, "down": {"uv": [0, 4, 8, 5], "texture": 0}}, "type": "cube", "uuid": "187b337d-032b-86e3-266e-d8a411f40bab"}, {"name": "HIYAAA", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.799999999999999, 0.30000000000000027, 3.6999999999999993], "to": [5.299999999999999, 1.1999999999999997, 4.600000000000001], "autouv": 0, "color": 0, "visibility": false, "origin": [0, 5, 7], "faces": {"north": {"uv": [1, 1, 2, 2], "texture": 0}, "east": {"uv": [0, 1, 1, 2], "texture": 0}, "south": {"uv": [3, 1, 4, 2], "texture": 0}, "west": {"uv": [2, 1, 3, 2], "texture": 0}, "up": {"uv": [1, 0, 2, 1], "texture": 0}, "down": {"uv": [2, 0, 3, 1], "texture": 0}}, "type": "cube", "uuid": "664d295b-c382-be33-91c3-31bed5077f20"}, {"name": "Power", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.5, 1.9, -6.1], "to": [5.25, 2.65, -5.1], "autouv": 0, "color": 1, "visibility": false, "origin": [0, 1, -1.5], "faces": {"north": {"uv": [1, 6, 2, 7], "texture": 0}, "east": {"uv": [0, 6, 1, 7], "texture": 0}, "south": {"uv": [3, 6, 4, 7], "texture": 0}, "west": {"uv": [2, 6, 3, 7], "texture": 0}, "up": {"uv": [1, 6, 2, 5], "texture": 0}, "down": {"uv": [2, 5, 3, 6], "texture": 0}}, "type": "cube", "uuid": "ac8fe40d-8582-198a-4760-7a79022b3e15"}, {"name": "Volume", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.75, 2, -6.1], "to": [4.25, 2.5, -5.1], "autouv": 0, "color": 1, "visibility": false, "origin": [0, 1, -1.5], "faces": {"north": {"uv": [1, 8, 2, 9], "texture": 0}, "east": {"uv": [0, 8, 1, 9], "texture": 0}, "south": {"uv": [3, 8, 4, 9], "texture": 0}, "west": {"uv": [2, 8, 3, 9], "texture": 0}, "up": {"uv": [1, 8, 2, 7], "texture": 0}, "down": {"uv": [2, 7, 3, 8], "texture": 0}}, "type": "cube", "uuid": "2db5f9e5-34e2-8e64-9984-1fc214c00d0d"}, {"name": "Volume", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3, 2, -6.1], "to": [3.5, 2.5, -5.1], "autouv": 0, "color": 1, "visibility": false, "origin": [0, 1, -1.5], "faces": {"north": {"uv": [1, 8, 2, 9], "texture": 0}, "east": {"uv": [0, 8, 1, 9], "texture": 0}, "south": {"uv": [3, 8, 4, 9], "texture": 0}, "west": {"uv": [2, 8, 3, 9], "texture": 0}, "up": {"uv": [1, 8, 2, 7], "texture": 0}, "down": {"uv": [2, 7, 3, 8], "texture": 0}}, "type": "cube", "uuid": "c408c38d-7591-74f1-e9f1-f6e57293fdf6"}, {"name": "Channel", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2.25, 2, -6.1], "to": [2.75, 2.5, -5.1], "autouv": 0, "color": 1, "visibility": false, "origin": [0, 1, -1.5], "faces": {"north": {"uv": [1, 8, 2, 9], "texture": 0}, "east": {"uv": [0, 8, 1, 9], "texture": 0}, "south": {"uv": [3, 8, 4, 9], "texture": 0}, "west": {"uv": [2, 8, 3, 9], "texture": 0}, "up": {"uv": [1, 8, 2, 7], "texture": 0}, "down": {"uv": [2, 7, 3, 8], "texture": 0}}, "type": "cube", "uuid": "65422114-5a3b-6d40-8fac-42a8b6af4fb6"}, {"name": "Channel", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.5, 2, -6.1], "to": [2, 2.5, -5.1], "autouv": 0, "color": 1, "visibility": false, "origin": [0, 1, -1.5], "faces": {"north": {"uv": [1, 8, 2, 9], "texture": 0}, "east": {"uv": [0, 8, 1, 9], "texture": 0}, "south": {"uv": [3, 8, 4, 9], "texture": 0}, "west": {"uv": [2, 8, 3, 9], "texture": 0}, "up": {"uv": [1, 8, 2, 7], "texture": 0}, "down": {"uv": [2, 7, 3, 8], "texture": 0}}, "type": "cube", "uuid": "940cf741-c327-3830-eeda-d7cd023566f4"}, {"name": "Border", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 4, -6], "to": [-5, 13, -4], "autouv": 0, "color": 0, "visibility": false, "mirror_uv": true, "origin": [0, 1, -1.5], "uv_offset": [0, 20], "faces": {"north": {"uv": [4, 22, 2, 31], "texture": 0}, "east": {"uv": [6, 22, 4, 31], "texture": 0}, "south": {"uv": [8, 22, 6, 31], "texture": 0}, "west": {"uv": [2, 22, 0, 31], "texture": 0}, "up": {"uv": [2, 22, 4, 20], "texture": 0}, "down": {"uv": [4, 20, 6, 22], "texture": 0}}, "type": "cube", "uuid": "716c15bf-a9fc-b071-98fa-8aa681864ff4"}, {"name": "Border", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 4, -6], "to": [7, 13, -4], "autouv": 0, "color": 0, "visibility": false, "origin": [0, 1, -1.5], "uv_offset": [0, 20], "faces": {"north": {"uv": [2, 22, 4, 31], "texture": 0}, "east": {"uv": [0, 22, 2, 31], "texture": 0}, "south": {"uv": [6, 22, 8, 31], "texture": 0}, "west": {"uv": [4, 22, 6, 31], "texture": 0}, "up": {"uv": [4, 22, 2, 20], "texture": 0}, "down": {"uv": [6, 20, 4, 22], "texture": 0}}, "type": "cube", "uuid": "7c308bbb-fe66-81d0-b66e-17b8da271c93"}, {"name": "Interface", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 1, -6], "to": [7, 4, -3], "autouv": 0, "color": 4, "visibility": false, "origin": [0, 1, -1.5], "uv_offset": [0, 12], "faces": {"north": {"uv": [3, 15, 17, 18], "texture": 0}, "east": {"uv": [0, 15, 3, 18], "texture": 0}, "south": {"uv": [20, 15, 34, 18], "texture": 0}, "west": {"uv": [17, 15, 20, 18], "texture": 0}, "up": {"uv": [17, 15, 3, 12], "texture": 0}, "down": {"uv": [31, 12, 17, 15], "texture": 0}}, "type": "cube", "uuid": "c247aaaf-9ea3-2355-16bd-a36aef5c599f"}, {"name": "Stand", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 0, -4.5], "to": [5, 3, 4.5], "autouv": 0, "color": 3, "visibility": false, "origin": [0, 1, -1.5], "faces": {"north": {"uv": [9, 9, 19, 12], "texture": 0}, "east": {"uv": [0, 9, 9, 12], "texture": 0}, "south": {"uv": [28, 9, 38, 12], "texture": 0}, "west": {"uv": [19, 9, 28, 12], "texture": 0}, "up": {"uv": [19, 9, 9, 0], "texture": 0}, "down": {"uv": [29, 0, 19, 9], "texture": 0}}, "type": "cube", "uuid": "27162795-bdbd-4185-acbc-e3c787c1616f"}, {"name": "Border", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 12, -6], "to": [6, 14, -4], "autouv": 0, "color": 0, "visibility": false, "origin": [0, 1, -1.5], "uv_offset": [6, 18], "faces": {"north": {"uv": [8, 20, 20, 22], "texture": 0}, "east": {"uv": [6, 20, 8, 22], "texture": 0}, "south": {"uv": [22, 20, 34, 22], "texture": 0}, "west": {"uv": [20, 20, 22, 22], "texture": 0}, "up": {"uv": [20, 20, 8, 18], "texture": 0}, "down": {"uv": [32, 18, 20, 20], "texture": 0}}, "type": "cube", "uuid": "ba24b29e-d7ac-6e89-e05a-5a56c8c26dea"}, {"name": "METS", "color": 0, "origin": [0, 3.5, -4.5], "rotation": [0, 0, 0], "export": true, "visibility": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "vertices": {"cKqE": [5, 8.5, -1], "JzLp": [5, 0.5, -1], "62o1": [-5, 8.5, -1], "BczB": [-5, 0.5, -1], "urIK": [-5, 3.1666666666666656, -1.1], "qru8": [5, 3.1666666666666656, -1.1], "aPiU": [-5, 5.833333333333333, -1.1], "2Zkg": [5, 5.833333333333333, -1.1], "EKh5": [-1.6666666666666665, 5.833333333333333, -1.4000000000000004], "uWAJ": [-1.6666666666666665, 3.1666666666666656, -1.4000000000000004], "j389": [1.6666666666666665, 5.833333333333333, -1.4000000000000004], "zDFM": [1.6666666666666665, 3.1666666666666656, -1.4000000000000004], "LRWT": [-1.6666666666666665, 0.5, -1.1], "GBFd": [1.6666666666666665, 0.5, -1.1], "rcWJ": [-1.6666666666666665, 8.5, -1.1], "25oi": [1.6666666666666665, 8.5, -1.1]}, "faces": {"Y9mc7YAx": {"uv": {"2Zkg": [0, 11.999999999999979], "25oi": [16, 0], "j389": [16, 11.999999999999979], "cKqE": [0, 0]}, "vertices": ["2Zkg", "25oi", "j389", "cKqE"], "texture": 6}, "BKfUvAVw": {"uv": {"JzLp": [0, 35.99999999999992], "zDFM": [16, 23.99999999999995], "GBFd": [16, 35.99999999999992], "qru8": [0, 23.99999999999995]}, "vertices": ["JzLp", "zDFM", "GBFd", "qru8"], "texture": 6}, "VzpA3JAI": {"uv": {"qru8": [0, 23.99999999999995], "j389": [16, 11.999999999999979], "zDFM": [16, 23.99999999999995], "2Zkg": [0, 11.999999999999979]}, "vertices": ["qru8", "j389", "zDFM", "2Zkg"], "texture": 6}, "WDLzjsBc": {"uv": {"aPiU": [48, 11.999999999999979], "EKh5": [32, 11.999999999999979], "uWAJ": [32, 23.99999999999995], "urIK": [48, 23.99999999999995]}, "vertices": ["aPiU", "EKh5", "uWAJ", "urIK"], "texture": 6}, "eqr28iAE": {"uv": {"EKh5": [32, 11.999999999999979], "j389": [16, 11.999999999999979], "zDFM": [16, 23.99999999999995], "uWAJ": [32, 23.99999999999995]}, "vertices": ["EKh5", "j389", "zDFM", "uWAJ"], "texture": 6}, "z2qin8kD": {"uv": {"urIK": [48, 23.99999999999995], "uWAJ": [32, 23.99999999999995], "LRWT": [32, 35.99999999999992], "BczB": [48, 35.99999999999992]}, "vertices": ["urIK", "uWAJ", "LRWT", "BczB"], "texture": 6}, "SOrpDcoX": {"uv": {"uWAJ": [32, 23.99999999999995], "zDFM": [16, 23.99999999999995], "GBFd": [16, 35.99999999999992], "LRWT": [32, 35.99999999999992]}, "vertices": ["uWAJ", "zDFM", "GBFd", "LRWT"], "texture": 6}, "FMhw20xL": {"uv": {"62o1": [48, 0], "rcWJ": [32, 0], "EKh5": [32, 11.999999999999979], "aPiU": [48, 11.999999999999979]}, "vertices": ["62o1", "rcWJ", "EKh5", "aPiU"], "texture": 6}, "3mfP3AOY": {"uv": {"rcWJ": [32, 0], "25oi": [16, 0], "j389": [16, 11.999999999999979], "EKh5": [32, 11.999999999999979]}, "vertices": ["rcWJ", "25oi", "j389", "EKh5"], "texture": 6}}, "type": "mesh", "uuid": "d4b25cf8-d258-7559-1344-e8abcc8511db"}, {"name": "Screen0", "color": 0, "origin": [0, 3.5, -4.5], "rotation": [0, 0, 0], "export": true, "visibility": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "vertices": {"cKqE": [5, 8.5, -1], "JzLp": [5, 0.5, -1], "62o1": [-5, 8.5, -1], "BczB": [-5, 0.5, -1], "urIK": [-5, 3.1666666666666656, -1.1], "qru8": [5, 3.1666666666666656, -1.1], "aPiU": [-5, 5.833333333333333, -1.1], "2Zkg": [5, 5.833333333333333, -1.1], "EKh5": [-1.6666666666666665, 5.833333333333333, -1.4000000000000004], "uWAJ": [-1.6666666666666665, 3.1666666666666656, -1.4000000000000004], "j389": [1.6666666666666665, 5.833333333333333, -1.4000000000000004], "zDFM": [1.6666666666666665, 3.1666666666666656, -1.4000000000000004], "LRWT": [-1.6666666666666665, 0.5, -1.1], "GBFd": [1.6666666666666665, 0.5, -1.1], "rcWJ": [-1.6666666666666665, 8.5, -1.1], "25oi": [1.6666666666666665, 8.5, -1.1]}, "faces": {"Y9mc7YAx": {"uv": {"2Zkg": [0, 3.0000000000000004], "25oi": [4, 0], "j389": [4, 3.0000000000000004], "cKqE": [0, 0]}, "vertices": ["2Zkg", "25oi", "j389", "cKqE"], "texture": 1}, "BKfUvAVw": {"uv": {"JzLp": [0, 9], "zDFM": [4, 6.000000000000001], "GBFd": [4, 9], "qru8": [0, 6.000000000000001]}, "vertices": ["JzLp", "zDFM", "GBFd", "qru8"], "texture": 1}, "VzpA3JAI": {"uv": {"qru8": [0, 6.000000000000001], "j389": [4, 3.0000000000000004], "zDFM": [4, 6.000000000000001], "2Zkg": [0, 3.0000000000000004]}, "vertices": ["qru8", "j389", "zDFM", "2Zkg"], "texture": 1}, "WDLzjsBc": {"uv": {"aPiU": [12, 3.0000000000000004], "EKh5": [8, 3.0000000000000004], "uWAJ": [8, 6.000000000000001], "urIK": [12, 6.000000000000001]}, "vertices": ["aPiU", "EKh5", "uWAJ", "urIK"], "texture": 1}, "eqr28iAE": {"uv": {"EKh5": [8, 3.0000000000000004], "j389": [4, 3.0000000000000004], "zDFM": [4, 6.000000000000001], "uWAJ": [8, 6.000000000000001]}, "vertices": ["EKh5", "j389", "zDFM", "uWAJ"], "texture": 1}, "z2qin8kD": {"uv": {"urIK": [12, 6.000000000000001], "uWAJ": [8, 6.000000000000001], "LRWT": [8, 9], "BczB": [12, 9]}, "vertices": ["urIK", "uWAJ", "LRWT", "BczB"], "texture": 1}, "SOrpDcoX": {"uv": {"uWAJ": [8, 6.000000000000001], "zDFM": [4, 6.000000000000001], "GBFd": [4, 9], "LRWT": [8, 9]}, "vertices": ["uWAJ", "zDFM", "GBFd", "LRWT"], "texture": 1}, "FMhw20xL": {"uv": {"62o1": [12, 0], "rcWJ": [8, 0], "EKh5": [8, 3.0000000000000004], "aPiU": [12, 3.0000000000000004]}, "vertices": ["62o1", "rcWJ", "EKh5", "aPiU"], "texture": 1}, "3mfP3AOY": {"uv": {"rcWJ": [8, 0], "25oi": [4, 0], "j389": [4, 3.0000000000000004], "EKh5": [8, 3.0000000000000004]}, "vertices": ["rcWJ", "25oi", "j389", "EKh5"], "texture": 1}}, "type": "mesh", "uuid": "20c32e37-344e-0e86-de5b-91c1957b34f1"}, {"name": "ScreenWell", "color": 0, "origin": [0, 3.5, -4.4], "rotation": [0, 0, 0], "export": true, "visibility": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "vertices": {"cKqE": [5, 8.5, -1], "JzLp": [5, 0.5, -1], "62o1": [-5, 8.5, -1], "BczB": [-5, 0.5, -1], "urIK": [-5, 3.1666666666666656, -1.1], "qru8": [5, 3.1666666666666656, -1.1], "aPiU": [-5, 5.833333333333333, -1.1], "2Zkg": [5, 5.833333333333333, -1.1], "EKh5": [-1.6666666666666665, 5.833333333333333, -1.4000000000000004], "uWAJ": [-1.6666666666666665, 3.1666666666666656, -1.4000000000000004], "j389": [1.6666666666666665, 5.833333333333333, -1.4000000000000004], "zDFM": [1.6666666666666665, 3.1666666666666656, -1.4000000000000004], "LRWT": [-1.6666666666666665, 0.5, -1.1], "GBFd": [1.6666666666666665, 0.5, -1.1], "rcWJ": [-1.6666666666666665, 8.5, -1.1], "25oi": [1.6666666666666665, 8.5, -1.1]}, "faces": {"Y9mc7YAx": {"uv": {"2Zkg": [0, 12.000000000000002], "25oi": [16, 0], "j389": [16, 12.000000000000002], "cKqE": [0, 0]}, "vertices": ["2Zkg", "25oi", "j389", "cKqE"], "texture": 4}, "BKfUvAVw": {"uv": {"JzLp": [0, 36], "zDFM": [16, 24.000000000000004], "GBFd": [16, 36], "qru8": [0, 24.000000000000004]}, "vertices": ["JzLp", "zDFM", "GBFd", "qru8"], "texture": 4}, "VzpA3JAI": {"uv": {"qru8": [0, 24.000000000000004], "j389": [16, 12.000000000000002], "zDFM": [16, 24.000000000000004], "2Zkg": [0, 12.000000000000002]}, "vertices": ["qru8", "j389", "zDFM", "2Zkg"], "texture": 4}, "WDLzjsBc": {"uv": {"aPiU": [48, 12.000000000000002], "EKh5": [32, 12.000000000000002], "uWAJ": [32, 24.000000000000004], "urIK": [48, 24.000000000000004]}, "vertices": ["aPiU", "EKh5", "uWAJ", "urIK"], "texture": 4}, "eqr28iAE": {"uv": {"EKh5": [32, 12.000000000000002], "j389": [16, 12.000000000000002], "zDFM": [16, 24.000000000000004], "uWAJ": [32, 24.000000000000004]}, "vertices": ["EKh5", "j389", "zDFM", "uWAJ"], "texture": 4}, "z2qin8kD": {"uv": {"urIK": [48, 24.000000000000004], "uWAJ": [32, 24.000000000000004], "LRWT": [32, 36], "BczB": [48, 36]}, "vertices": ["urIK", "uWAJ", "LRWT", "BczB"], "texture": 4}, "SOrpDcoX": {"uv": {"uWAJ": [32, 24.000000000000004], "zDFM": [16, 24.000000000000004], "GBFd": [16, 36], "LRWT": [32, 36]}, "vertices": ["uWAJ", "zDFM", "GBFd", "LRWT"], "texture": 4}, "FMhw20xL": {"uv": {"62o1": [48, 0], "rcWJ": [32, 0], "EKh5": [32, 12.000000000000002], "aPiU": [48, 12.000000000000002]}, "vertices": ["62o1", "rcWJ", "EKh5", "aPiU"], "texture": 4}, "3mfP3AOY": {"uv": {"rcWJ": [32, 0], "25oi": [16, 0], "j389": [16, 12.000000000000002], "EKh5": [32, 12.000000000000002]}, "vertices": ["rcWJ", "25oi", "j389", "EKh5"], "texture": 4}}, "type": "mesh", "uuid": "bbd5e52b-b770-cc36-fe61-8d90018ac22d"}, {"name": "Noggin", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 25, -4], "to": [4, 33, 4], "autouv": 0, "color": 3, "origin": [0, 10, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 8}, "east": {"uv": [0, 8, 8, 16], "texture": 8}, "south": {"uv": [24, 8, 32, 16], "texture": 8}, "west": {"uv": [16, 8, 24, 16], "texture": 8}, "up": {"uv": [16, 8, 8, 0], "texture": 8}, "down": {"uv": [24, 0, 16, 8], "texture": 8}}, "type": "cube", "uuid": "b5092793-9f7e-7f27-a4fc-015b5c95642c"}, {"name": "Chest", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 17, -2], "to": [3.5, 25, 2], "autouv": 0, "color": 8, "origin": [0, 10, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 11, 28], "texture": 8}, "east": {"uv": [0, 20, 4, 28], "texture": 8}, "south": {"uv": [15, 20, 22, 28], "texture": 8}, "west": {"uv": [11, 20, 15, 28], "texture": 8}, "up": {"uv": [11, 20, 4, 16], "texture": 8}, "down": {"uv": [18, 16, 11, 20], "texture": 8}}, "type": "cube", "uuid": "90130b1f-e50b-6e5e-41be-ae10482f9690"}, {"name": "Abdomen", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 12, -2], "to": [4, 17, 2], "autouv": 0, "color": 4, "origin": [0, 10, 0], "uv_offset": [0, 28], "faces": {"north": {"uv": [4, 32, 12, 37], "texture": 8}, "east": {"uv": [0, 32, 4, 37], "texture": 8}, "south": {"uv": [16, 32, 24, 37], "texture": 8}, "west": {"uv": [12, 32, 16, 37], "texture": 8}, "up": {"uv": [12, 32, 4, 28], "texture": 8}, "down": {"uv": [20, 28, 12, 32], "texture": 8}}, "type": "cube", "uuid": "458a0340-3768-9af7-18c2-d65d023fbf61"}, {"name": "ArmBits", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 12.5, -2], "to": [-3.5, 24.5, 2], "autouv": 0, "color": 7, "origin": [0, 10, 0], "uv_offset": [0, 37], "faces": {"north": {"uv": [4, 41, 7, 53], "texture": 8}, "east": {"uv": [0, 41, 4, 53], "texture": 8}, "south": {"uv": [11, 41, 14, 53], "texture": 8}, "west": {"uv": [7, 41, 11, 53], "texture": 8}, "up": {"uv": [7, 41, 4, 37], "texture": 8}, "down": {"uv": [10, 37, 7, 41], "texture": 8}}, "type": "cube", "uuid": "813ec64c-1d39-82b6-0a99-25a74102a8cd"}, {"name": "ArmBits", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.5, 12.5, -2], "to": [6.5, 24.5, 2], "autouv": 0, "color": 1, "origin": [0, 10, 0], "uv_offset": [14, 37], "faces": {"north": {"uv": [18, 41, 21, 53], "texture": 8}, "east": {"uv": [14, 41, 18, 53], "texture": 8}, "south": {"uv": [25, 41, 28, 53], "texture": 8}, "west": {"uv": [21, 41, 25, 53], "texture": 8}, "up": {"uv": [21, 41, 18, 37], "texture": 8}, "down": {"uv": [24, 37, 21, 41], "texture": 8}}, "type": "cube", "uuid": "f7576f2c-b1cf-884b-b649-bbadf805dd39"}, {"name": "LegBits", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 0, -2], "to": [0, 12, 2], "autouv": 0, "color": 5, "origin": [0, 10, 0], "uv_offset": [16, 53], "faces": {"north": {"uv": [20, 57, 24, 69], "texture": 8}, "east": {"uv": [16, 57, 20, 69], "texture": 8}, "south": {"uv": [28, 57, 32, 69], "texture": 8}, "west": {"uv": [24, 57, 28, 69], "texture": 8}, "up": {"uv": [24, 57, 20, 53], "texture": 8}, "down": {"uv": [28, 53, 24, 57], "texture": 8}}, "type": "cube", "uuid": "0af6065b-9df4-e600-b1fa-ae585b4e6d2a"}, {"name": "LegBits", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0, 0, -2], "to": [4, 12, 2], "autouv": 0, "color": 6, "origin": [0, 10, 0], "uv_offset": [0, 53], "faces": {"north": {"uv": [4, 57, 8, 69], "texture": 8}, "east": {"uv": [0, 57, 4, 69], "texture": 8}, "south": {"uv": [12, 57, 16, 69], "texture": 8}, "west": {"uv": [8, 57, 12, 69], "texture": 8}, "up": {"uv": [8, 57, 4, 53], "texture": 8}, "down": {"uv": [12, 53, 8, 57], "texture": 8}}, "type": "cube", "uuid": "4850a516-f058-fb83-20cc-b503904ef2b9"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 25, -4], "to": [4, 33, 4], "autouv": 0, "color": 3, "inflate": 0.25, "origin": [0, 10, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 8}, "east": {"uv": [32, 8, 40, 16], "texture": 8}, "south": {"uv": [56, 8, 64, 16], "texture": 8}, "west": {"uv": [48, 8, 56, 16], "texture": 8}, "up": {"uv": [48, 8, 40, 0], "texture": 8}, "down": {"uv": [56, 0, 48, 8], "texture": 8}}, "type": "cube", "uuid": "01f6f18c-ca0f-e8e9-558e-2684cabba2d0"}, {"name": "InnerScrubs", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 3.5, -2.25], "to": [3, 16.5, -0.25], "autouv": 0, "color": 0, "origin": [0, 10, 0], "uv_offset": [28, 81], "faces": {"north": {"uv": [30, 83, 36, 96], "texture": 8}, "east": {"uv": [28, 83, 30, 96], "texture": 8}, "south": {"uv": [38, 83, 44, 96], "texture": 8}, "west": {"uv": [36, 83, 38, 96], "texture": 8}, "up": {"uv": [36, 83, 30, 81], "texture": 8}, "down": {"uv": [42, 81, 36, 83], "texture": 8}}, "type": "cube", "uuid": "5e745e95-8cf1-a698-711c-a7d7acbe53e8"}, {"name": "InnerScrubs", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.375, 3.275, -2], "to": [-2.375, 16.274999999999988, 0], "autouv": 0, "color": 0, "rotation": [0, 0, -4], "origin": [0, 10, 0], "uv_offset": [44, 81], "faces": {"north": {"uv": [46, 83, 48, 96], "texture": 8}, "east": {"uv": [44, 83, 46, 96], "texture": 8}, "south": {"uv": [50, 83, 52, 96], "texture": 8}, "west": {"uv": [48, 83, 50, 96], "texture": 8}, "up": {"uv": [48, 83, 46, 81], "texture": 8}, "down": {"uv": [50, 81, 48, 83], "texture": 8}}, "type": "cube", "uuid": "90e96f79-48e0-02cd-645b-5818d1a7de0e"}, {"name": "InnerScrubs", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2.375, 3.275, -2], "to": [4.3750000000000036, 16.274999999999988, 0], "autouv": 0, "color": 0, "mirror_uv": true, "rotation": [0, 0, 4], "origin": [0, 10, 0], "uv_offset": [20, 81], "faces": {"north": {"uv": [24, 83, 22, 96], "texture": 8}, "east": {"uv": [26, 83, 24, 96], "texture": 8}, "south": {"uv": [28, 83, 26, 96], "texture": 8}, "west": {"uv": [22, 83, 20, 96], "texture": 8}, "up": {"uv": [22, 83, 24, 81], "texture": 8}, "down": {"uv": [24, 81, 26, 83], "texture": 8}}, "type": "cube", "uuid": "f2cfbe08-1fdd-8c5d-6b8e-08699b8a1cce"}, {"name": "InnerScrubs", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.25, 3.5, -2], "to": [-2.25, 16.5, 2], "autouv": 0, "color": 0, "inflate": 0.1, "origin": [0, 10, 0], "uv_offset": [52, 79], "faces": {"north": {"uv": [56, 83, 58, 96], "texture": 8}, "east": {"uv": [52, 83, 56, 96], "texture": 8}, "south": {"uv": [62, 83, 64, 96], "texture": 8}, "west": {"uv": [58, 83, 62, 96], "texture": 8}, "up": {"uv": [58, 83, 56, 79], "texture": 8}, "down": {"uv": [60, 79, 58, 83], "texture": 8}}, "type": "cube", "uuid": "c58f576a-33ff-990b-23d3-d7f8f1b2f31c"}, {"name": "InnerScrubs", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2.25, 3.5, -2], "to": [4.25, 16.5, 2], "autouv": 0, "color": 0, "mirror_uv": true, "inflate": 0.1, "origin": [0, 10, 0], "uv_offset": [8, 79], "faces": {"north": {"uv": [14, 83, 12, 96], "texture": 8}, "east": {"uv": [18, 83, 14, 96], "texture": 8}, "south": {"uv": [20, 83, 18, 96], "texture": 8}, "west": {"uv": [12, 83, 8, 96], "texture": 8}, "up": {"uv": [12, 83, 14, 79], "texture": 8}, "down": {"uv": [14, 79, 16, 83], "texture": 8}}, "type": "cube", "uuid": "d01c66af-f90d-ff6c-d6ce-2b6c131db425"}, {"name": "InnerScrubs", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 3.5, 0.25], "to": [3, 16.5, 2.25], "autouv": 0, "color": 0, "origin": [0, 10, 0], "uv_offset": [72, 81], "faces": {"north": {"uv": [74, 83, 80, 96], "texture": 8}, "east": {"uv": [72, 83, 74, 96], "texture": 8}, "south": {"uv": [82, 83, 88, 96], "texture": 8}, "west": {"uv": [80, 83, 82, 96], "texture": 8}, "up": {"uv": [80, 83, 74, 81], "texture": 8}, "down": {"uv": [86, 81, 80, 83], "texture": 8}}, "type": "cube", "uuid": "41aaedb6-cc1e-6512-c94e-a9b1d252d6a7"}, {"name": "InnerScrubs", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.375, 3.275, 0], "to": [-2.375, 16.274999999999988, 2], "autouv": 0, "color": 0, "rotation": [0, 0, -4], "origin": [0, 10, 0], "uv_offset": [64, 81], "faces": {"north": {"uv": [66, 83, 68, 96], "texture": 8}, "east": {"uv": [64, 83, 66, 96], "texture": 8}, "south": {"uv": [70, 83, 72, 96], "texture": 8}, "west": {"uv": [68, 83, 70, 96], "texture": 8}, "up": {"uv": [68, 83, 66, 81], "texture": 8}, "down": {"uv": [70, 81, 68, 83], "texture": 8}}, "type": "cube", "uuid": "6ca6582d-3208-dcd6-3434-4d1b5833c8e2"}, {"name": "InnerScrubs", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2.375, 3.275, 0], "to": [4.3750000000000036, 16.274999999999988, 2], "autouv": 0, "color": 0, "mirror_uv": true, "rotation": [0, 0, 4], "origin": [0, 10, 0], "uv_offset": [88, 81], "faces": {"north": {"uv": [92, 83, 90, 96], "texture": 8}, "east": {"uv": [94, 83, 92, 96], "texture": 8}, "south": {"uv": [96, 83, 94, 96], "texture": 8}, "west": {"uv": [90, 83, 88, 96], "texture": 8}, "up": {"uv": [90, 83, 92, 81], "texture": 8}, "down": {"uv": [92, 81, 94, 83], "texture": 8}}, "type": "cube", "uuid": "4747fc4d-bf85-4f5b-ac06-7a46fe740b5a"}, {"name": "ArmLayers", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.4, 16.5, -2], "to": [6.400000000000001, 24.5, 2], "autouv": 0, "color": 1, "inflate": 0.25, "rotation": [0, 0, -1.25], "origin": [0, 10, 0], "uv_offset": [42, 37], "faces": {"north": {"uv": [46, 41, 49, 49], "texture": 8}, "east": {"uv": [42, 41, 46, 49], "texture": 8}, "south": {"uv": [53, 41, 56, 49], "texture": 8}, "west": {"uv": [49, 41, 53, 49], "texture": 8}, "up": {"uv": [49, 41, 46, 37], "texture": 8}, "down": {"uv": [52, 37, 49, 41], "texture": 8}}, "type": "cube", "uuid": "c3987e08-1f5b-53d2-3c58-7f286b5534d4"}, {"name": "ArmLayers", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.4, 16.5, -2], "to": [-3.4000000000000004, 24.5, 2], "autouv": 0, "color": 7, "inflate": 0.25, "rotation": [0, 0, 1.25], "origin": [0, 10, 0], "uv_offset": [28, 37], "faces": {"north": {"uv": [32, 41, 35, 49], "texture": 8}, "east": {"uv": [28, 41, 32, 49], "texture": 8}, "south": {"uv": [39, 41, 42, 49], "texture": 8}, "west": {"uv": [35, 41, 39, 49], "texture": 8}, "up": {"uv": [35, 41, 32, 37], "texture": 8}, "down": {"uv": [38, 37, 35, 41], "texture": 8}}, "type": "cube", "uuid": "40aff086-8e9a-e2ff-797b-97d396b6d0a6"}, {"name": "Ch<PERSON><PERSON><PERSON><PERSON>", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 17, -2], "to": [3.5, 25, 2], "autouv": 0, "color": 8, "inflate": 0.25, "origin": [0, 10, 0], "uv_offset": [22, 16], "faces": {"north": {"uv": [26, 20, 33, 28], "texture": 8}, "east": {"uv": [22, 20, 26, 28], "texture": 8}, "south": {"uv": [37, 20, 44, 28], "texture": 8}, "west": {"uv": [33, 20, 37, 28], "texture": 8}, "up": {"uv": [33, 20, 26, 16], "texture": 8}, "down": {"uv": [40, 16, 33, 20], "texture": 8}}, "type": "cube", "uuid": "9da20f42-93c3-1c5d-62fa-c2d1ed782a67"}, {"name": "OuterScrubs", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 3.5, 0.25], "to": [3, 16.5, 2.25], "autouv": 0, "color": 0, "inflate": 0.2, "origin": [0, 10, 0], "uv_offset": [80, 64], "faces": {"north": {"uv": [82, 66, 88, 79], "texture": 8}, "east": {"uv": [80, 66, 82, 79], "texture": 8}, "south": {"uv": [90, 66, 96, 79], "texture": 8}, "west": {"uv": [88, 66, 90, 79], "texture": 8}, "up": {"uv": [88, 66, 82, 64], "texture": 8}, "down": {"uv": [94, 64, 88, 66], "texture": 8}}, "type": "cube", "uuid": "434a7a17-6dbf-830d-c2c7-10e675848e15"}, {"name": "OuterScrubs", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 3.5, -2.25], "to": [3, 16.5, -0.25], "autouv": 0, "color": 0, "inflate": 0.2, "origin": [0, 10, 0], "uv_offset": [52, 64], "faces": {"north": {"uv": [54, 66, 60, 79], "texture": 8}, "east": {"uv": [52, 66, 54, 79], "texture": 8}, "south": {"uv": [62, 66, 68, 79], "texture": 8}, "west": {"uv": [60, 66, 62, 79], "texture": 8}, "up": {"uv": [60, 66, 54, 64], "texture": 8}, "down": {"uv": [66, 64, 60, 66], "texture": 8}}, "type": "cube", "uuid": "e79fea88-9654-2ced-c04f-b7a3e8d73474"}, {"name": "OuterScrubs", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2.25, 3.5, -2], "to": [4.25, 16.5, 2], "autouv": 0, "color": 0, "mirror_uv": true, "inflate": 0.30000000000000004, "origin": [0, 10, 0], "uv_offset": [40, 62], "faces": {"north": {"uv": [46, 66, 44, 79], "texture": 8}, "east": {"uv": [50, 66, 46, 79], "texture": 8}, "south": {"uv": [52, 66, 50, 79], "texture": 8}, "west": {"uv": [44, 66, 40, 79], "texture": 8}, "up": {"uv": [44, 66, 46, 62], "texture": 8}, "down": {"uv": [46, 62, 48, 66], "texture": 8}}, "type": "cube", "uuid": "f7ffd0d1-dcfa-595a-d991-4985b37ae4a2"}, {"name": "OuterScrubs", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.25, 3.5, -2], "to": [-2.25, 16.5, 2], "autouv": 0, "color": 0, "inflate": 0.30000000000000004, "origin": [0, 10, 0], "uv_offset": [68, 62], "faces": {"north": {"uv": [72, 66, 74, 79], "texture": 8}, "east": {"uv": [68, 66, 72, 79], "texture": 8}, "south": {"uv": [78, 66, 80, 79], "texture": 8}, "west": {"uv": [74, 66, 78, 79], "texture": 8}, "up": {"uv": [74, 66, 72, 62], "texture": 8}, "down": {"uv": [76, 62, 74, 66], "texture": 8}}, "type": "cube", "uuid": "262423f2-6152-5d97-47db-0f2ba531a79f"}, {"name": "<PERSON><PERSON><PERSON>", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 25.5, -3.8], "to": [4, 27.5, -3.8], "autouv": 0, "color": 3, "origin": [0, 10, 0], "uv_offset": [8, 8], "faces": {"north": {"uv": [5, 2, 6, 4], "texture": 10}, "east": {"uv": [0, 0, 0, 0], "texture": null}, "south": {"uv": [0, 0, 0, 0], "texture": null}, "west": {"uv": [0, 0, 0, 0], "texture": null}, "up": {"uv": [0, 0, 0, 0], "texture": null}, "down": {"uv": [0, 0, 0, 0], "texture": null}}, "type": "cube", "uuid": "648dab10-151e-251a-8194-20d24cbd62ce"}, {"name": "Eyebawls", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 26, -3.9], "to": [-1, 27, -3.9], "autouv": 0, "color": 3, "origin": [0, 10, 0], "uv_offset": [8, 8], "faces": {"north": {"uv": [0, 0, 4, 4], "texture": 9}, "east": {"uv": [0, 0, 0, 0], "texture": null}, "south": {"uv": [0, 0, 0, 0], "texture": null}, "west": {"uv": [0, 0, 0, 0], "texture": null}, "up": {"uv": [0, 0, 0, 0], "texture": null}, "down": {"uv": [0, 0, 0, 0], "texture": null}}, "type": "cube", "uuid": "277cc04b-13ee-4da3-8c17-11228a3e888b"}, {"name": "Eyebawls", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1, 26, -3.9], "to": [2, 27, -3.9], "autouv": 0, "color": 3, "origin": [0, 10, 0], "uv_offset": [8, 8], "faces": {"north": {"uv": [4, 0, 0, 4], "texture": 9}, "east": {"uv": [0, 0, 0, 0], "texture": null}, "south": {"uv": [0, 0, 0, 0], "texture": null}, "west": {"uv": [0, 0, 0, 0], "texture": null}, "up": {"uv": [0, 0, 0, 0], "texture": null}, "down": {"uv": [0, 0, 0, 0], "texture": null}}, "type": "cube", "uuid": "1312b2da-acfd-7764-5dfc-2e7809915ef5"}, {"name": "HairBits", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 19.25, -4.5], "to": [3, 31.25, -2.5], "autouv": 0, "color": 0, "origin": [0, 10, 0], "uv_offset": [80, 0], "faces": {"north": {"uv": [82, 2, 88, 14], "texture": 8}, "east": {"uv": [80, 2, 82, 14], "texture": 8}, "south": {"uv": [90, 2, 96, 14], "texture": 8}, "west": {"uv": [88, 2, 90, 14], "texture": 8}, "up": {"uv": [88, 2, 82, 0], "texture": 8}, "down": {"uv": [94, 0, 88, 2], "texture": 8}}, "type": "cube", "uuid": "95d36fe0-5f95-2c7e-3146-2bcbf5737c49"}, {"name": "HairBits", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 18.25, -4.25], "to": [-3, 30.25, 0.75], "autouv": 0, "color": 0, "origin": [0, 10, 0], "uv_offset": [66, 17], "faces": {"north": {"uv": [71, 22, 73, 34], "texture": 8}, "east": {"uv": [66, 22, 71, 34], "texture": 8}, "south": {"uv": [78, 22, 80, 34], "texture": 8}, "west": {"uv": [73, 22, 78, 34], "texture": 8}, "up": {"uv": [73, 22, 71, 17], "texture": 8}, "down": {"uv": [75, 17, 73, 22], "texture": 8}}, "type": "cube", "uuid": "17465bcd-8d29-ef96-8aeb-7fad2330d74b"}, {"name": "HairBits", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3, 18.25, -4.25], "to": [5, 30.25, 0.75], "autouv": 0, "color": 0, "mirror_uv": true, "origin": [0, 10, 0], "uv_offset": [66, 0], "faces": {"north": {"uv": [73, 5, 71, 17], "texture": 8}, "east": {"uv": [78, 5, 73, 17], "texture": 8}, "south": {"uv": [80, 5, 78, 17], "texture": 8}, "west": {"uv": [71, 5, 66, 17], "texture": 8}, "up": {"uv": [71, 5, 73, 0], "texture": 8}, "down": {"uv": [73, 0, 75, 5], "texture": 8}}, "type": "cube", "uuid": "f2401a33-f673-31db-874c-e84e58647af3"}, {"name": "TheTape", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 0, -1.5], "to": [3, 1, 1.5], "autouv": 0, "color": 0, "origin": [0, 0, 0], "uv_offset": [32, 53], "faces": {"north": {"uv": [6, 75, 18, 77], "texture": 8}, "east": {"uv": [0, 75, 6, 77], "texture": 8}, "south": {"uv": [6, 77, 18, 79], "texture": 8}, "west": {"uv": [0, 77, 6, 79], "texture": 8}, "up": {"uv": [12, 75, 0, 69], "texture": 8}, "down": {"uv": [24, 69, 12, 75], "texture": 8}}, "type": "cube", "uuid": "7b456ac5-f235-8e50-1138-52781facc4d4"}], "outliner": [{"name": "TV", "origin": [0, 1, 0], "color": 0, "uuid": "177d7da3-67de-d888-246c-fc85b175cfe3", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": [{"name": "Housing", "origin": [0, 4, -3.5], "color": 0, "uuid": "a791bc95-a4fa-7528-6677-f92b989a176b", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": [{"name": "Buttons", "origin": [-4, 3, -4.6], "color": 0, "uuid": "6edff398-d2e8-bc80-fa50-ee9ab06e4f80", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["ac8fe40d-8582-198a-4760-7a79022b3e15", "2db5f9e5-34e2-8e64-9984-1fc214c00d0d", "c408c38d-7591-74f1-e9f1-f6e57293fdf6", "65422114-5a3b-6d40-8fac-42a8b6af4fb6", "940cf741-c327-3830-eeda-d7cd023566f4"]}, {"name": "<PERSON><PERSON><PERSON>", "origin": [-3.9999999999999996, 1.75, 4], "rotation": [-90, 0, 0], "color": 0, "uuid": "4fe73de0-0ae8-cae0-7f8e-5c420b865e5f", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["9238854b-d5ca-0df3-62e3-73308dc85253", {"name": "Antenna2", "origin": [-3.4999999999999996, 1.25, 4.15], "color": 0, "uuid": "845c089d-f6a8-3f14-5e18-2f2d348a4aab", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["72441fb2-35e3-e956-0b61-c6459705ab96", {"name": "Antenna3", "origin": [-3.2499999999999996, 1.25, 4.15], "color": 0, "uuid": "c4a3720b-43c5-d1d5-c524-a5bbf96d81c3", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["8317ebc9-9de9-c9c4-14ec-1498d8f5ad6c", {"name": "Antenna4", "origin": [-2.9999999999999996, 1.25, 4.15], "color": 0, "uuid": "8c89e849-8f94-26fe-b440-768a8db34420", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["187b337d-032b-86e3-266e-d8a411f40bab", "664d295b-c382-be33-91c3-31bed5077f20"]}]}]}]}, {"name": "Screen", "origin": [0, 8, -5.9], "color": 0, "uuid": "fa253189-e097-51de-1674-ac2c7c110c4d", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["33027ec2-73cf-1a6b-8119-82621a98061e", "d4b25cf8-d258-7559-1344-e8abcc8511db", {"name": "ScreenWell", "origin": [0, 8, -5.9], "color": 0, "uuid": "e455a48b-3b72-4687-fd92-ca0107317c2f", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["bbd5e52b-b770-cc36-fe61-8d90018ac22d"]}, {"name": "ScreenDark", "origin": [0, 8, -5.9], "color": 0, "uuid": "88edbc71-55c8-aa88-efea-c1925b339da5", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["20c32e37-344e-0e86-de5b-91c1957b34f1"]}]}, "9a3b6333-8e2e-050d-3b86-32d301b35caf", "ba24b29e-d7ac-6e89-e05a-5a56c8c26dea", "716c15bf-a9fc-b071-98fa-8aa681864ff4", "7c308bbb-fe66-81d0-b66e-17b8da271c93", "c247aaaf-9ea3-2355-16bd-a36aef5c599f", "27162795-bdbd-4185-acbc-e3c787c1616f"]}]}, {"name": "RingEyes", "origin": [0, 0, 0], "color": 0, "uuid": "ff855f96-5c65-888a-34f7-48443a374385", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": false, "autouv": 0, "children": []}, {"name": "Ring", "origin": [0, 1, 0], "color": 0, "uuid": "becce561-f622-7592-514f-5aede933e7be", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "Noggle", "origin": [0, 25, 0], "color": 0, "uuid": "d0f57d3a-5c99-127f-bb1e-dbd873765b47", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "RANGO", "origin": [0, 29, 0], "color": 0, "uuid": "407744b2-7e21-a6f0-e104-973f5932b900", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "Neck", "origin": [0, 25, 0], "color": 0, "uuid": "fd4c51f4-3e9f-0590-04fe-6d3a2c8a4ee8", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "NogginBits", "origin": [0, 25, 0], "color": 0, "uuid": "0ce83a3b-f3e0-b7d1-64e1-8f5fd4c9f064", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["b5092793-9f7e-7f27-a4fc-015b5c95642c", "01f6f18c-ca0f-e8e9-558e-2684cabba2d0", {"name": "Eyes", "origin": [0, 26, 0], "color": 0, "uuid": "8fa55186-2352-de04-4d0d-2e94622484c4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["648dab10-151e-251a-8194-20d24cbd62ce", {"name": "LeftEye", "origin": [-1.5, 26.5, -3.9], "color": 0, "uuid": "e5e69819-52db-c577-1116-b04b331d7732", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["277cc04b-13ee-4da3-8c17-11228a3e888b"]}, {"name": "RightEye", "origin": [1.5, 26.5, -3.9], "color": 0, "uuid": "36145320-9f7b-a7be-1d1c-06f5ef5a57f7", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["1312b2da-acfd-7764-5dfc-2e7809915ef5"]}]}, {"name": "TheVeil", "origin": [0, 26.25, 0], "color": 0, "uuid": "c419ee69-4273-618b-53a8-7c054f6c99a2", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "FrontVeil", "origin": [0, 31.25, -4.25], "rotation": [2.5, 0, 0], "color": 0, "uuid": "9d7c0b25-cc44-6a43-9602-09df292fbfff", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["95d36fe0-5f95-2c7e-3146-2bcbf5737c49"]}, {"name": "RightVeil", "origin": [4, 30.25, -4.25], "rotation": [1, -5, 0], "color": 0, "uuid": "f052b1a8-653c-610b-fba5-c6173a05c90b", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["f2401a33-f673-31db-874c-e84e58647af3"]}, {"name": "LeftVeil", "origin": [-4, 30.25, -4.25], "rotation": [1, 5, 0], "color": 0, "uuid": "9cb55e3c-c096-2ba6-c5bc-b3a31300ae63", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["17465bcd-8d29-ef96-8aeb-7fad2330d74b"]}]}]}]}, {"name": "Chest", "origin": [0, 25, 0], "color": 0, "uuid": "a55b555a-6578-72c7-1034-18bf620ffd9a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "TorsoBits", "origin": [0, 25, 0], "color": 0, "uuid": "dbf533e5-3f4a-b016-4a88-d6bb4d683ef8", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["90130b1f-e50b-6e5e-41be-ae10482f9690", "9da20f42-93c3-1c5d-62fa-c2d1ed782a67"]}, {"name": "Shoulders", "origin": [0, 23.5, 0], "color": 0, "uuid": "a247b8fc-3e11-172d-2179-4eebf17e6fe8", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "LeftShoulder", "origin": [-5, 23.5, 0], "rotation": [1.243587792468623e-17, -2.5, -5.000000000000001], "color": 0, "uuid": "3126f4e1-836b-4366-ae9f-f7f183d03b33", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["813ec64c-1d39-82b6-0a99-25a74102a8cd", "40aff086-8e9a-e2ff-797b-97d396b6d0a6"]}, {"name": "RightShoulder", "origin": [5, 23.5, 0], "rotation": [2.487175584937246e-17, 2.5, 5.000000000000005], "color": 0, "uuid": "b5cb972a-0c76-6ec0-1d8c-978a07c87f24", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["f7576f2c-b1cf-884b-b649-bbadf805dd39", "c3987e08-1f5b-53d2-3c58-7f286b5534d4"]}]}, {"name": "MidSection", "origin": [0, 17, 0], "color": 0, "uuid": "069d4014-e378-cde6-ed3f-3b8fc92c9750", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "MidBits", "origin": [0, 17, 0], "color": 0, "uuid": "9f56a887-e8c5-e26b-edc9-984f2603b96e", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["458a0340-3768-9af7-18c2-d65d023fbf61"]}, {"name": "<PERSON><PERSON><PERSON>", "origin": [0, 16.5, 0], "color": 0, "uuid": "39b607ae-53e6-6156-41f8-11a554e1e77f", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "FrontScrubs", "origin": [0, 16.5, -2], "rotation": [5, 0, 0], "color": 0, "uuid": "*************-d6e3-2315-015aa3b6fb4d", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["5e745e95-8cf1-a698-711c-a7d7acbe53e8", "90e96f79-48e0-02cd-645b-5818d1a7de0e", "f2cfbe08-1fdd-8c5d-6b8e-08699b8a1cce", "e79fea88-9654-2ced-c04f-b7a3e8d73474"]}, {"name": "BackScrubs", "origin": [0, 16.5, 2], "rotation": [-5, 0, 0], "color": 0, "uuid": "a8267e42-e1ec-7aa4-67ce-f3079d498125", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["41aaedb6-cc1e-6512-c94e-a9b1d252d6a7", "6ca6582d-3208-dcd6-3434-4d1b5833c8e2", "4747fc4d-bf85-4f5b-ac06-7a46fe740b5a", "434a7a17-6dbf-830d-c2c7-10e675848e15"]}, {"name": "LeftScrubs", "origin": [-4, 16.5, 0], "rotation": [0, 0, -5], "color": 0, "uuid": "07f1c422-136b-88b2-3301-0d1a14dd6560", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["c58f576a-33ff-990b-23d3-d7f8f1b2f31c", "262423f2-6152-5d97-47db-0f2ba531a79f"]}, {"name": "RightScrubs", "origin": [4, 16.5, 0], "rotation": [0, 0, 5], "color": 0, "uuid": "fd2c92eb-7659-a0b9-2254-118d0dad40f5", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["d01c66af-f90d-ff6c-d6ce-2b6c131db425", "f7ffd0d1-dcfa-595a-d991-4985b37ae4a2"]}]}, {"name": "Legs", "origin": [0, 12, 0], "color": 0, "uuid": "0c083c53-995e-71ab-f3e1-dcb04bd31c4a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "LeftHip", "origin": [-2, 12, 0], "color": 0, "uuid": "243936f8-b9c6-d179-6a92-d5c9a01b4340", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0af6065b-9df4-e600-b1fa-ae585b4e6d2a"]}, {"name": "RightHip", "origin": [2, 12, 0], "color": 0, "uuid": "ce3e2383-010d-03b5-2c18-1f82ea6055d8", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["4850a516-f058-fb83-20cc-b503904ef2b9"]}]}]}]}]}]}, {"name": "Skull", "origin": [0, 0, 0], "rotation": [0, 17.5, 0], "color": 0, "uuid": "3e15a505-0e06-8244-c549-db28cc757185", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["7b456ac5-f235-8e50-1138-52781facc4d4"]}], "textures": [{"path": "C:\\Users\\<USER>\\Desktop\\Figura Models\\InGameAvatars\\avatars\\InTheWorks\\Okiku\\textures\\TV.png", "name": "TV.png", "folder": "", "namespace": "", "id": "3", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "60055257-b84a-2037-be91-3991e4c3f3fb", "relative_path": "textures/TV.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\Desktop\\Figura Models\\InGameAvatars\\avatars\\InTheWorks\\Okiku\\textures\\Screen0.png", "name": "Screen0.png", "folder": "", "namespace": "", "id": "5", "width": 12, "height": 9, "uv_width": 12, "uv_height": 9, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "a63cad28-b4b4-d6f1-06cb-230a510484ff", "relative_path": "textures/Screen0.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAJCAYAAAAGuM1UAAAAAXNSR0IArs4c6QAAAH5JREFUKFONUMsOwzAIs9NOWhj//7ljwiRdll3KJQr4BXz2HrhZJMFuJgIB7My9J8LLPRABMMejkrl81RWEoJklXJ8LL6vyU38INrZvpDtrPM6zIsliKoKIv23KqfEA3T3exbiKg5RvSdVckeYOP4z9CKtYnjUzTdXVZz90Yj6ueSs57ded/AAAAABJRU5ErkJggg=="}, {"path": "C:\\Users\\<USER>\\Desktop\\Figura Models\\InGameAvatars\\avatars\\InTheWorks\\Okiku\\textures\\Screen1.png", "name": "Screen1.png", "folder": "", "namespace": "", "id": "6", "width": 12, "height": 9, "uv_width": 12, "uv_height": 9, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "6a81ff23-22a7-4c3a-dc1a-af56c05b99fc", "relative_path": "textures/Screen1.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAJCAYAAAAGuM1UAAAAAXNSR0IArs4c6QAAASlJREFUKFM1kU1KBEEMhb9UdfdMOyqoGxcezxEcceVS0DOICy8gLryC4NIbeA7Rsf+qKxWpUgOBkPDyXl5kfboxMBDHomkQ51BVhmEghwEiUmrvHXK2vjSzRO4t2hbTBAKaFI2KpVRA/yGb8ytDjIOjQ6qmIc6BFJU5zozDSFJlngNmRspC7h8erW1bjk+OSSbM01QY4jTRdR3d95bt12dh8d4jr2/vttrdYbW3j6s8MQRcHhTtjrHvfuUlpd9ukafnF9M4kyRvEMIYCrBuFqzalv4PgBkhBOTm9s7GoWOcBjRFMtI5R900VFVdbooaqSqPF49cbK4tzCNqmTZROY8TV6zMGVVJ2SnTYrKcra9sub/D1+cHfT/gnf/1PX/HEpZLM5Z1TV15fgDwGqpNrQAWBAAAAABJRU5ErkJggg=="}, {"path": "C:\\Users\\<USER>\\Desktop\\Figura Models\\InGameAvatars\\avatars\\InTheWorks\\Okiku\\textures\\Screen_e.png", "name": "Screen1_e.png", "folder": "", "namespace": "", "id": "6", "width": 12, "height": 9, "uv_width": 12, "uv_height": 9, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "0db90cea-2cb2-6c9f-60f9-32367993adbe", "relative_path": "textures/Screen_e.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAJCAYAAAAGuM1UAAAAAXNSR0IArs4c6QAAASlJREFUKFM1kU1KBEEMhb9UdfdMOyqoGxcezxEcceVS0DOICy8gLryC4NIbeA7Rsf+qKxWpUgOBkPDyXl5kfboxMBDHomkQ51BVhmEghwEiUmrvHXK2vjSzRO4t2hbTBAKaFI2KpVRA/yGb8ytDjIOjQ6qmIc6BFJU5zozDSFJlngNmRspC7h8erW1bjk+OSSbM01QY4jTRdR3d95bt12dh8d4jr2/vttrdYbW3j6s8MQRcHhTtjrHvfuUlpd9ukafnF9M4kyRvEMIYCrBuFqzalv4PgBkhBOTm9s7GoWOcBjRFMtI5R900VFVdbooaqSqPF49cbK4tzCNqmTZROY8TV6zMGVVJ2SnTYrKcra9sub/D1+cHfT/gnf/1PX/HEpZLM5Z1TV15fgDwGqpNrQAWBAAAAABJRU5ErkJggg=="}, {"path": "C:\\Users\\<USER>\\Desktop\\Figura Models\\InGameAvatars\\avatars\\InTheWorks\\Okiku\\textures\\Screen2.png", "name": "Screen2.png", "folder": "block", "namespace": "", "id": "7", "width": 48, "height": 36, "uv_width": 48, "uv_height": 36, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "955c61ff-d455-1a9e-4561-6d2d6aca1980", "relative_path": "textures/Screen2.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\Desktop\\Figura Models\\InGameAvatars\\avatars\\InTheWorks\\Okiku\\textures\\Screen2_e.png", "name": "Screen2_e.png", "folder": "block", "namespace": "", "id": "7", "width": 48, "height": 36, "uv_width": 48, "uv_height": 36, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "3f94ed24-9b45-f8a6-e0e0-e593b9754573", "relative_path": "textures/Screen2_e.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\Desktop\\Figura Models\\InGameAvatars\\avatars\\InTheWorks\\Okiku\\textures\\METS.png", "name": "METS.png", "folder": "", "namespace": "", "id": "9", "width": 48, "height": 36, "uv_width": 48, "uv_height": 36, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "cafcdd5a-a3b7-04e7-592b-afb905bb83a6", "relative_path": "textures/METS.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\Desktop\\Figura Models\\InGameAvatars\\avatars\\InTheWorks\\Okiku\\textures\\METS_e.png", "name": "METS_e.png", "folder": "", "namespace": "", "id": "9", "width": 48, "height": 36, "uv_width": 48, "uv_height": 36, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "b70dab92-97b1-38e8-6f1a-8a5a5ce9ffb9", "relative_path": "textures/METS_e.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\Desktop\\Figura Models\\InGameAvatars\\avatars\\InTheWorks\\Okiku\\textures\\TheRing.png", "name": "TheRing.png", "folder": "block", "namespace": "", "id": "8", "width": 96, "height": 96, "uv_width": 96, "uv_height": 96, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "427d87b9-4d52-1d35-17a2-0599ce99cb66", "relative_path": "textures/TheRing.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\Desktop\\Figura Models\\InGameAvatars\\avatars\\InTheWorks\\Okiku\\textures\\Eyes.png", "name": "Eyes.png", "folder": "block", "namespace": "", "id": "10", "width": 8, "height": 4, "uv_width": 8, "uv_height": 4, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "bdc4b0ef-8146-a9cb-26b1-17034d8791e2", "relative_path": "textures/Eyes.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAYAAACzzX7wAAAAAXNSR0IArs4c6QAAAGhJREFUGFdjZGBgYNDzCvivoqoKYjKsm9jNCGZAAaOwT/h/6X8/GR6/fsPgaGWJqUDU1uX/nx8/GGRFRcB6Lm3bwBhQVvF/Q1cH2CRGAXXt/6xikmDJf/zCDG+3rGSMb2z+v7C+FqwAAMHPIQW/Q0FmAAAAAElFTkSuQmCC"}, {"path": "C:\\Users\\<USER>\\Desktop\\Figura Models\\InGameAvatars\\avatars\\InTheWorks\\Okiku\\textures\\Eyes_e.png", "name": "Eyes_e.png", "folder": "block", "namespace": "", "id": "10", "width": 8, "height": 4, "uv_width": 8, "uv_height": 4, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "53ede7cf-1ac1-2476-ef0c-b950e4eef566", "relative_path": "textures/Eyes_e.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAYAAACzzX7wAAAAAXNSR0IArs4c6QAAAGhJREFUGFdjZGBgYNDzCvivoqoKYjKsm9jNCGZAAaOwT/h/6X8/GR6/fsPgaGWJqUDU1uX/nx8/GGRFRcB6Lm3bwBhQVvF/Q1cH2CRGAXXt/6xikmDJf/zCDG+3rGSMb2z+v7C+FqwAAMHPIQW/Q0FmAAAAAElFTkSuQmCC"}], "animations": [{"uuid": "7c16e698-7daf-0a80-60e9-dd10f0f2822e", "name": "Flicker", "loop": "loop", "override": false, "length": 0.25, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"fa253189-e097-51de-1674-ac2c7c110c4d": {"name": "Screen", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f1bedb0c-b37c-e31b-4262-c7901d6faa52", "time": 0, "color": -1, "interpolation": "step"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "f46916af-d4e6-ff6b-33db-c1c483066531", "time": 0, "color": -1, "uniform": true, "interpolation": "step"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "6b4978ea-3c93-837a-7864-55c0235a871e", "time": 0.25, "color": -1, "uniform": true, "interpolation": "step"}, {"channel": "scale", "data_points": [{"x": -1, "y": -1, "z": "1"}], "uuid": "9f9755ca-e7e7-bdf3-1024-b032ae30d64f", "time": 0.125, "color": -1, "uniform": false, "interpolation": "step"}]}}}, {"uuid": "583580d7-3374-6b1d-21e1-5f4da7f53b60", "name": "WellWellWell", "loop": "loop", "override": false, "length": 2.5, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"e455a48b-3b72-4687-fd92-ca0107317c2f": {"name": "ScreenWell", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0.1, "y": "0", "z": "0"}], "uuid": "ea68bb4a-417b-a507-73d1-754056b003d6", "time": 0, "color": -1, "interpolation": "step"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "53baf5ea-3e47-10c1-ea6a-4bd8e89c4d48", "time": 0.041666666666666664, "color": -1, "interpolation": "step"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f916bd99-**************-cfc657a87aae", "time": 1.25, "color": -1, "interpolation": "step"}, {"channel": "position", "data_points": [{"x": -0.09999999999999992, "y": 0, "z": 0}], "uuid": "4e12facf-d12b-3375-2014-9cde917ab404", "time": 2.25, "color": -1, "interpolation": "step"}, {"channel": "position", "data_points": [{"x": 0.1, "y": "0", "z": "0"}], "uuid": "05c4bded-1b4a-286a-1068-db3694faa481", "time": 2.5, "color": -1, "interpolation": "step"}, {"channel": "position", "data_points": [{"x": -0.1999999999999999, "y": "0", "z": "0"}], "uuid": "7811988e-4db4-c351-5e79-5b334668a834", "time": 0.75, "color": -1, "interpolation": "step"}, {"channel": "position", "data_points": [{"x": 0.1, "y": "0", "z": "0"}], "uuid": "b60d6917-3364-e0aa-a450-738473fc5f0c", "time": 0.08333333333333333, "color": -1, "interpolation": "step"}, {"channel": "position", "data_points": [{"x": 0.1, "y": "0", "z": "0"}], "uuid": "c6a637f6-f9f7-2952-8619-673d02148d0a", "time": 0.7916666666666666, "color": -1, "interpolation": "step"}, {"channel": "position", "data_points": [{"x": 0.1, "y": "0", "z": "0"}], "uuid": "eb38e534-e21b-2b1d-38c4-6520cf45ff1e", "time": 1.2916666666666667, "color": -1, "interpolation": "step"}, {"channel": "position", "data_points": [{"x": 0.1, "y": "0", "z": "0"}], "uuid": "7b9319f1-cac7-4d17-fdac-5fb84f3a3f74", "time": 1.5, "color": -1, "interpolation": "step"}, {"channel": "scale", "data_points": [{"x": 1.02, "y": "1", "z": "1"}], "uuid": "12341860-6d8b-ae7c-46a9-8f0a8d9b82b0", "time": 0, "color": -1, "uniform": false, "interpolation": "step"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "e419cf49-1fe6-d4d9-c368-10f21cab4122", "time": 0.041666666666666664, "color": -1, "uniform": false, "interpolation": "step"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "f82de9d9-c9e6-b2a1-31ac-0f3837c52368", "time": 1.25, "color": -1, "uniform": false, "interpolation": "step"}, {"channel": "scale", "data_points": [{"x": 1.03, "y": 1, "z": 1}], "uuid": "7146d7fe-7a36-db2f-4970-28e4595eda84", "time": 2.25, "color": -1, "uniform": false, "interpolation": "step"}, {"channel": "scale", "data_points": [{"x": 1.02, "y": "1", "z": "1"}], "uuid": "98a8c418-e623-b223-f8d1-e80a54cd9ec0", "time": 2.5, "color": -1, "uniform": false, "interpolation": "step"}, {"channel": "scale", "data_points": [{"x": 1.07, "y": "1", "z": "1"}], "uuid": "dff41377-3a02-59e5-f426-c0b4db8d20bb", "time": 0.75, "color": -1, "uniform": false, "interpolation": "step"}, {"channel": "scale", "data_points": [{"x": 1.02, "y": "1", "z": "1"}], "uuid": "ed7cfcad-0f46-aefc-6b14-89e07f12da68", "time": 0.08333333333333333, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1.02, "y": "1", "z": "1"}], "uuid": "9ac80594-cf76-2d67-d68c-47c7b0cd291f", "time": 0.7916666666666666, "color": -1, "uniform": false, "interpolation": "step"}, {"channel": "scale", "data_points": [{"x": 1.02, "y": "1", "z": "1"}], "uuid": "751178a7-b358-0764-18a0-2c23f55cd90b", "time": 1.2916666666666667, "color": -1, "uniform": false, "interpolation": "step"}, {"channel": "scale", "data_points": [{"x": 1.02, "y": "1", "z": "1"}], "uuid": "5f8b4818-2961-b7e7-1230-9e7e0f9fbf23", "time": 1.5, "color": -1, "uniform": false, "interpolation": "step"}]}}}, {"uuid": "20146593-a4bb-cc57-3b61-5ff0f271b6ef", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loop": "loop", "override": false, "length": 5, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"177d7da3-67de-d888-246c-fc85b175cfe3": {"name": "TV", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "4cc3b901-5dfd-780b-4326-195cd9065886", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -7.5, "y": "1", "z": 0}], "uuid": "0d31dec5-f652-3364-b2de-75c924839595", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "7.5", "y": "2.5", "z": 0}], "uuid": "be79010e-333d-0206-5bca-8100700900c7", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "37fd10f0-e555-d2ad-7df8-c656e3cadd8f", "time": 2, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -2.5, "y": 5, "z": 2.5}], "uuid": "b4d6ae82-756d-e132-c997-64e20d573251", "time": 1.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "5", "z": "0"}], "uuid": "424f67a5-6fe2-aa2e-2bd4-db75def474c2", "time": 1.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -3.5, "y": 5, "z": 15}], "uuid": "018c83b9-40c5-83ee-ee41-eacaa272ce80", "time": 2.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": -5, "z": "0"}], "uuid": "a9c88677-9969-f071-48e5-ff99393ba22b", "time": 3, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": -5, "z": "0"}], "uuid": "e7c7291a-4810-a625-bf9a-97a2e7559e2a", "time": 4.25, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "a35fe048-5492-ec40-48d1-c7719152fe9b", "time": 4.75, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "10", "y": "5", "z": "-12.5"}], "uuid": "73930be2-f088-55e8-dbda-1549266fd032", "time": 4.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "5", "z": "0"}], "uuid": "158e2fc9-f20b-aaf9-5934-e1618507c6c3", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "e46acf61-0729-3b9f-3126-66b0a25c0cfe", "time": 2.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": -5, "z": "0"}], "uuid": "084eeb2d-c809-a211-4b18-a384d19fb5da", "time": 3.25, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -10, "y": -5, "z": 0}], "uuid": "7a73d393-9e8a-42fd-01bb-b5bdd7fbef68", "time": 3.4166666666666665, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": -5, "z": "0"}], "uuid": "fe5ea32a-c4a7-8f3b-18ba-b0666ba73d50", "time": 3.75, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": -5, "z": "0"}], "uuid": "442b406d-bd1c-900b-1d55-d0e09c30f198", "time": 4, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 7.5, "y": -5, "z": 0}], "uuid": "ecf89d48-710b-d0a0-5cd1-ea2dde864be7", "time": 3.5833333333333335, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": 0.75, "z": 0}], "uuid": "2420bb01-e1e2-94ed-1bac-2b9b2954541c", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": 0.5, "z": 0}], "uuid": "baa6def5-4b6a-c7c5-274a-76f400a147fb", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "9b056cdc-716f-e495-30b2-419bcdadc890", "time": 0.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "763d4717-fab7-1074-1864-eeac731b73f0", "time": 2, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "a55534fd-9be1-848a-3e4a-cd856f412bc3", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -0.25, "y": "0", "z": 0}], "uuid": "0b3fe692-7762-91bc-50db-931c4a13676a", "time": 1.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 1, "y": 1, "z": 1}], "uuid": "78637dfb-5cc3-1b06-d4d7-9da676356179", "time": 2.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 1.5, "y": "0", "z": 0.75}], "uuid": "e5c30042-8471-910c-17fa-c6135b1ae1c0", "time": 3, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 1.5, "y": "0", "z": 0.75}], "uuid": "6a4dfca8-f91a-4b38-e342-89f4a97a9920", "time": 4.25, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "23603a0d-2663-310b-f246-83720c63e96a", "time": 4.75, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0.5, "y": 1.25, "z": 0.5}], "uuid": "b323d3a3-3c2f-9a4e-b820-ccc3c50756d4", "time": 4.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -0.25, "y": "0", "z": 0}], "uuid": "40893add-b6ab-4702-d9dd-5beeeb36258b", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7c89e66f-8a74-d9af-205f-b5cfdba5d2b5", "time": 2.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 1.5, "y": "0", "z": 0.75}], "uuid": "4b04a1e4-9cc3-f489-7fbb-00aee73656d8", "time": 3.25, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 1.5, "y": 1, "z": 0.75}], "uuid": "fc8b77ef-f4dd-47a7-0e28-a3647ad31b04", "time": 3.4166666666666665, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 1.5, "y": "0", "z": 0.75}], "uuid": "b516ce71-f750-b2f5-aa38-7847c1144418", "time": 3.75, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 1.5, "y": "0", "z": 0.75}], "uuid": "a1105cbe-e36a-fe37-e4d8-2f9d49564f25", "time": 4, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 1.5, "y": 0.5, "z": 0.75}], "uuid": "64a6e1ca-1e9e-3f0b-d626-3ecfb0fe2778", "time": 3.5833333333333335, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 1.5, "y": "0", "z": 0.75}], "uuid": "4ad53def-8c67-1c66-58dd-4557a825fb5e", "time": 3.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -0.12, "y": 0.5, "z": 0}], "uuid": "f49a61b3-7ee2-8c32-62d4-71dc8a9e430c", "time": 1.75, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "d9b642ce-3ada-58dd-8d75-c7975fc84199", "name": "THEMETS", "loop": "hold", "override": false, "length": 1.25, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"effects": {"name": "Effects", "type": "effect", "keyframes": [{"channel": "timeline", "data_points": [{"script": "sounds:playSound(\"entity.vex.charge\", player:get<PERSON><PERSON>(), 0.1, .6)"}], "uuid": "9a0906e9-0afa-2143-5397-a8fa687e200a", "time": 0.75, "color": -1, "interpolation": "linear"}, {"channel": "timeline", "data_points": [{"script": "sounds:playSound(\"block.note_block.cow_bell\", player:get<PERSON><PERSON>(), 0.7, 2.5)"}], "uuid": "9ddb7bf0-e98e-c4cc-ff40-b35759493a60", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "timeline", "data_points": [{"script": "sounds:playSound(\"entity.vex.charge\", player:get<PERSON><PERSON>(), 0.2, .9)"}], "uuid": "5896ee6a-6fa2-8710-89ad-2ed9a55a78e1", "time": 1.2083333333333333, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "6a7dc0b1-83c5-c4cb-ffc8-9ea0e647116f", "name": "AntennaUp", "loop": "hold", "override": false, "length": 1, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"4fe73de0-0ae8-cae0-7f8e-5c420b865e5f": {"name": "<PERSON><PERSON><PERSON>", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "ac3ce347-3cb2-4151-8ce6-43f3224efb45", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": 7.448215910699219e-08, "z": 100}], "uuid": "eeaa9d8c-c814-1844-5890-a050c3074fdf", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 85, "y": 0, "z": 100}], "uuid": "6402dab4-c7d9-8ada-7a57-60f4d8eb255d", "time": 1, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": 0.25}], "uuid": "28497ce5-7d81-eb1c-b7b8-5b75a2b3020f", "time": 0.6666666666666666, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1941984649253287], "bezier_left_value": [0, 0, -0.3692307692307692], "bezier_right_time": [0.1, 0.1, 0.1941984649253287], "bezier_right_value": [0, 0, 0.3692307692307692]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "37dca64a-fd41-0b19-93a9-a881bdf572a3", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.09827271970012495], "bezier_left_value": [0, 0, -0.27692307692307694], "bezier_right_time": [0.1, 0.1, 0.09827271970012495], "bezier_right_value": [0, 0, 0.27692307692307694]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": 1}], "uuid": "96b9e67e-8fe8-01af-3263-b8f90bba1a91", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.11776631165585769], "bezier_left_value": [0, 0, 0.08076923076923076], "bezier_right_time": [0.1, 0.1, 0.11776631165585769], "bezier_right_value": [0, 0, -0.08076923076923076]}]}, "845c089d-f6a8-3f14-5e18-2f2d348a4aab": {"name": "Antenna2", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "689ccd80-5d54-b822-a6a8-822f37933c5c", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "a498a7d7-e6b9-e699-45a9-5242d8ec3c88", "time": 1, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -20}], "uuid": "407de695-74da-bfb2-c2be-6bc00ee40da7", "time": 0.5, "color": -1, "interpolation": "catmullrom"}]}, "c4a3720b-43c5-d1d5-c524-a5bbf96d81c3": {"name": "Antenna3", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": -7, "y": "0", "z": "0"}], "uuid": "ed55c23e-ac64-5ff3-8229-07bfed3566d5", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "8f86fb3a-2821-360a-67c1-c1ecd66716d2", "time": 0.5, "color": -1, "interpolation": "linear"}]}, "8c89e849-8f94-26fe-b440-768a8db34420": {"name": "Antenna4", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": -7, "y": "0", "z": "0"}], "uuid": "e06abfe2-2754-d93d-4d02-c1ba6d5638b2", "time": 0.8333333333333334, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "e11f0efe-f78d-f93b-a5e9-287807c0af1c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "fa253189-e097-51de-1674-ac2c7c110c4d": {"name": "Screen", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": "0", "z": -0.1}], "uuid": "1a87e196-e521-18b7-8d3e-4382946b05cc", "time": 0.5, "color": -1, "interpolation": "step"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "ca33a6dd-7e28-adc7-a8cc-a1b573a57042", "time": 0.041666666666666664, "color": -1, "interpolation": "step"}]}, "effects": {"name": "Effects", "type": "effect", "keyframes": [{"channel": "timeline", "data_points": [{"script": "sounds:playSound(\"item.spyglass.use\", player:get<PERSON>os(),0.5, .5)"}], "uuid": "e35f756d-3f56-1f50-3ad3-af0199e14f20", "time": 0.25, "color": -1, "interpolation": "linear"}, {"channel": "timeline", "data_points": [{"script": "sounds:playSound(\"block.note_block.basedrum\", player:getPos(), 0.1, 5)"}], "uuid": "77c3891e-d922-e04d-3eaa-dff74b0b7602", "time": 0.5, "color": -1, "interpolation": "linear"}]}, "88edbc71-55c8-aa88-efea-c1925b339da5": {"name": "ScreenDark", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": "0", "z": 0.24999999999999994}], "uuid": "604729e1-0bd9-bc4a-5ed8-bac7237989fa", "time": 0.5, "color": -1, "interpolation": "step"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "5ba350e1-95db-d4a8-72d8-6fbf3e1d194a", "time": 0.041666666666666664, "color": -1, "interpolation": "step"}]}}}, {"uuid": "851c3111-330b-d4da-408f-08f3eab1d3a2", "name": "AntennaDown", "loop": "hold", "override": false, "length": 0.75, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"4fe73de0-0ae8-cae0-7f8e-5c420b865e5f": {"name": "<PERSON><PERSON><PERSON>", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "ac3ce347-3cb2-4151-8ce6-43f3224efb45", "time": 0.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": 7.448215910699219e-08, "z": 100}], "uuid": "eeaa9d8c-c814-1844-5890-a050c3074fdf", "time": 0.33333333333333337, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 85, "y": 0, "z": 100}], "uuid": "6402dab4-c7d9-8ada-7a57-60f4d8eb255d", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": 0.25}], "uuid": "28497ce5-7d81-eb1c-b7b8-5b75a2b3020f", "time": 0.33333333333333337, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1941984649253287], "bezier_left_value": [0, 0, 0.3692307692307692], "bezier_right_time": [0.1, 0.1, 0.1941984649253287], "bezier_right_value": [0, 0, -0.3692307692307692]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "37dca64a-fd41-0b19-93a9-a881bdf572a3", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.09827271970012495], "bezier_left_value": [0, 0, 0.27692307692307694], "bezier_right_time": [0.1, 0.1, 0.09827271970012495], "bezier_right_value": [0, 0, -0.27692307692307694]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": 1}], "uuid": "96b9e67e-8fe8-01af-3263-b8f90bba1a91", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.11776631165585769], "bezier_left_value": [0, 0, -0.08076923076923076], "bezier_right_time": [0.1, 0.1, 0.11776631165585769], "bezier_right_value": [0, 0, 0.08076923076923076]}]}, "845c089d-f6a8-3f14-5e18-2f2d348a4aab": {"name": "Antenna2", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "689ccd80-5d54-b822-a6a8-822f37933c5c", "time": 0.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "a498a7d7-e6b9-e699-45a9-5242d8ec3c88", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -20}], "uuid": "407de695-74da-bfb2-c2be-6bc00ee40da7", "time": 0.5, "color": -1, "interpolation": "catmullrom"}]}, "c4a3720b-43c5-d1d5-c524-a5bbf96d81c3": {"name": "Antenna3", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": -7, "y": "0", "z": "0"}], "uuid": "ed55c23e-ac64-5ff3-8229-07bfed3566d5", "time": 0.33333333333333337, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "8f86fb3a-2821-360a-67c1-c1ecd66716d2", "time": 0.5, "color": -1, "interpolation": "linear"}]}, "8c89e849-8f94-26fe-b440-768a8db34420": {"name": "Antenna4", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": -7, "y": "0", "z": "0"}], "uuid": "e06abfe2-2754-d93d-4d02-c1ba6d5638b2", "time": 0.16666666666666663, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "e11f0efe-f78d-f93b-a5e9-287807c0af1c", "time": 0.33333333333333337, "color": -1, "interpolation": "linear"}]}, "effects": {"name": "Effects", "type": "effect", "keyframes": [{"channel": "timeline", "data_points": [{"script": "sounds:playSound(\"item.spyglass.use\", player:getPos(),0.5, .3)\nsounds:playSound(\"block.note_block.basedrum\", player:getPos(), 0.1, 3.5)\n"}], "uuid": "3ba7110d-0063-133e-b50d-a7376bbccdca", "time": 0, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "5a04c87a-a8a4-f893-c3a1-a0761738a3d1", "name": "Uppies", "loop": "hold", "override": false, "length": 8, "snapping": 24, "selected": true, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"177d7da3-67de-d888-246c-fc85b175cfe3": {"name": "TV", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -90, "y": "0", "z": "0"}], "uuid": "b8ba701e-cf39-456e-b5fb-56f050156ab7", "time": 1.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0aaded61-fa4e-af86-4615-6993268b086d", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -27.5, "y": "0", "z": "0"}], "uuid": "6e929213-49c7-bac1-abe2-d164face3f9b", "time": 0.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -52.5, "y": "0", "z": "0"}], "uuid": "2e7cc26f-af82-3fb6-824f-451392150fcd", "time": 1, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -90, "y": "0", "z": "0"}], "uuid": "ee2341f5-d9da-2efc-02ed-8d29c38ed75c", "time": 1.3333333333333333, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -90, "y": "0", "z": "0"}], "uuid": "8c78799b-9be4-df00-8add-9ce396a8b076", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -82.5, "y": "0", "z": "0"}], "uuid": "cfa8f09a-8808-f2d8-762b-0d6059ec3caf", "time": 7, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -85, "y": "0", "z": "0"}], "uuid": "1b4e43a6-bace-857f-6b19-0b6c7e00f24e", "time": 7.25, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -85, "y": "0", "z": "0"}], "uuid": "338acdac-e27f-0714-3eaa-afebde36a8c1", "time": 7.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "31c2c66f-4444-a4f7-32a8-6b98ae45943d", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 2, "z": 0.5}], "uuid": "0ac5d4f9-54f7-00c9-5115-ae14391e26dc", "time": 0.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 3.25, "z": -2.5}], "uuid": "eb2cc508-0481-acf0-84db-37e8f7f40c14", "time": 1, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 4.5, "z": "0"}], "uuid": "ba484e1d-6157-825f-a4cb-8205c3cc2801", "time": 1.3333333333333333, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 4.5, "z": "0"}], "uuid": "62dc2ed6-46ba-239f-a50b-************", "time": 1.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 4.5, "z": "0"}], "uuid": "78ef99aa-4777-c189-67c4-37477914dac7", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 3.75, "z": "0"}], "uuid": "c9c91c87-52a1-7882-6bba-bfa4cd47a212", "time": 7, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": 4, "z": "0"}], "uuid": "ee9bd8c8-b805-eea0-5909-bdd1e91776a7", "time": 7.25, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": 4, "z": "0"}], "uuid": "31459e4f-5ff1-2d6f-2074-57bccca9fc3f", "time": 7.5, "color": -1, "interpolation": "linear"}]}, "845c089d-f6a8-3f14-5e18-2f2d348a4aab": {"name": "Antenna2", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "2c1d0333-8c98-a32b-9723-fecd8eeed87a", "time": 1.3333333333333333, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "20e6e8d3-a497-30a8-3b13-35d493ca30de", "time": 1.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 2.5, "z": 0}], "uuid": "6b0f3680-fbec-7455-93ff-8e511c0344e6", "time": 1.4166666666666667, "color": -1, "interpolation": "catmullrom"}]}, "d0f57d3a-5c99-127f-bb1e-dbd873765b47": {"name": "Noggle", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 1, "y": -23, "z": 6}], "uuid": "000aad0a-de23-3894-952f-20d3337fa67d", "time": 2, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 1, "y": -19, "z": 6}], "uuid": "534e80c4-6cfd-89bd-44db-3fd4251ac32f", "time": 2.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -1, "y": -12, "z": 6}], "uuid": "70a3f9f7-ee24-d546-8225-6ac34183d164", "time": 3, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -1, "y": -9, "z": 6}], "uuid": "7841d262-2cfb-e855-4552-8cfe3fefdfe9", "time": 3.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0.5, "y": -8, "z": 6}], "uuid": "57c5af49-4a39-3735-7c8b-18948d4a79c8", "time": 3.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 6}], "uuid": "41e9d7e3-8cd9-b5b9-4829-ced9f3c9d8b2", "time": 5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 6}], "uuid": "108169fc-7a0c-07fb-26a9-c2e6d698d75b", "time": 5.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 6}], "uuid": "7bdbe1c7-2e9a-591e-e5d5-cc563770a9b5", "time": 4.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 5.5, "z": 7}], "uuid": "e7dba195-a849-9b5c-8a72-3c83b35cf3c1", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 4.67, "z": 2}], "uuid": "63cbd354-b0a0-598d-7d2e-3b9d534aa1e8", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 8.5, "z": -2.58}], "uuid": "7c27fe8c-4a15-eead-4be1-af94c7960312", "time": 7, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 11, "z": "0.75"}], "uuid": "1689c01f-800e-d0ad-cbec-5614c9cbf225", "time": 7.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": 10.5, "z": "0.75"}], "uuid": "a99a4ec6-97b5-e958-3e9a-59652946728e", "time": 7.75, "color": -1, "interpolation": "linear"}]}, "effects": {"name": "Effects", "type": "effect", "keyframes": [{"channel": "timeline", "data_points": [{"script": "animations.Okiku.ShakeyShakey:stop()\nsounds:playSound(\"entity.zombie_villager.converted\", player:getPos(), 1, .135)\n"}], "uuid": "1ca62bd0-c1a5-3253-d563-7507377b770c", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "timeline", "data_points": [{"script": "models.Okiku.Ring:setVisible(true)\n"}], "uuid": "ae819bd4-a8e3-fc9e-ba90-91f632f77138", "time": 1.5, "color": -1, "interpolation": "linear"}, {"channel": "timeline", "data_points": [{"script": "\nmodels.Okiku.Ring.Noggle.RANGO:setVisible(false)\n"}], "uuid": "fe3ad644-17a0-84a2-0646-3781fd5c6f9c", "time": 1.5416666666666667, "color": -1, "interpolation": "linear"}, {"channel": "timeline", "data_points": [{"script": "models.Okiku.RingEyes:setVisible(true)\n"}], "uuid": "83077cc8-97c8-9c9b-bf12-e948042abb89", "time": 7.5, "color": -1, "interpolation": "linear"}]}, "0ce83a3b-f3e0-b7d1-64e1-8f5fd4c9f064": {"name": "NogginBits", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "b9b701e8-11cc-d4f5-13f3-a369be4c31f8", "time": 2.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 10}], "uuid": "4bd96cc9-d05a-5dc0-fe3a-df3612591ae5", "time": 2.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": -10}], "uuid": "a200f57c-04da-aefb-f9b8-72a43402ffc5", "time": 3, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": -10}], "uuid": "27abbf94-f61b-abf0-8b2d-29d4a6856827", "time": 3.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": "0"}], "uuid": "69a261a2-979c-381f-d156-f4d970afb360", "time": 3.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": "0"}], "uuid": "f895c89a-affa-a79b-9c70-6c9217770cbc", "time": 4.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 32.5, "y": 0, "z": "0"}], "uuid": "eea410fb-f9d5-8273-02c0-fc74a4b6c623", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 10, "y": 0, "z": 0}], "uuid": "a07f3dab-e5bc-88c7-ccb6-52ec9f6e790a", "time": 7, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "9e5c71a4-2e3c-631d-89e2-b28fbdbeb5b5", "time": 7.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "cfbe1a04-01e7-2610-b5d2-386f97476810", "time": 2, "color": -1, "uniform": true, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "1ef792b6-0ea3-f458-98d4-4e1bd7fd2d4f", "time": 2.2916666666666665, "color": -1, "uniform": true, "interpolation": "catmullrom"}]}, "c419ee69-4273-618b-53a8-7c054f6c99a2": {"name": "TheVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "5b79c2e3-a2d3-0937-42c6-f1887bd49aa7", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": -10}], "uuid": "077c0a69-926d-3b16-c921-5a57ddf8da6c", "time": 2.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 5}], "uuid": "784687e2-e1f4-bed4-4024-4ecb2b408c85", "time": 3, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 5}], "uuid": "16acb51f-a727-4823-bbcc-a535757a8041", "time": 3.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": "0"}], "uuid": "d1126a8a-a92b-8190-ef50-cf6846c9caf7", "time": 3.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": "0"}], "uuid": "d5ccddfb-2e30-2b0d-3338-5129c86faa86", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": "0"}], "uuid": "7e7912c0-338c-2d53-af9f-9eb093051a07", "time": 7, "color": -1, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "36f986d3-7484-3fa8-8ada-5d91a43f16af", "time": 2.5, "color": -1, "uniform": true, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "79cdb6fd-799e-f877-f1eb-6e6498291aa8", "time": 2.75, "color": -1, "uniform": true, "interpolation": "catmullrom"}]}, "9d7c0b25-cc44-6a43-9602-09df292fbfff": {"name": "FrontVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -12.5, "y": "0", "z": "0"}], "uuid": "83c02a84-2abb-b9bd-ec4f-26a4d657fb2f", "time": 5.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "5b9b699c-057d-f98a-6dd6-c2d9cc6efd5d", "time": 4.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -20, "y": 0, "z": 0}], "uuid": "81543feb-edd6-a69e-4c24-96ea3ef9856f", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "a4869039-278f-e031-6234-f0f33d381457", "time": 7.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "31195c23-337c-940f-8a31-e172465a56f0", "time": 6.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "4247987d-321d-1139-2152-f5c290f5c37f", "time": 7.75, "color": -1, "interpolation": "catmullrom"}]}, "f052b1a8-653c-610b-fba5-c6173a05c90b": {"name": "RightVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -10, "y": "0", "z": "0"}], "uuid": "0531e9db-8c27-fd06-8b42-18cd7515fb75", "time": 5.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "8bd86b73-e56e-fa78-679f-b19f8ab48ce3", "time": 4.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -27.5, "y": 0, "z": 0}], "uuid": "03f95045-c424-86f5-09e9-af0300ffdfd4", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "8829a5c3-d07b-9db4-7b3a-a0dfe0f4f2d2", "time": 6.75, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "a9cd404d-5fa1-899f-342d-61401fd979d8", "time": 7.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -15, "y": 0, "z": 0}], "uuid": "4960fc0a-c50d-7d53-c9d5-4f066e9ceba0", "time": 7.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": -7.5}], "uuid": "1a457530-013c-01be-ae19-1ada693c7885", "time": 3.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-9", "y": -0.5, "z": -5}], "uuid": "a6bb0f87-e342-14db-6eaf-620898e4e8d0", "time": 6.5, "color": -1, "interpolation": "linear"}]}, "9cb55e3c-c096-2ba6-c5bc-b3a31300ae63": {"name": "LeftVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -10, "y": 0, "z": 0}], "uuid": "33228e96-6d34-f061-64b1-509c2d8f1c9b", "time": 5.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "52689d33-fc9b-914b-1bda-9bb92fffa936", "time": 4.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -30, "y": 0, "z": 0}], "uuid": "db142949-4325-370f-3419-ad3b2a10732a", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -15, "y": "0", "z": "0"}], "uuid": "8159ee3c-8972-9957-d7a1-8f4a79237142", "time": 7.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "82169dec-4974-1c24-e043-70ee69d45dc8", "time": 6.75, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "bcd43893-032e-0b61-5f09-8200c1e59e55", "time": 7.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 10}], "uuid": "2664e949-74ca-e3b6-a4ff-a970f69c52d3", "time": 3.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-9", "y": "0.5", "z": "5"}], "uuid": "93c6b164-b875-0898-a4f5-739f4dc5e917", "time": 6.5, "color": -1, "interpolation": "linear"}]}, "a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 15, "y": "0", "z": "0"}], "uuid": "c7fb6980-8c18-cd95-c87b-eecff061b73a", "time": 4.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "b235eb57-6a01-3b36-34d8-5a300bba85c5", "time": 4, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "33f09e7d-a69f-4953-5f45-b5bd5bc18483", "time": 5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 25, "y": 0, "z": 0}], "uuid": "88bade26-a8bc-420f-e982-f53154c1860b", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 40, "y": 0, "z": 0}], "uuid": "d8585b35-a0fe-57bf-ab35-1e3b77f9fb68", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 27.92, "y": 0, "z": 0}], "uuid": "060c6587-3498-b175-8809-1664a1dab092", "time": 7, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "0846742b-0980-2bd4-6f44-3c7a4fe7710e", "time": 7.5, "color": -1, "interpolation": "catmullrom"}]}, "a247b8fc-3e11-172d-2179-4eebf17e6fe8": {"name": "Shoulders", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-15", "y": "0", "z": "0"}], "uuid": "d99ebf9e-07eb-a403-eecd-38d089fc515c", "time": 4.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "4a4a753d-4fd5-c72c-6cb5-8975bf6f36f2", "time": 4, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-25", "y": "0", "z": "0"}], "uuid": "1e7e580d-1f74-74c6-c5f8-ff6e19e1288e", "time": 5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -25, "y": 0, "z": 0}], "uuid": "52967522-7b62-962a-a84e-12eb25c4bee4", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "7d325cd6-e58b-d8dd-b42a-b510432b8fdd", "time": 6.5, "color": -1, "interpolation": "catmullrom"}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-172.5", "y": "-2.5", "z": "12.5"}], "uuid": "dc250ab1-9f48-b8c8-3eef-61674ccd4461", "time": 3, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-140", "y": "-35", "z": "-17.5"}], "uuid": "9482ce00-62cd-72ef-1db7-103d3affb4c7", "time": 3.375, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-87.5", "y": "0", "z": "-50"}], "uuid": "6d4c6419-7883-0caa-8d10-106e361727f6", "time": 3.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-62.5", "y": "12.5", "z": "-62.5"}], "uuid": "74348889-7fb6-b228-4dc9-f91d1bc4ca0b", "time": 4, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-17.5", "y": "17.5", "z": "-17.5"}], "uuid": "5d0dcafb-b8cb-0288-4ab9-b8690d122d92", "time": 4.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-22.5", "y": "12.5", "z": "-35"}], "uuid": "bcada84d-40a6-2d4b-0108-47875168e4b2", "time": 4.333333333333333, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-10", "y": "17.5", "z": "-12.5"}], "uuid": "d5502d48-f0da-c5aa-1f85-16808a46ba1c", "time": 5.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-5", "y": "20", "z": "0"}], "uuid": "48719d38-7995-ffbe-0031-cbf469d7fd66", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-87.5", "y": "-5", "z": "-17.5"}], "uuid": "7357b9b8-8659-53bb-e429-55b0faa23ee1", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 27.5, "z": -17.5}], "uuid": "c16ac4f1-41c4-8fec-1943-77f6176e34f0", "time": 7, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.09640930522581037, -0.12154416864513778, -0.15745111638703407], "bezier_left_value": [-9.913793103448276, -2.7058823484465435, -3.3093413405850978], "bezier_right_time": [0.09640930522581037, 0.12154416864513778, 0.15745111638703407], "bezier_right_value": [9.913793103448276, 2.7058823484465435, 3.3093413405850978]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "e4719115-08c2-6e81-85cf-afcfe4d37f53", "time": 8, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -7.5, "y": "0", "z": "0"}], "uuid": "8536514d-f773-ffbb-eafb-c388e5c77728", "time": 7.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1502697268386548, -0.1, -0.1], "bezier_left_value": [1.322691321914803, 0, 0], "bezier_right_time": [0.1502697268386548, 0.1, 0.1], "bezier_right_value": [-1.322691321914803, 0, 0]}, {"channel": "position", "data_points": [{"x": -1, "y": -7.5, "z": -0.5}], "uuid": "0fbeca0a-3ff4-7cba-6628-2afeec8c5a22", "time": 3, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -1, "y": 2.5, "z": -1.5}], "uuid": "5a26fc60-720d-3cb9-fa32-0c7ab3d1885a", "time": 2.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -1, "y": -1.75, "z": -0.5}], "uuid": "1bd2b787-9850-8dc5-f008-0e0f646a8d25", "time": 3.375, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -1, "y": 0.25, "z": 0.5}], "uuid": "7b212cf8-bfdd-eac3-28fb-d50252f321cc", "time": 3.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -1, "y": -0.75, "z": 1.5}], "uuid": "aa1e35dd-fd7a-8491-b39a-047d6e13c394", "time": 4, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -1, "y": 0.25, "z": 2.5}], "uuid": "f40b3d60-b6bd-6aa3-77ac-8cdfa77edc4e", "time": 4.333333333333333, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -1, "y": -0.75, "z": 1.5}], "uuid": "7b4a2741-68b1-dfb3-382d-87022cdeb604", "time": 4.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -1, "y": -3, "z": 1}], "uuid": "13800eaf-0d14-b606-2f7f-828204b26a50", "time": 5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -1, "y": -4, "z": 0.5}], "uuid": "3da34545-ac92-026a-a568-5c7a2b4e1295", "time": 5.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": -7, "z": 0.5}], "uuid": "ee81990a-b0d4-1b75-f197-17985a1b48a7", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": -3, "z": 0.5}], "uuid": "207993ea-f897-654b-555a-6fda0112db75", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": -1, "z": 1.5}], "uuid": "8b7011c6-2fb3-cd90-854a-7bb10c5ee2b8", "time": 7, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "21d26576-eafb-516f-f376-2e0ee3991908", "time": 7.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "1156ff34-2a46-206e-c19c-89b5de4ca32c", "time": 2.5, "color": -1, "uniform": true, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "7eef8e57-db0c-756c-5443-b18e4c375fa1", "time": 3, "color": -1, "uniform": true, "interpolation": "catmullrom"}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-180", "y": "0", "z": "0"}], "uuid": "596bc859-a454-ed3b-0ef0-dbf347db199c", "time": 2, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-165", "y": "32.5", "z": "-2.5"}], "uuid": "23ac6d60-fa9d-f619-9c00-8a2c0806a932", "time": 2.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-75", "y": "10", "z": "95"}], "uuid": "7c3dfac5-4ac6-8681-d96d-eafaa0f869ed", "time": 3, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -130, "y": 65, "z": 30}], "uuid": "e2aa49e8-393c-01f7-7134-b921f46fdf17", "time": 2.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-80", "y": "-5", "z": "65"}], "uuid": "c8be8534-09eb-3791-65f3-c2e5bbfaffc3", "time": 3.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-77.5", "y": "-22.5", "z": "60"}], "uuid": "0166722b-7797-21af-5ecb-6c4f30647dd9", "time": 3.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-47.5", "y": "-15", "z": "40"}], "uuid": "49616973-cc98-f1e9-d639-6f4de054052d", "time": 4, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-15", "y": "12.5", "z": "-0.5"}], "uuid": "7a69da3c-d2d5-8344-4b01-ad6e90af6e70", "time": 5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-45", "y": "-22.5", "z": "32.5"}], "uuid": "ea546fe2-b987-54cc-51bc-d6dfe23cee70", "time": 4.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-15", "y": "0", "z": "22.5"}], "uuid": "c491b912-80df-c04a-a04d-b12497621a89", "time": 4.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": -20, "z": 7.5}], "uuid": "bfc02eb6-6161-bf68-fdfb-cd48d200b57a", "time": 5.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-5", "y": -20, "z": 0}], "uuid": "351545bf-e652-0b77-1334-366a96138d53", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-87.5", "y": 5, "z": 17.5}], "uuid": "51c1e8a1-10a9-4eb5-e21e-0a708efb981f", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": -27.5, "z": 17.5}], "uuid": "3c617e9a-5549-0779-dd4e-26d4587d71d9", "time": 7, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "a75326aa-726e-44ab-26c5-a047a814842b", "time": 8, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -10, "y": "0", "z": "0"}], "uuid": "7d29a691-b80d-75c4-cbe0-4ff6fe90f0a3", "time": 7.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.08204652612905186, -0.1, -0.1], "bezier_left_value": [5.288208563294114, 0, 0], "bezier_right_time": [0.08204652612905186, 0.1, 0.1], "bezier_right_value": [-5.288208563294114, 0, 0]}, {"channel": "position", "data_points": [{"x": 2, "y": "0", "z": "0"}], "uuid": "daf66a74-0734-25dd-6bfe-8b1bd7f5a9f1", "time": 2.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 2, "y": "0", "z": "0"}], "uuid": "cdd27fc2-0c92-8804-6b2a-9baadae52bdb", "time": 3, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -0.5, "y": 0.5, "z": 2}], "uuid": "d8a6de6d-12f0-7175-54f1-f83cd4afd089", "time": 3.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 2.25, "y": "0", "z": "0"}], "uuid": "79e0cd15-57c4-e8dd-3219-3bd865b06ad6", "time": 2.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -0.5, "y": -2.75, "z": 2}], "uuid": "3b80d7ad-5a09-5805-0191-9d2eb33bfa3b", "time": 5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -0.5, "y": 0.5, "z": 2}], "uuid": "39a537f0-1af9-6ae0-97cf-35935f3b32ba", "time": 4, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -0.5, "y": -1.5, "z": 1}], "uuid": "8b547b74-a72e-01fc-18d2-99a99bc12de7", "time": 4.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -0.5, "y": 0.88, "z": 1}], "uuid": "52ef453e-69a1-c478-e8bb-37442e801d37", "time": 4.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -0.5, "y": -3.75, "z": 1}], "uuid": "b7867306-4edf-0a47-4357-36dbace80750", "time": 5.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": -7, "z": 0.5}], "uuid": "b849f8cf-bc5f-45f8-56d8-2035db282736", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": -3, "z": 0.5}], "uuid": "76f1455e-bdd1-7c59-84a2-cd2028dc180c", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": -1, "z": 1.5}], "uuid": "77fb8acb-c17f-2c18-2066-a1e9ec5d01dd", "time": 7, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7cc12c27-c6ba-9e49-6d83-a821223d529d", "time": 7.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "95b07f86-5d62-d850-4623-2f4893424caf", "time": 2, "color": -1, "uniform": true, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "8d282ad5-b5f6-a4f1-6816-1245b02d4073", "time": 2.2916666666666665, "color": -1, "uniform": true, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "5ba876d4-cdae-802f-b958-2020cbf0763e", "time": 3, "color": -1, "uniform": true, "interpolation": "catmullrom"}]}, "069d4014-e378-cde6-ed3f-3b8fc92c9750": {"name": "MidSection", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -32.5, "y": "0", "z": "0"}], "uuid": "277e5b73-eda0-943b-4fa1-d38809028ebb", "time": 5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "334b8e9a-5e31-76dd-4a9d-5babcd362d22", "time": 4.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -50, "y": 0, "z": 0}], "uuid": "4e53c31d-8eda-898e-b099-0ac57d589ca9", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-22.5", "y": 0, "z": 0}], "uuid": "04c6f8be-9443-b8bc-9de5-db55ae0fa60f", "time": 7, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "f9089d55-c088-ed62-a183-0a3409f967c8", "time": 7.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7a253ec3-011a-14f9-78d3-2fb14474b622", "time": 4.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": 0.5, "z": 0}], "uuid": "03645b9c-2040-8e8c-22e8-72736fb818bf", "time": 5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "48c41234-6716-7ccf-89dc-c9919c9dc352", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "3c3f9a99-8724-ed59-973f-93da5532fa5c", "time": 3.75, "color": -1, "uniform": true, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "62feeecf-e8b0-4131-917c-dc2b807005a4", "time": 4, "color": -1, "uniform": true, "interpolation": "catmullrom"}]}, "39b607ae-53e6-6156-41f8-11a554e1e77f": {"name": "<PERSON><PERSON><PERSON>", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "44334aab-405a-d02b-0c28-d7ba79e8b5c5", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "a348cf7b-2c4d-a06b-e2b6-32d9dfb47628", "time": 5.875, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "5d961327-22b7-be79-549c-bff218d8116f", "time": 7.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "011780a3-ebd8-13f5-c3fe-6a89df090be2", "time": 3.75, "color": -1, "uniform": true, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": 0.8, "y": 0.6, "z": "1"}], "uuid": "b2e0e169-bf07-419d-2f71-b0189e621380", "time": 4.25, "color": -1, "uniform": false, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "0.8", "z": "1"}], "uuid": "93b06845-53ac-c042-4c74-3d0a20e52100", "time": 5, "color": -1, "uniform": false, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "66df6dff-3db7-abed-0cd8-381c39d7b864", "time": 5.875, "color": -1, "uniform": false, "interpolation": "catmullrom"}]}, "*************-d6e3-2315-015aa3b6fb4d": {"name": "FrontScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "6622bb7a-f71e-bbf4-20ba-3550f4ef86b7", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "82bbddd0-5c5a-9a08-03eb-79e6c20a525d", "time": 5.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "231e5e18-8351-bfee-30f9-a91740ded6b4", "time": 7.5, "color": -1, "interpolation": "catmullrom"}]}, "a8267e42-e1ec-7aa4-67ce-f3079d498125": {"name": "BackScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "1cef63b4-1777-f494-990f-866bb2965fc1", "time": 6.25, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "220c1eda-0657-be9b-8ea3-9fa895b9fb8c", "time": 6.75, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "2adfd577-3d12-5029-ec93-b2c4fa0d5c28", "time": 6.5, "color": -1, "interpolation": "linear"}]}, "07f1c422-136b-88b2-3301-0d1a14dd6560": {"name": "LeftScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": 2.5}], "uuid": "f0dd02d0-878d-4fea-397c-507ca3e4e7d9", "time": 5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "577f4b57-9485-7374-6d1a-0a80f27bca63", "time": 6, "color": -1, "interpolation": "catmullrom"}]}, "fd2c92eb-7659-a0b9-2254-118d0dad40f5": {"name": "RightScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -2.5}], "uuid": "cbcebd38-b971-45a7-7fd9-ca2c9d093d7b", "time": 5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "45d8ea0f-f67a-c4d5-b1ac-beff9bf0b9a9", "time": 6, "color": -1, "interpolation": "catmullrom"}]}, "0c083c53-995e-71ab-f3e1-dcb04bd31c4a": {"name": "Legs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "44a42b30-d993-ae34-20b9-75922517230f", "time": 5.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7c1571f1-849b-0514-6faa-c703605378a9", "time": 5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -10, "y": 0, "z": 0}], "uuid": "43d8dedd-ae52-205f-bb75-a887bafd2d8d", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "6e7b46ee-aae5-72f7-d209-f30aa35ffeb3", "time": 7.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -15, "y": 0, "z": 0}], "uuid": "51b48c10-0731-9d8d-0fee-18af2082f3aa", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "61338c55-f42d-16b5-0b45-c8ccc19eb007", "time": 8, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 3, "z": "0"}], "uuid": "6028d846-3096-a0e6-cf7a-3d1a24cb1d25", "time": 6.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 2, "z": "0"}], "uuid": "12937a38-65fd-4410-d84a-398bcd90b152", "time": 6, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": 2, "z": "0"}], "uuid": "e3de6c3b-bba8-861d-742f-3894e0744b19", "time": 6.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "664a68c9-b38b-977b-5c66-f243672ec0d8", "time": 7.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "134d0e10-e896-666e-9de7-6edc385a411c", "time": 8, "color": -1, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "89e7d10d-66be-e44a-fb15-c50bd633acf0", "time": 5, "color": -1, "uniform": true, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "d1b27fc1-a295-b845-24cf-9b768cfd67b4", "time": 6, "color": -1, "uniform": true, "interpolation": "catmullrom"}]}, "dbf533e5-3f4a-b016-4a88-d6bb4d683ef8": {"name": "TorsoBits", "type": "bone", "keyframes": [{"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "9315315d-f693-0b16-37a2-033f89815fd0", "time": 3, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "1fb2a50d-ea52-0698-1020-af346fb7b9e0", "time": 2.5, "color": -1, "uniform": true, "interpolation": "linear"}]}}}, {"uuid": "d6697892-d8a7-f176-e4da-d70728ed29f6", "name": "Freedom", "loop": "hold", "override": false, "length": 1, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"d0f57d3a-5c99-127f-bb1e-dbd873765b47": {"name": "Noggle", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 10.5, "z": "0.75"}], "uuid": "47eb506d-c912-c499-edac-0f09fc8b0bc2", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.197, -0.38816295619486463, -0.3674248050307978], "bezier_left_value": [0, 2.9590266621147276, -1.1219512195121952], "bezier_right_time": [0.197, 0.38816295619486463, 0.3674248050307978], "bezier_right_value": [0, -2.9590266621147276, 1.1219512195121952]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "70f25e0a-c3da-ec81-ddd2-8baf6c79a0a1", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.197, -0.43227748454752574, -0.197], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.197, 0.43227748454752574, 0.197], "bezier_right_value": [0, 0, 0]}]}, "a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "99e7bf16-c715-833e-c92e-b16b5a3d90a5", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "8ffab7df-101a-8cde-ff9a-c63130d0b235", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "2a231824-83ec-a140-9486-cd1126f4adcd", "time": 1, "color": -1, "interpolation": "linear"}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 10, "y": 0, "z": -10}], "uuid": "18783fa7-a474-d700-156c-100ca47ee97b", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.197, -0.197, -0.316928566503154], "bezier_left_value": [0, 0, 10.481460381706038], "bezier_right_time": [0.197, 0.197, 0.316928566503154], "bezier_right_value": [0, 0, -10.481460381706038]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "1c49de19-9800-78a7-2651-f86017b44771", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.197, -0.197, -0.2664323279755102], "bezier_left_value": [0, 0, -0.3506921084248118], "bezier_right_time": [0.197, 0.197, 0.2664323279755102], "bezier_right_value": [0, 0, 0.3506921084248118]}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 10, "y": 0, "z": 10}], "uuid": "45ba98dd-d25a-2dee-fdc0-9f5b6fbc574c", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.197, -0.197, -0.316928566503154], "bezier_left_value": [0, 0, -10.481460381706038], "bezier_right_time": [0.197, 0.197, 0.316928566503154], "bezier_right_value": [0, 0, 10.481460381706038]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "07155f70-87ee-4b0c-dcfa-86a44f378e3b", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.197, -0.197, -0.2664323279755102], "bezier_left_value": [0, 0, 0.3506921084248118], "bezier_right_time": [0.197, 0.197, 0.2664323279755102], "bezier_right_value": [0, 0, -0.3506921084248118]}]}, "069d4014-e378-cde6-ed3f-3b8fc92c9750": {"name": "MidSection", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "1d335bc4-4e5e-f54e-29ea-9ec12f86d2c4", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-5", "y": "0", "z": "0"}], "uuid": "b0be6291-ab6e-15c5-7641-bf649c3d7607", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0c0d9050-6d24-467f-caa9-602ca42bcec9", "time": 1, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "d3173344-e843-19c8-ec3d-205533e1f60b", "name": "idle", "loop": "loop", "override": false, "length": 5, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"d0f57d3a-5c99-127f-bb1e-dbd873765b47": {"name": "Noggle", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": -0.25, "z": "0"}], "uuid": "4171b614-2f5b-12b1-233f-9f5123cfebf4", "time": 0, "color": -1, "interpolation": "linear"}]}, "0ce83a3b-f3e0-b7d1-64e1-8f5fd4c9f064": {"name": "NogginBits", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "0d3dc5ca-75c6-c0c5-84d1-bb1e76d8b062", "time": 0, "color": -1, "interpolation": "linear"}]}, "9d7c0b25-cc44-6a43-9602-09df292fbfff": {"name": "FrontVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -2.5, "y": "0", "z": "0"}], "uuid": "fab66d7c-a736-13a4-be3b-7baaba7a98db", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.4409166666666667, -0.143, -0.143], "bezier_left_value": [-1, 0, 0], "bezier_right_time": [0.4409166666666667, 0.143, 0.143], "bezier_right_value": [1, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -2.5, "y": "0", "z": "0"}], "uuid": "7ab6b223-6143-c298-5f31-55f44e89ed3d", "time": 2.875, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.3813333333333333, -0.143, -0.143], "bezier_left_value": [1, 0, 0], "bezier_right_time": [0.3813333333333333, 0.143, 0.143], "bezier_right_value": [-1, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "cb3849c3-5269-ae6f-aab3-22e8cba00a25", "time": 3.5833333333333335, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.32175, -0.143, -0.143], "bezier_left_value": [0.5, 0, 0], "bezier_right_time": [0.32175, 0.143, 0.143], "bezier_right_value": [-0.5, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -2.5, "y": "0", "z": "0"}], "uuid": "1145d8f6-2ef9-e975-0c94-24e4aba6c708", "time": 5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.4409166666666667, -0.143, -0.143], "bezier_left_value": [-1, 0, 0], "bezier_right_time": [0.4409166666666667, 0.143, 0.143], "bezier_right_value": [1, 0, 0]}]}, "a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "2dd4490b-e636-ef15-d49c-b80e123639fc", "time": 0, "color": -1, "interpolation": "linear"}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -10, "y": 15, "z": 0}], "uuid": "cc2e0354-7a35-5ced-6d9b-d884b8ffdb28", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -8, "y": 15, "z": 0}], "uuid": "e88d8a3c-bf01-6343-4aae-38383404421d", "time": 3.3333333333333335, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -10, "y": 15, "z": 0}], "uuid": "ad6e9663-b61d-a977-6dd6-ed7c7d79ae72", "time": 5, "color": -1, "interpolation": "catmullrom"}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -10, "y": -15, "z": 0}], "uuid": "5a1d1ffd-c29d-6967-a52f-806d88419db8", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -8, "y": -15, "z": 0}], "uuid": "4a27cf4b-e89a-9731-4586-4c4cb66eef03", "time": 3.3333333333333335, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -10, "y": -15, "z": 0}], "uuid": "30cbf740-f0e0-d2b5-d23d-641e0785d5df", "time": 5, "color": -1, "interpolation": "catmullrom"}]}, "069d4014-e378-cde6-ed3f-3b8fc92c9750": {"name": "MidSection", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "4683e7da-566d-8ed3-1790-7b37f7cdc47b", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": 0.25, "z": "0"}], "uuid": "5949e94f-cf03-fec2-7eb4-8cd62b233411", "time": 0, "color": -1, "interpolation": "linear"}]}, "07f1c422-136b-88b2-3301-0d1a14dd6560": {"name": "LeftScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "2", "z": "1"}], "uuid": "4a0e0602-8474-e978-a19e-bdbe1a595f68", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "c5901db5-cbbc-3ae0-6b2e-6e91e9848f93", "time": 3, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "2", "z": "1"}], "uuid": "d0265167-b8ca-751c-f655-ea5657503d56", "time": 5, "color": -1, "interpolation": "catmullrom"}]}, "fd2c92eb-7659-a0b9-2254-118d0dad40f5": {"name": "RightScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "c75eb8ef-18d8-3689-ad89-7a01e37d3b68", "time": 3, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "eec74d92-21c9-94b9-bad9-140baebabc8d", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "e45677fb-9966-4f27-2b4d-4a5b62f3797e", "time": 5, "color": -1, "interpolation": "catmullrom"}]}}}, {"uuid": "bb381bcc-c667-0258-9353-447dc0ad2e56", "name": "walk", "loop": "loop", "override": false, "length": 1, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"becce561-f622-7592-514f-5aede933e7be": {"name": "Ring", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": "0", "z": -1.5}], "uuid": "ee936469-ff49-a81d-d395-d123512a66e5", "time": 0, "color": -1, "interpolation": "linear"}]}, "d0f57d3a-5c99-127f-bb1e-dbd873765b47": {"name": "Noggle", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": -0.25, "z": "0"}], "uuid": "aac0ae09-1218-76a1-46a1-25c2aaaa0c5e", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -0.5, "z": 0}], "uuid": "5888d719-18fa-24c7-d658-ea192cfccf6f", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -0.25, "z": "0"}], "uuid": "cb0d14f4-8977-b9e8-b2ef-a0bdc953eb6a", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -0.5, "z": 0}], "uuid": "9a826b07-bfc4-029b-63d0-823862f00d44", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -0.5, "z": 0}], "uuid": "3e63fe0b-12d6-f834-417a-5e37cfc42b5c", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "0ce83a3b-f3e0-b7d1-64e1-8f5fd4c9f064": {"name": "NogginBits", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "2a0f6f7b-b6f9-333d-6e77-e35dc98d51de", "time": 0, "color": -1, "interpolation": "linear"}]}, "9d7c0b25-cc44-6a43-9602-09df292fbfff": {"name": "FrontVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-2.5", "y": "0", "z": "1"}], "uuid": "84ee109d-d141-0b19-c845-13a378aa8e9a", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-2.5", "y": "0", "z": "-1"}], "uuid": "43e15804-9de8-c379-46b6-9cf42ec65335", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-2.5", "y": "0", "z": "-1"}], "uuid": "3c7fc8ec-4095-4050-bccc-b7f214442a55", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}]}, "f052b1a8-653c-610b-fba5-c6173a05c90b": {"name": "RightVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": 2}], "uuid": "1a89d89a-6674-dded-95fb-08e42108adeb", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -2}], "uuid": "968a39a3-f108-274d-2ed7-f16a879ab5dd", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -2}], "uuid": "0f61ed6d-ece8-9fa3-81e8-a39600d28d52", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}]}, "9cb55e3c-c096-2ba6-c5bc-b3a31300ae63": {"name": "LeftVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": 2}], "uuid": "ea49b58e-161e-216b-4117-a1f8e159d206", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -2}], "uuid": "c1fd89f8-90b2-ac27-0ecf-1d1776e20d59", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -2}], "uuid": "6df51777-2b47-66e7-78f1-80ae6a3615ef", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}]}, "a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "10", "y": "5", "z": "1"}], "uuid": "ff1050dc-2e3a-1236-b957-7275d7ac569a", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "10", "y": "-5", "z": "-1"}], "uuid": "e90fb11c-34cd-45f2-0eb4-86de8288d1b2", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "10", "y": "-5", "z": "-1"}], "uuid": "d4883f5e-3156-43ed-d32f-cbefddd2b6a5", "time": 1, "color": -1, "interpolation": "linear"}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": 15, "z": 0}], "uuid": "c58921cb-518b-461f-bfc1-8c938f95aeb4", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [0, 0, 1], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [0, 0, -1]}, {"channel": "rotation", "data_points": [{"x": "-5", "y": 15, "z": -5}], "uuid": "bd154db2-ca5a-fe05-49ec-c7bbaa01f624", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.046019688419942126, -0.1, -0.1], "bezier_left_value": [2.0298507462686572, 0, 0], "bezier_right_time": [0.046019688419942126, 0.1, 0.1], "bezier_right_value": [-2.0298507462686572, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -10, "y": 15, "z": 0}], "uuid": "8f1b1d81-4a0e-607e-0422-b86ac5974b94", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [-1, 0, -1], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [1, 0, 1]}, {"channel": "rotation", "data_points": [{"x": 5, "y": 15, "z": 0}], "uuid": "cfe606b6-526f-5f4f-7f2f-1071efbb5920", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [0, 0, 1], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [0, 0, -1]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "e296a7d1-dab7-1850-389c-7c36e66dad22", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": -0.5}], "uuid": "c064ad17-d64f-d072-41cb-267be789a167", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "9b36e0e3-ec34-35de-cb93-03006d2e83c8", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -10, "y": -15, "z": 0}], "uuid": "352df58e-e832-84d6-112b-fa1bc792c674", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [-1, 0, 1], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [1, 0, -1]}, {"channel": "rotation", "data_points": [{"x": -10, "y": -15, "z": 0}], "uuid": "317eb81f-a120-f26e-57d4-89a25751d90e", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [-1, 0, 1], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [1, 0, -1]}, {"channel": "rotation", "data_points": [{"x": 5, "y": -15, "z": 0}], "uuid": "7d7527cd-4c17-aeb1-bad1-50418b298e0b", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [0, 0, -1], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [0, 0, 1]}, {"channel": "rotation", "data_points": [{"x": "-5", "y": "-15", "z": "5"}], "uuid": "c0d2c677-c349-43c6-853d-51360d4ec05c", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.046019688419942126, -0.1, -0.1], "bezier_left_value": [2.0298507462686572, 0, 0], "bezier_right_time": [0.046019688419942126, 0.1, 0.1], "bezier_right_value": [-2.0298507462686572, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": -0.5}], "uuid": "9464d9c8-a8a8-0520-7746-782b64fc6ed1", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "bf9e6e2f-b173-c909-24e2-6b01ddb8b942", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": -0.5}], "uuid": "ec92cc6f-2956-426d-bfd6-f946b2e2d856", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}]}, "069d4014-e378-cde6-ed3f-3b8fc92c9750": {"name": "MidSection", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-5", "y": "-5", "z": "0"}], "uuid": "14376535-e116-b889-f675-907bb21a2e6d", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-5", "y": "5", "z": 3.3272316613874864e-05}], "uuid": "ad531b2f-c890-aed1-8b92-b8282e935f94", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-5", "y": 5, "z": "0"}], "uuid": "03232452-d137-9412-33e4-6b4866b5c248", "time": 1, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": 0.25, "z": "0"}], "uuid": "1edfa9e8-6eb3-2414-32c6-1b13492aa9f0", "time": 0, "color": -1, "interpolation": "linear"}]}, "*************-d6e3-2315-015aa3b6fb4d": {"name": "FrontScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-12.5", "y": -5, "z": 1.5}], "uuid": "66573cfa-c81f-845c-3404-fb3f4f351195", "time": 0.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-12.5", "y": "5", "z": "-1.5"}], "uuid": "63f72c3f-6823-11a8-7621-7ee5f2af7558", "time": 1, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-12.5", "y": "5", "z": "-1.5"}], "uuid": "82936dff-7249-f975-1ce4-1a46bf3606e3", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-8", "y": 0, "z": 0}], "uuid": "ba77dc1b-656d-f8cb-1cc8-eb66cb647f9a", "time": 0.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-8", "y": 0, "z": 0}], "uuid": "85822d7e-a07f-da13-cadf-b6522682bda5", "time": 0.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.5000000000000004}], "uuid": "abb5984d-6b1c-6aa9-2bed-d588d2a0bb32", "time": 0, "color": -1, "uniform": false, "interpolation": "catmullrom"}]}, "a8267e42-e1ec-7aa4-67ce-f3079d498125": {"name": "BackScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "10", "y": "-5", "z": "-1.5"}], "uuid": "1add43fb-7735-98a4-46ae-257732d62e2b", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "10", "y": 5, "z": 1.5}], "uuid": "565027ee-d7e6-6688-8a84-2dd9cb97d641", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "10", "y": 5, "z": 1.5}], "uuid": "21021c3d-3de2-17f9-1ae3-e772066dffce", "time": 1, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.5000000000000004}], "uuid": "42a1ae18-4a40-606c-e493-0f61df369d07", "time": 0.5, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.5000000000000004}], "uuid": "729f7d7f-dd6b-0a06-aa03-9deb3d451a8f", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.5000000000000004}], "uuid": "8ad8bee8-0d9e-6eb3-5c43-467e3170707c", "time": 1, "color": -1, "uniform": false, "interpolation": "linear"}]}, "07f1c422-136b-88b2-3301-0d1a14dd6560": {"name": "LeftScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": 2, "z": 1}], "uuid": "d2570fa0-9ef0-1755-b8c4-348e9ede2aed", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -5, "y": 2, "z": 1}], "uuid": "061f16a5-fa18-88d4-a674-74d79edcb47a", "time": 1, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "2.5", "y": 2.5, "z": -4}], "uuid": "4abd106f-852a-d5b7-e8e1-60d0b4bb7196", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.1}], "uuid": "a1d0bf80-4b56-f032-1db8-fd085604a1d5", "time": 0.5, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "cc0bf39c-d995-70ba-5b79-cb64c699b078", "time": 1, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "7decde99-7832-fcb7-c343-71b27964971f", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}, "fd2c92eb-7659-a0b9-2254-118d0dad40f5": {"name": "RightScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "2.5", "y": "-2.5", "z": "4"}], "uuid": "3d673400-a6d3-9311-6cf9-04e6c07e3a96", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -5, "y": -2, "z": -1}], "uuid": "79673a55-db9e-68ba-29bd-efdec4aae102", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "2.5", "y": "-2.5", "z": "4"}], "uuid": "52182b3f-d60b-0636-fb10-c1f090563030", "time": 1, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.1}], "uuid": "6d97f040-487d-78c6-1e20-b94f0a739c6e", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "a2870a79-33f5-77b5-8ed9-74eabcf4ea21", "time": 0.5, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.1}], "uuid": "b7b3dbd5-bb9c-3b06-7367-44daaeaa888c", "time": 1, "color": -1, "uniform": false, "interpolation": "linear"}]}, "243936f8-b9c6-d179-6a92-d5c9a01b4340": {"name": "LeftHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -20, "y": "0", "z": "0"}], "uuid": "b1c8e66f-3964-ba01-290b-ebf3fc9bee51", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -20, "y": "0", "z": "0"}], "uuid": "81253ef9-6f58-863e-1d09-9eb5f44a82e4", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 17.5, "y": "0", "z": "0"}], "uuid": "5467dcdd-d28a-0251-ff19-b558efa003ab", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-1.5", "y": "0", "z": "-2.5"}], "uuid": "d48e069b-f2fb-1518-05d0-96da4c256d3e", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [11.833766527588438, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [-11.833766527588438, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": -1}], "uuid": "3663842c-75bb-ca6c-649d-209e612efb46", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.14626883849719247, -0.1], "bezier_left_value": [0, 0.10029850746268656, 0], "bezier_right_time": [0.1, 0.14626883849719247, 0.1], "bezier_right_value": [0, -0.10029850746268656, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": -1}], "uuid": "a17c627e-33c9-9658-98c6-c7b773a90aeb", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.14626883849719247, -0.1], "bezier_left_value": [0, 0.10029850746268656, 0], "bezier_right_time": [0.1, 0.14626883849719247, 0.1], "bezier_right_value": [0, -0.10029850746268656, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": 1}], "uuid": "e3d27098-5d6e-e696-88bd-a702b16f4f41", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.15398031158005787, -0.14626883849719247], "bezier_left_value": [0, -0.10029850746268656, -0.05731343283582089], "bezier_right_time": [0.1, 0.15398031158005787, 0.14626883849719247], "bezier_right_value": [0, 0.10029850746268656, 0.05731343283582089]}, {"channel": "position", "data_points": [{"x": 0, "y": 1, "z": 0.25}], "uuid": "754fd726-da39-e4fc-d11e-9f9c6bdb7d2a", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.04216395187850942], "bezier_left_value": [0, 0, 0.27940298507462685], "bezier_right_time": [0.1, 0.1, 0.04216395187850942], "bezier_right_value": [0, 0, -0.27940298507462685]}]}, "ce3e2383-010d-03b5-2c18-1f82ea6055d8": {"name": "RightHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 17.5, "y": 0, "z": 0}], "uuid": "f1e28bc2-921f-2079-b7a6-ab555d257372", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 17.5, "y": 0, "z": 0}], "uuid": "a75487c3-d575-c784-034f-aed9dbd2e29b", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-1.5", "y": 0, "z": 2.5}], "uuid": "731b343e-c86f-341b-9ba5-3c9b04cde013", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [11.833766527588438, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [-11.833766527588438, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -20, "y": 0, "z": 0}], "uuid": "025117e5-0c6a-4dbc-102a-7a7520c431b4", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": 1}], "uuid": "dc95c546-8a5d-13cb-7337-5ce93eba20dc", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.15398031158005787, -0.14626883849719247], "bezier_left_value": [0, -0.10029850746268656, -0.05731343283582089], "bezier_right_time": [0.1, 0.15398031158005787, 0.14626883849719247], "bezier_right_value": [0, 0.10029850746268656, 0.05731343283582089]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": 1}], "uuid": "c27e858b-635b-84ce-e18c-d653b1393dda", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.15398031158005787, -0.14626883849719247], "bezier_left_value": [0, -0.10029850746268656, -0.05731343283582089], "bezier_right_time": [0.1, 0.15398031158005787, 0.14626883849719247], "bezier_right_value": [0, 0.10029850746268656, 0.05731343283582089]}, {"channel": "position", "data_points": [{"x": 0, "y": 1, "z": 0.25}], "uuid": "d404c388-5abe-9471-4ff1-4ccc14ef74cf", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.04216395187850942], "bezier_left_value": [0, 0, 0.27940298507462685], "bezier_right_time": [0.1, 0.1, 0.04216395187850942], "bezier_right_value": [0, 0, -0.27940298507462685]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": -1}], "uuid": "d718ff0f-24de-5b3a-7ac7-1c2dea76efeb", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.14626883849719247, -0.1], "bezier_left_value": [0, 0.10029850746268656, 0], "bezier_right_time": [0.1, 0.14626883849719247, 0.1], "bezier_right_value": [0, -0.10029850746268656, 0]}]}}}, {"uuid": "a8aae2ad-9717-5d2a-0b4d-494a44550ba0", "name": "walkback", "loop": "loop", "override": false, "length": 1, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"d0f57d3a-5c99-127f-bb1e-dbd873765b47": {"name": "Noggle", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": -0.25, "z": "0"}], "uuid": "aac0ae09-1218-76a1-46a1-25c2aaaa0c5e", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -0.5, "z": 0}], "uuid": "5888d719-18fa-24c7-d658-ea192cfccf6f", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -0.25, "z": "0"}], "uuid": "cb0d14f4-8977-b9e8-b2ef-a0bdc953eb6a", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -0.5, "z": 0}], "uuid": "9a826b07-bfc4-029b-63d0-823862f00d44", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -0.5, "z": 0}], "uuid": "3e63fe0b-12d6-f834-417a-5e37cfc42b5c", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "0ce83a3b-f3e0-b7d1-64e1-8f5fd4c9f064": {"name": "NogginBits", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "2a0f6f7b-b6f9-333d-6e77-e35dc98d51de", "time": 1, "color": -1, "interpolation": "linear"}]}, "9d7c0b25-cc44-6a43-9602-09df292fbfff": {"name": "FrontVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-2.5", "y": "0", "z": "1"}], "uuid": "84ee109d-d141-0b19-c845-13a378aa8e9a", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-2.5", "y": "0", "z": "-1"}], "uuid": "43e15804-9de8-c379-46b6-9cf42ec65335", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-2.5", "y": "0", "z": "-1"}], "uuid": "3c7fc8ec-4095-4050-bccc-b7f214442a55", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}]}, "f052b1a8-653c-610b-fba5-c6173a05c90b": {"name": "RightVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": 2}], "uuid": "1a89d89a-6674-dded-95fb-08e42108adeb", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -2}], "uuid": "968a39a3-f108-274d-2ed7-f16a879ab5dd", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -2}], "uuid": "0f61ed6d-ece8-9fa3-81e8-a39600d28d52", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}]}, "9cb55e3c-c096-2ba6-c5bc-b3a31300ae63": {"name": "LeftVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": 2}], "uuid": "ea49b58e-161e-216b-4117-a1f8e159d206", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -2}], "uuid": "c1fd89f8-90b2-ac27-0ecf-1d1776e20d59", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -2}], "uuid": "6df51777-2b47-66e7-78f1-80ae6a3615ef", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}]}, "a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-10", "y": "5", "z": "1"}], "uuid": "ff1050dc-2e3a-1236-b957-7275d7ac569a", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-10", "y": "-5", "z": "-1"}], "uuid": "e90fb11c-34cd-45f2-0eb4-86de8288d1b2", "time": 1, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-10", "y": "-5", "z": "-1"}], "uuid": "d4883f5e-3156-43ed-d32f-cbefddd2b6a5", "time": 0, "color": -1, "interpolation": "linear"}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": "-10", "z": 0}], "uuid": "c58921cb-518b-461f-bfc1-8c938f95aeb4", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [0, 0, -1], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [0, 0, 1]}, {"channel": "rotation", "data_points": [{"x": "-5", "y": "-10", "z": "-2.5"}], "uuid": "bd154db2-ca5a-fe05-49ec-c7bbaa01f624", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.046019688419942126, -0.1, -0.1], "bezier_left_value": [-2.0298507462686572, 0, 0], "bezier_right_time": [0.046019688419942126, 0.1, 0.1], "bezier_right_value": [2.0298507462686572, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -10, "y": "-10", "z": 0}], "uuid": "8f1b1d81-4a0e-607e-0422-b86ac5974b94", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [1, 0, 1], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [-1, 0, -1]}, {"channel": "rotation", "data_points": [{"x": 5, "y": "-10", "z": 0}], "uuid": "cfe606b6-526f-5f4f-7f2f-1071efbb5920", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [0, 0, -1], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [0, 0, 1]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "e296a7d1-dab7-1850-389c-7c36e66dad22", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": -0.5}], "uuid": "c064ad17-d64f-d072-41cb-267be789a167", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "9b36e0e3-ec34-35de-cb93-03006d2e83c8", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -10, "y": "10", "z": 0}], "uuid": "352df58e-e832-84d6-112b-fa1bc792c674", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [1, 0, -1], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [-1, 0, 1]}, {"channel": "rotation", "data_points": [{"x": -10, "y": "10", "z": 0}], "uuid": "317eb81f-a120-f26e-57d4-89a25751d90e", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [1, 0, -1], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [-1, 0, 1]}, {"channel": "rotation", "data_points": [{"x": 5, "y": "10", "z": 0}], "uuid": "7d7527cd-4c17-aeb1-bad1-50418b298e0b", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [0, 0, 1], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [0, 0, -1]}, {"channel": "rotation", "data_points": [{"x": "-5", "y": "10", "z": "2.5"}], "uuid": "c0d2c677-c349-43c6-853d-51360d4ec05c", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.046019688419942126, -0.1, -0.1], "bezier_left_value": [-2.0298507462686572, 0, 0], "bezier_right_time": [0.046019688419942126, 0.1, 0.1], "bezier_right_value": [2.0298507462686572, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": -0.5}], "uuid": "9464d9c8-a8a8-0520-7746-782b64fc6ed1", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "bf9e6e2f-b173-c909-24e2-6b01ddb8b942", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": -0.5}], "uuid": "ec92cc6f-2956-426d-bfd6-f946b2e2d856", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0]}]}, "069d4014-e378-cde6-ed3f-3b8fc92c9750": {"name": "MidSection", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "-5", "z": "0"}], "uuid": "14376535-e116-b889-f675-907bb21a2e6d", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "5", "z": 3.3272316613874864e-05}], "uuid": "ad531b2f-c890-aed1-8b92-b8282e935f94", "time": 1, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": 5, "z": "0"}], "uuid": "03232452-d137-9412-33e4-6b4866b5c248", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": 0.25, "z": "0"}], "uuid": "1edfa9e8-6eb3-2414-32c6-1b13492aa9f0", "time": 1, "color": -1, "interpolation": "linear"}]}, "*************-d6e3-2315-015aa3b6fb4d": {"name": "FrontScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-12.5", "y": -5, "z": 1.5}], "uuid": "66573cfa-c81f-845c-3404-fb3f4f351195", "time": 0.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-12.5", "y": "5", "z": "-1.5"}], "uuid": "63f72c3f-6823-11a8-7621-7ee5f2af7558", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-12.5", "y": "5", "z": "-1.5"}], "uuid": "82936dff-7249-f975-1ce4-1a46bf3606e3", "time": 1, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-8", "y": 0, "z": 0}], "uuid": "ba77dc1b-656d-f8cb-1cc8-eb66cb647f9a", "time": 0.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-8", "y": 0, "z": 0}], "uuid": "85822d7e-a07f-da13-cadf-b6522682bda5", "time": 0.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.5000000000000004}], "uuid": "abb5984d-6b1c-6aa9-2bed-d588d2a0bb32", "time": 1, "color": -1, "uniform": false, "interpolation": "catmullrom"}]}, "a8267e42-e1ec-7aa4-67ce-f3079d498125": {"name": "BackScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "10", "y": "-5", "z": "-1.5"}], "uuid": "1add43fb-7735-98a4-46ae-257732d62e2b", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "10", "y": 5, "z": 1.5}], "uuid": "565027ee-d7e6-6688-8a84-2dd9cb97d641", "time": 1, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "10", "y": 5, "z": 1.5}], "uuid": "21021c3d-3de2-17f9-1ae3-e772066dffce", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.5000000000000004}], "uuid": "42a1ae18-4a40-606c-e493-0f61df369d07", "time": 0.5, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.5000000000000004}], "uuid": "729f7d7f-dd6b-0a06-aa03-9deb3d451a8f", "time": 1, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.5000000000000004}], "uuid": "8ad8bee8-0d9e-6eb3-5c43-467e3170707c", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "07f1c422-136b-88b2-3301-0d1a14dd6560": {"name": "LeftScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": 2, "z": 1}], "uuid": "d2570fa0-9ef0-1755-b8c4-348e9ede2aed", "time": 1, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -5, "y": 2, "z": 1}], "uuid": "061f16a5-fa18-88d4-a674-74d79edcb47a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "2.5", "y": 2.5, "z": -4}], "uuid": "4abd106f-852a-d5b7-e8e1-60d0b4bb7196", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.1}], "uuid": "a1d0bf80-4b56-f032-1db8-fd085604a1d5", "time": 0.5, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "cc0bf39c-d995-70ba-5b79-cb64c699b078", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "7decde99-7832-fcb7-c343-71b27964971f", "time": 1, "color": -1, "uniform": true, "interpolation": "linear"}]}, "fd2c92eb-7659-a0b9-2254-118d0dad40f5": {"name": "RightScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "2.5", "y": "-2.5", "z": "4"}], "uuid": "3d673400-a6d3-9311-6cf9-04e6c07e3a96", "time": 1, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -5, "y": -2, "z": -1}], "uuid": "79673a55-db9e-68ba-29bd-efdec4aae102", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "2.5", "y": "-2.5", "z": "4"}], "uuid": "52182b3f-d60b-0636-fb10-c1f090563030", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.1}], "uuid": "6d97f040-487d-78c6-1e20-b94f0a739c6e", "time": 1, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "a2870a79-33f5-77b5-8ed9-74eabcf4ea21", "time": 0.5, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.1}], "uuid": "b7b3dbd5-bb9c-3b06-7367-44daaeaa888c", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "243936f8-b9c6-d179-6a92-d5c9a01b4340": {"name": "LeftHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -20, "y": "0", "z": "0"}], "uuid": "b1c8e66f-3964-ba01-290b-ebf3fc9bee51", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -20, "y": "0", "z": "0"}], "uuid": "81253ef9-6f58-863e-1d09-9eb5f44a82e4", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 17.5, "y": "0", "z": "0"}], "uuid": "5467dcdd-d28a-0251-ff19-b558efa003ab", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-1.5", "y": "0", "z": "-2.5"}], "uuid": "d48e069b-f2fb-1518-05d0-96da4c256d3e", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [-11.833766527588438, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [11.833766527588438, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": -1}], "uuid": "3663842c-75bb-ca6c-649d-209e612efb46", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.14626883849719247, -0.1], "bezier_left_value": [0, -0.10029850746268656, 0], "bezier_right_time": [0.1, 0.14626883849719247, 0.1], "bezier_right_value": [0, 0.10029850746268656, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": -1}], "uuid": "a17c627e-33c9-9658-98c6-c7b773a90aeb", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.14626883849719247, -0.1], "bezier_left_value": [0, -0.10029850746268656, 0], "bezier_right_time": [0.1, 0.14626883849719247, 0.1], "bezier_right_value": [0, 0.10029850746268656, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": 1}], "uuid": "e3d27098-5d6e-e696-88bd-a702b16f4f41", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.15398031158005787, -0.14626883849719247], "bezier_left_value": [0, 0.10029850746268656, 0.05731343283582089], "bezier_right_time": [0.1, 0.15398031158005787, 0.14626883849719247], "bezier_right_value": [0, -0.10029850746268656, -0.05731343283582089]}, {"channel": "position", "data_points": [{"x": 0, "y": 1, "z": 0.25}], "uuid": "754fd726-da39-e4fc-d11e-9f9c6bdb7d2a", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.04216395187850942], "bezier_left_value": [0, 0, -0.27940298507462685], "bezier_right_time": [0.1, 0.1, 0.04216395187850942], "bezier_right_value": [0, 0, 0.27940298507462685]}]}, "ce3e2383-010d-03b5-2c18-1f82ea6055d8": {"name": "RightHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 17.5, "y": 0, "z": 0}], "uuid": "f1e28bc2-921f-2079-b7a6-ab555d257372", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 17.5, "y": 0, "z": 0}], "uuid": "a75487c3-d575-c784-034f-aed9dbd2e29b", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-1.5", "y": 0, "z": 2.5}], "uuid": "731b343e-c86f-341b-9ba5-3c9b04cde013", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [-11.833766527588438, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [11.833766527588438, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -20, "y": 0, "z": 0}], "uuid": "025117e5-0c6a-4dbc-102a-7a7520c431b4", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": 1}], "uuid": "dc95c546-8a5d-13cb-7337-5ce93eba20dc", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.15398031158005787, -0.14626883849719247], "bezier_left_value": [0, 0.10029850746268656, 0.05731343283582089], "bezier_right_time": [0.1, 0.15398031158005787, 0.14626883849719247], "bezier_right_value": [0, -0.10029850746268656, -0.05731343283582089]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": 1}], "uuid": "c27e858b-635b-84ce-e18c-d653b1393dda", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.15398031158005787, -0.14626883849719247], "bezier_left_value": [0, 0.10029850746268656, 0.05731343283582089], "bezier_right_time": [0.1, 0.15398031158005787, 0.14626883849719247], "bezier_right_value": [0, -0.10029850746268656, -0.05731343283582089]}, {"channel": "position", "data_points": [{"x": 0, "y": 1, "z": 0.25}], "uuid": "d404c388-5abe-9471-4ff1-4ccc14ef74cf", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.04216395187850942], "bezier_left_value": [0, 0, -0.27940298507462685], "bezier_right_time": [0.1, 0.1, 0.04216395187850942], "bezier_right_value": [0, 0, 0.27940298507462685]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": -1}], "uuid": "d718ff0f-24de-5b3a-7ac7-1c2dea76efeb", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.14626883849719247, -0.1], "bezier_left_value": [0, -0.10029850746268656, 0], "bezier_right_time": [0.1, 0.14626883849719247, 0.1], "bezier_right_value": [0, 0.10029850746268656, 0]}]}, "becce561-f622-7592-514f-5aede933e7be": {"name": "Ring", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": "0", "z": 2}], "uuid": "784c7ed2-8995-f841-794f-b8369a2d3594", "time": 0, "color": -1, "interpolation": "linear"}]}, "a247b8fc-3e11-172d-2179-4eebf17e6fe8": {"name": "Shoulders", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 15, "y": "0", "z": "0"}], "uuid": "41147126-2d39-7626-f4e3-6c3f474e861b", "time": 0, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "f61b4ada-c610-68a5-cbd3-c21169ad58cd", "name": "jumpup", "loop": "hold", "override": false, "length": 0.16666666666666666, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"becce561-f622-7592-514f-5aede933e7be": {"name": "Ring", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 2, "z": "0"}], "uuid": "05b17f12-8ccd-ac91-0618-7dd40b4a8530", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "fe2e0abc-2af9-c352-6eb2-4634281b5cd3", "time": 0, "color": -1, "interpolation": "catmullrom"}]}, "9d7c0b25-cc44-6a43-9602-09df292fbfff": {"name": "FrontVeil", "type": "bone", "keyframes": [{"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "be0da3e6-47ae-05f4-8940-2009c22e8963", "time": 0.16666666666666666, "color": -1, "uniform": true, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1.05", "z": "1"}], "uuid": "2dd3be75-45f8-20e4-b63d-6175951ec733", "time": 0, "color": -1, "uniform": false, "interpolation": "catmullrom"}]}, "f052b1a8-653c-610b-fba5-c6173a05c90b": {"name": "RightVeil", "type": "bone", "keyframes": [{"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "d810469b-e23b-2f41-2b8d-8307a2cc093f", "time": 0.16666666666666666, "color": -1, "uniform": true, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": 1.1, "z": "1"}], "uuid": "9bb5026e-2abb-0429-ef19-827128ba3c61", "time": 0, "color": -1, "uniform": false, "interpolation": "catmullrom"}]}, "9cb55e3c-c096-2ba6-c5bc-b3a31300ae63": {"name": "LeftVeil", "type": "bone", "keyframes": [{"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "fb255309-80ea-55ed-09c3-52028ef80cdc", "time": 0.16666666666666666, "color": -1, "uniform": true, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": 1.1, "z": "1"}], "uuid": "829a6611-9210-f0de-f306-0d4cd7677b36", "time": 0, "color": -1, "uniform": false, "interpolation": "catmullrom"}]}, "a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "4e457d93-2a58-a25e-ddd3-6c14cc5b810c", "time": 0, "color": -1, "interpolation": "catmullrom"}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "10", "y": "10", "z": "-5"}], "uuid": "5f7d5bd9-8b0c-8ecf-b600-e1e5fe9a3d0d", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 2.5, "y": 10, "z": -5}], "uuid": "8e31df47-0210-d50c-c2b0-e050a3a46a3e", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom"}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "10", "y": -10, "z": 5}], "uuid": "9d1a68eb-2330-d413-8ca5-7bbf202ab8c8", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 2.5, "y": -10, "z": 5}], "uuid": "1df0acd1-40fb-1edd-3db8-8a6bea7f8d04", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom"}]}, "069d4014-e378-cde6-ed3f-3b8fc92c9750": {"name": "MidSection", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "8e7b8749-f22a-5adb-f4fe-4f30af07a500", "time": 0, "color": -1, "interpolation": "catmullrom"}]}, "39b607ae-53e6-6156-41f8-11a554e1e77f": {"name": "<PERSON><PERSON><PERSON>", "type": "bone", "keyframes": [{"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "015f6e27-c773-346c-ee92-c011e03e3a33", "time": 0.16666666666666666, "color": -1, "uniform": true, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.1, "z": 1}], "uuid": "a1e642b4-3ad0-3886-6f04-6692ad532959", "time": 0, "color": -1, "uniform": false, "interpolation": "catmullrom"}]}, "*************-d6e3-2315-015aa3b6fb4d": {"name": "FrontScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "c7dbb77b-1872-c526-abce-df16fd51ee68", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "ffbe9921-c492-1de9-6671-32b3ba8b9b56", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom"}]}, "a8267e42-e1ec-7aa4-67ce-f3079d498125": {"name": "BackScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "5", "y": "0", "z": "0"}], "uuid": "8f89c8a1-2b0a-3901-5e0f-cc42ed25c6d9", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "c2b242af-d3e2-70be-1d46-6f122ceb7404", "time": 0, "color": -1, "interpolation": "catmullrom"}]}, "0c083c53-995e-71ab-f3e1-dcb04bd31c4a": {"name": "Legs", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "c1c5bf1b-4ff1-43b9-1f2f-b442fd212159", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "63dee6db-c901-0833-a79d-a74884b37245", "time": 0, "color": -1, "interpolation": "catmullrom"}]}}}, {"uuid": "6e4e0e4c-88a1-9ebc-fb53-718697f7401a", "name": "jumpdown", "loop": "hold", "override": false, "length": 0, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"9d7c0b25-cc44-6a43-9602-09df292fbfff": {"name": "FrontVeil", "type": "bone", "keyframes": [{"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "be0da3e6-47ae-05f4-8940-2009c22e8963", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}, "f052b1a8-653c-610b-fba5-c6173a05c90b": {"name": "RightVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": 5}], "uuid": "58d93168-adc7-3510-7ac3-a03a61a538bd", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "d810469b-e23b-2f41-2b8d-8307a2cc093f", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}, "9cb55e3c-c096-2ba6-c5bc-b3a31300ae63": {"name": "LeftVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -5}], "uuid": "11bcbda6-28a4-bfaf-7d5a-bbc364b3258d", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "fb255309-80ea-55ed-09c3-52028ef80cdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}, "a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 10, "y": 0, "z": 0}], "uuid": "2adf97f7-eda4-0ff4-ae33-a95d9df92d06", "time": 0, "color": -1, "interpolation": "linear"}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-30", "y": "5", "z": "-22.5"}], "uuid": "f6b751ab-0c7e-ac3d-1b84-429e5346e9f4", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -0.75, "y": "0", "z": "0"}], "uuid": "e5616aa1-521b-f43c-15b6-74337b501b97", "time": 0, "color": -1, "interpolation": "linear"}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-30", "y": -5, "z": 22.5}], "uuid": "434a8d95-331e-a8f0-bd64-cbbb0f24cb18", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0.75, "y": "0", "z": "0"}], "uuid": "788ef582-6ce6-d823-8760-2bfa66393a21", "time": 0, "color": -1, "interpolation": "linear"}]}, "069d4014-e378-cde6-ed3f-3b8fc92c9750": {"name": "MidSection", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -15, "y": 0, "z": 0}], "uuid": "881bd391-4bc0-e452-c93a-22c95512dc6b", "time": 0, "color": -1, "interpolation": "linear"}]}, "39b607ae-53e6-6156-41f8-11a554e1e77f": {"name": "<PERSON><PERSON><PERSON>", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -7.5, "y": "0", "z": "0"}], "uuid": "ff32151b-1421-d643-821f-7663122494d1", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 0.9, "z": 1}], "uuid": "c01b211c-cc71-a557-45c8-9dd01250a82f", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "*************-d6e3-2315-015aa3b6fb4d": {"name": "FrontScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": 0, "z": 0}], "uuid": "e8a94223-6bc3-a992-e99b-2f8bfbcab1b0", "time": 0, "color": -1, "interpolation": "linear"}]}, "a8267e42-e1ec-7aa4-67ce-f3079d498125": {"name": "BackScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "5", "y": "0", "z": "0"}], "uuid": "8f89c8a1-2b0a-3901-5e0f-cc42ed25c6d9", "time": 0, "color": -1, "interpolation": "linear"}]}, "0c083c53-995e-71ab-f3e1-dcb04bd31c4a": {"name": "Legs", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 1, "z": "0"}], "uuid": "c1c5bf1b-4ff1-43b9-1f2f-b442fd212159", "time": 0, "color": -1, "interpolation": "linear"}]}, "becce561-f622-7592-514f-5aede933e7be": {"name": "Ring", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 2, "z": "0"}], "uuid": "bcc165ea-94d4-308b-0d73-bd2ae42b0103", "time": 0, "color": -1, "interpolation": "linear"}]}, "fd4c51f4-3e9f-0590-04fe-6d3a2c8a4ee8": {"name": "Neck", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 15, "y": "0", "z": "0"}], "uuid": "610ebd42-dc5a-eae2-808a-bcf5f47f765f", "time": 0, "color": -1, "interpolation": "linear"}]}, "c419ee69-4273-618b-53a8-7c054f6c99a2": {"name": "TheVeil", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 0.5, "z": "0"}], "uuid": "a2992787-40c2-5136-fa60-d9af3af753c1", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 0.9, "z": 1}], "uuid": "79b35d5d-a85c-0feb-b96e-739f3017a965", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "07f1c422-136b-88b2-3301-0d1a14dd6560": {"name": "LeftScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -5}], "uuid": "9310fe3c-dc0b-4ab6-52f3-3f80d1a5de92", "time": 0, "color": -1, "interpolation": "linear"}]}, "fd2c92eb-7659-a0b9-2254-118d0dad40f5": {"name": "RightScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": 5}], "uuid": "919704eb-39b0-2181-dee4-febc987fc9f5", "time": 0, "color": -1, "interpolation": "linear"}]}, "243936f8-b9c6-d179-6a92-d5c9a01b4340": {"name": "LeftHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-10", "y": "-5", "z": "-1.5"}], "uuid": "17769e87-9a66-9999-be53-7a52afe91e67", "time": 0, "color": -1, "interpolation": "linear"}]}, "ce3e2383-010d-03b5-2c18-1f82ea6055d8": {"name": "RightHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": 7.5, "z": 0}], "uuid": "cc2e72bd-58d5-49cb-604f-c9d3745ad58a", "time": 0, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "af9fc83b-944e-d445-8014-244e5dff7ff5", "name": "fall", "loop": "loop", "override": false, "length": 0.5, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"9d7c0b25-cc44-6a43-9602-09df292fbfff": {"name": "FrontVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -25, "y": "0", "z": "0"}], "uuid": "3163bf20-7704-e97e-13a4-0ca86be0e864", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-27.5", "y": "-2.5", "z": "-5"}], "uuid": "3882c8e0-d963-1804-52c2-ca2300898bcb", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-27.5", "y": 2.5, "z": 5}], "uuid": "aa092f20-cd5e-9231-3514-c354c343b4fc", "time": 0.3333333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -25, "y": "0", "z": "0"}], "uuid": "df94aaae-2a30-0f4c-8d29-f1fd88547213", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "be0da3e6-47ae-05f4-8940-2009c22e8963", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}, "f052b1a8-653c-610b-fba5-c6173a05c90b": {"name": "RightVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-15", "y": -20, "z": 35}], "uuid": "361098d3-5622-4e62-d23d-************", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-20", "y": -10, "z": 35}], "uuid": "f27eb4a0-6b6f-8d24-50b7-d94426d3a047", "time": 0.33333333333333337, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -25, "y": -15, "z": 25}], "uuid": "578dd8f7-95ec-4691-751d-983b9aff510c", "time": 0.16666666666666669, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-15", "y": -20, "z": 35}], "uuid": "ed4fb05f-2c6a-514d-363a-86e65bcf11cd", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 2, "y": "0", "z": "0"}], "uuid": "4c9bbfc4-75c8-0f98-034e-fff2f729971c", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 1.25, "y": 0, "z": 0}], "uuid": "fde5170b-cd22-2fcd-5def-3d5b3bd21136", "time": 0.33333333333333337, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 1.25, "y": 2, "z": 1}], "uuid": "ea6ba27f-4d1a-9f83-1ae6-26dd16295b56", "time": 0.16666666666666669, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 2, "y": "0", "z": "0"}], "uuid": "686a7404-9964-eb95-368a-9d9e673fd710", "time": 0.5, "color": -1, "interpolation": "linear"}]}, "9cb55e3c-c096-2ba6-c5bc-b3a31300ae63": {"name": "LeftVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-15", "y": 20, "z": -35}], "uuid": "8069eee4-0260-1550-3e47-18493592cd22", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-20", "y": "10", "z": "-35"}], "uuid": "83163d43-4149-07a9-6b9d-8037ffc9fa9d", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -25, "y": 15, "z": -25}], "uuid": "5653d77d-e3d0-4b31-d6e1-4f864a2ce5e2", "time": 0.3333333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-15", "y": 20, "z": -35}], "uuid": "0aa31b2f-bf65-c210-9316-8339fa4a6a84", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -2, "y": "0", "z": "0"}], "uuid": "74e272ec-4d85-93f9-8b7a-afb7fa64caff", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -1.25, "y": 0, "z": 0}], "uuid": "1da1761c-a8b2-4973-9783-89b3659435a5", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -1.25, "y": 2, "z": 1}], "uuid": "e95cbf31-a3fe-8e77-1413-ffda8a3def2b", "time": 0.3333333333333333, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -2, "y": "0", "z": "0"}], "uuid": "e1e24459-8053-1dfb-4d14-3e0c972c8a58", "time": 0.5, "color": -1, "interpolation": "linear"}]}, "a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 10, "y": 0, "z": "2.5"}], "uuid": "2adf97f7-eda4-0ff4-ae33-a95d9df92d06", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.05833333333333334, -0.1, -0.05833333333333334], "bezier_left_value": [1.75, 0, 0], "bezier_right_time": [0.05833333333333334, 0.1, 0.05833333333333334], "bezier_right_value": [-1.75, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 10, "y": 0, "z": 2.5}], "uuid": "940fd2e5-0153-e549-831a-0d3990d01ea9", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.05833333333333334, -0.1, -0.05833333333333334], "bezier_left_value": [1.75, 0, 0], "bezier_right_time": [0.05833333333333334, 0.1, 0.05833333333333334], "bezier_right_value": [-1.75, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "12.5", "y": 0, "z": 0}], "uuid": "41a05ba5-1a8e-a151-985d-061a9d797588", "time": 0.375, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.03692639050271702, -0.1, -0.03450578569218028], "bezier_left_value": [-0.21958860775756062, 0, -1.3216134431426485], "bezier_right_time": [0.03692639050271702, 0.1, 0.03450578569218028], "bezier_right_value": [0.21958860775756062, 0, 1.3216134431426485]}, {"channel": "rotation", "data_points": [{"x": 10, "y": 0, "z": "-2.5"}], "uuid": "8eceae47-f9df-a0de-5c51-6092d8a6a762", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.05833333333333334, -0.1, -0.05833333333333334], "bezier_left_value": [-1.75, 0, 0], "bezier_right_time": [0.05833333333333334, 0.1, 0.05833333333333334], "bezier_right_value": [1.75, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "7.5", "y": 0, "z": 0}], "uuid": "cbe61c21-167a-ae06-49a7-f9c294fc31a4", "time": 0.125, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.03692639050271702, -0.1, -0.038073609497283], "bezier_left_value": [0.21958860775756062, 0, 1.3216134431426485], "bezier_right_time": [0.03692639050271702, 0.1, 0.038073609497283], "bezier_right_value": [-0.21958860775756062, 0, -1.3216134431426485]}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -70, "y": 60, "z": -50}], "uuid": "e04ca357-8676-f721-21a3-eab050c693e4", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": -1}], "uuid": "c837e0aa-a218-db0a-c2da-1801d964863d", "time": 0, "color": -1, "interpolation": "linear"}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -70, "y": "-60", "z": "50"}], "uuid": "434a8d95-331e-a8f0-bd64-cbbb0f24cb18", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "c0929ea0-e3a6-4b11-e6a5-521d5852ba74", "time": 0, "color": -1, "interpolation": "linear"}]}, "069d4014-e378-cde6-ed3f-3b8fc92c9750": {"name": "MidSection", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-15", "y": 0, "z": "-2.5"}], "uuid": "80f991f1-382e-3631-14d3-dcc42f9aa426", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.05183437863111328, -0.1, -0.05833333333333334], "bezier_left_value": [-1.5, 0, 0], "bezier_right_time": [0.05183437863111328, 0.1, 0.05833333333333334], "bezier_right_value": [1.5, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-15", "y": 0, "z": 2.5}], "uuid": "30936ffe-e106-2553-37b1-70334cf75f9e", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.05183437863111328, -0.1, -0.05833333333333334], "bezier_left_value": [1.5, 0, 0], "bezier_right_time": [0.05183437863111328, 0.1, 0.05833333333333334], "bezier_right_value": [-1.5, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-15", "y": 0, "z": "-2.5"}], "uuid": "61849326-6baa-419e-ceeb-af0b029457c1", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.05183437863111328, -0.1, -0.05833333333333334], "bezier_left_value": [-1.5, 0, 0], "bezier_right_time": [0.05183437863111328, 0.1, 0.05833333333333334], "bezier_right_value": [1.5, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-12.5", "y": 0, "z": 0}], "uuid": "675d7472-8b5f-398d-de1b-593dbfe2d3d9", "time": 0.125, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.05183437863111328, -0.1, -0.05833333333333334], "bezier_left_value": [0, 0, -1.8216134431426476], "bezier_right_time": [0.05183437863111328, 0.1, 0.05833333333333334], "bezier_right_value": [0, 0, 1.8216134431426476]}, {"channel": "rotation", "data_points": [{"x": "-17.5", "y": 0, "z": 0}], "uuid": "ec59826a-3e39-8ef2-a370-04e88302857b", "time": 0.375, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.05183437863111328, -0.1, -0.05833333333333334], "bezier_left_value": [0, 0, 1.8216134431426485], "bezier_right_time": [0.05183437863111328, 0.1, 0.05833333333333334], "bezier_right_value": [0, 0, -1.8216134431426485]}]}, "39b607ae-53e6-6156-41f8-11a554e1e77f": {"name": "<PERSON><PERSON><PERSON>", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -7.5, "y": "0", "z": "0"}], "uuid": "ff32151b-1421-d643-821f-7663122494d1", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": -5, "z": 0}], "uuid": "23cb5ce2-168e-a8f8-c331-ce62f1d345e6", "time": 0.08333333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-8.5", "y": "5", "z": "-1.5"}], "uuid": "94c5a4a4-885b-f641-b519-5d71f648a7ae", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -7.5, "y": "0", "z": "0"}], "uuid": "07ab5003-0c12-ba37-02bc-41c608f21add", "time": 0.25, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -7.5, "y": "0", "z": "0"}], "uuid": "eb227769-449e-714d-e75b-************", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": -5, "z": 0}], "uuid": "24b7db8d-7806-2b22-542b-a736d7e23163", "time": 0.3333333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-8.5", "y": "5", "z": "-1.5"}], "uuid": "b04c6138-5889-6dba-056e-a8e6afc368ec", "time": 0.4166666666666667, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 0.7000000000000001, "z": 1}], "uuid": "c01b211c-cc71-a557-45c8-9dd01250a82f", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "*************-d6e3-2315-015aa3b6fb4d": {"name": "FrontScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -7.5, "y": 0, "z": 0}], "uuid": "e8a94223-6bc3-a992-e99b-2f8bfbcab1b0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -7.5, "y": 0, "z": 0}], "uuid": "21c43d01-a55c-7281-aa76-85ebbc69221c", "time": 0.25, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -7.5, "y": 0, "z": 0}], "uuid": "29beb12d-a58f-596d-d629-d0d218fa5933", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.5000000000000004}], "uuid": "13835515-a6fd-85a9-ca3e-0b75ee688c54", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.3000000000000003}], "uuid": "d7bc089f-7a13-ce60-d6be-2ec006d12632", "time": 0.25, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.5000000000000004}], "uuid": "8b89e15e-45a7-f9ca-8371-bbf6a6a38a92", "time": 0.5, "color": -1, "uniform": false, "interpolation": "linear"}]}, "a8267e42-e1ec-7aa4-67ce-f3079d498125": {"name": "BackScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 10, "y": "0", "z": "0"}], "uuid": "8f89c8a1-2b0a-3901-5e0f-cc42ed25c6d9", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 7.5, "y": "0", "z": "0"}], "uuid": "46b4bf9a-34e4-8f76-58da-8de0b05b776e", "time": 0.25, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 10, "y": "0", "z": "0"}], "uuid": "c7919faf-cb7f-7a09-cae4-ee385feb053c", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.5000000000000004}], "uuid": "dc73291b-5701-8ab5-1e9e-ce4d9c72c7be", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.3000000000000003}], "uuid": "4388f8c7-e74a-3502-cac4-c1aa00adc45c", "time": 0.25, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.5000000000000004}], "uuid": "a4f20055-db21-1f11-f533-2977715b9064", "time": 0.5, "color": -1, "uniform": false, "interpolation": "linear"}]}, "0c083c53-995e-71ab-f3e1-dcb04bd31c4a": {"name": "Legs", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 1, "z": "0"}], "uuid": "c1c5bf1b-4ff1-43b9-1f2f-b442fd212159", "time": 0, "color": -1, "interpolation": "linear"}]}, "becce561-f622-7592-514f-5aede933e7be": {"name": "Ring", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": "-2", "z": "0"}], "uuid": "bcc165ea-94d4-308b-0d73-bd2ae42b0103", "time": 0, "color": -1, "interpolation": "linear"}]}, "fd4c51f4-3e9f-0590-04fe-6d3a2c8a4ee8": {"name": "Neck", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 15, "y": "0", "z": "0"}], "uuid": "610ebd42-dc5a-eae2-808a-bcf5f47f765f", "time": 0, "color": -1, "interpolation": "linear"}]}, "c419ee69-4273-618b-53a8-7c054f6c99a2": {"name": "TheVeil", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7a751916-4102-8fca-d3d0-6f88d8d0f875", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": "0"}], "uuid": "d4e2ef5a-163b-dea1-86ff-120293185393", "time": 0.25, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "c98ca6c5-d301-95bb-a8b4-cdf89dcd7bdf", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "e0b3a1a0-08cc-eb06-5562-1519462d07ea", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": 0.8, "z": "1"}], "uuid": "bb25b720-88f3-94b8-7017-a12b573b8213", "time": 0.25, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "cf13739b-9978-75aa-26d8-5036d46309a0", "time": 0.5, "color": -1, "uniform": true, "interpolation": "linear"}]}, "07f1c422-136b-88b2-3301-0d1a14dd6560": {"name": "LeftScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -5}], "uuid": "9310fe3c-dc0b-4ab6-52f3-3f80d1a5de92", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -5}], "uuid": "cde3284e-a827-1730-d954-fe76d82a896a", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -6}], "uuid": "44db775a-1c44-94eb-3945-a38b38083baa", "time": 0.25, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "ea18e2e2-ebbb-7b44-08ad-5bc1d1b083b4", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": 1.0499999999999998, "z": "1"}], "uuid": "d4a65508-37f6-f9b3-4a73-a200a3d57471", "time": 0.25, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "be5b6572-32bb-eddd-877d-fc1e9351a515", "time": 0.5, "color": -1, "uniform": true, "interpolation": "linear"}]}, "fd2c92eb-7659-a0b9-2254-118d0dad40f5": {"name": "RightScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": 5}], "uuid": "919704eb-39b0-2181-dee4-febc987fc9f5", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": 6}], "uuid": "eb1225d9-a38f-cc06-852f-c278ccee0a36", "time": 0.25, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": 5}], "uuid": "7af2cffa-0e55-368e-b856-ca17cd8c8817", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": 1.0499999999999998, "z": "1"}], "uuid": "22c26e85-29eb-9b2e-48fb-810ee3138a4d", "time": 0.25, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "abb188d6-bc0b-6d3a-784c-e14a46b0dd49", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "ccc0c311-50d3-0c18-2fb1-6dd3c73bc42d", "time": 0.5, "color": -1, "uniform": true, "interpolation": "linear"}]}, "243936f8-b9c6-d179-6a92-d5c9a01b4340": {"name": "LeftHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "7.5", "y": "15", "z": "7.5"}], "uuid": "17769e87-9a66-9999-be53-7a52afe91e67", "time": 0, "color": -1, "interpolation": "linear"}]}, "ce3e2383-010d-03b5-2c18-1f82ea6055d8": {"name": "RightHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-5", "y": "-15", "z": "-7.5"}], "uuid": "cc2e72bd-58d5-49cb-604f-c9d3745ad58a", "time": 0, "color": -1, "interpolation": "linear"}]}, "a247b8fc-3e11-172d-2179-4eebf17e6fe8": {"name": "Shoulders", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "fdfe31cc-4778-418f-6b9f-20b4b2d63a50", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -2.5, "y": 0, "z": 0}], "uuid": "868ea366-656d-ba59-dccb-48cbf3b7d00a", "time": 0.25, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-2.5", "y": "-5", "z": "0.25"}], "uuid": "09d460e8-b9cb-d98a-ab1b-166a63073630", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-1.5", "y": "5", "z": "0"}], "uuid": "1efec17c-092e-7076-7d95-6edf7d22ff0b", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "ddce168b-2216-c192-2cc6-81b28c389846", "time": 0.5, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "cc30b00c-0414-2251-2569-4fcc5975d331", "name": "crouch", "loop": "loop", "override": false, "length": 3, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"d0f57d3a-5c99-127f-bb1e-dbd873765b47": {"name": "Noggle", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "05d13715-26ca-9b99-e0a6-618620beb724", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": -23, "z": -5}], "uuid": "ffffdec7-a6a1-5cc5-e6be-9e3451c1f771", "time": 0, "color": -1, "interpolation": "linear"}]}, "a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 87.5, "y": "0", "z": "0"}], "uuid": "6dbffb2d-ec3b-728e-43f2-fcd6ddf1fe06", "time": 0, "color": -1, "interpolation": "linear"}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -145.01382941887596, "y": -35.53341257492548, "z": 16.022839689891953}], "uuid": "500f1198-c90d-e9ed-7fa3-d85c488d9f73", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -145.01382941887596, "y": -35.53341257492548, "z": 16.022839689891953}], "uuid": "523dea99-5bc1-de6a-63d5-36c7e2192321", "time": 3, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-152.5", "y": "-40", "z": "22.5"}], "uuid": "85b510ec-bdcb-97d1-55f2-a0493439e226", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": -1, "y": -6, "z": 3}], "uuid": "e3f91d3f-2be7-d433-a7b7-d9240a1047a5", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.3405342251558678, -0.1, -0.3305119657743733], "bezier_left_value": [0.29080193438048635, 0, -1.031387235515548], "bezier_right_time": [0.3405342251558678, 0.1, 0.3305119657743733], "bezier_right_value": [-0.29080193438048635, 0, 1.031387235515548]}, {"channel": "position", "data_points": [{"x": -1.5, "y": -6, "z": 2.25}], "uuid": "4bbf7bda-c039-c336-a889-7a0f51dd0e97", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18518920474270317, -0.1, -0.2753895391761536], "bezier_left_value": [-0.36350241797560795, 0, 0.2395127069892371], "bezier_right_time": [0.18518920474270317, 0.1, 0.2753895391761536], "bezier_right_value": [0.36350241797560795, 0, -0.2395127069892371]}, {"channel": "position", "data_points": [{"x": -1, "y": -6, "z": 3}], "uuid": "dd910b2e-a3db-661c-5c5b-2f3ed24c1e80", "time": 3, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.3405342251558678, -0.1, -0.3305119657743733], "bezier_left_value": [0.29080193438048635, 0, -1.031387235515548], "bezier_right_time": [0.3405342251558678, 0.1, 0.3305119657743733], "bezier_right_value": [-0.29080193438048635, 0, 1.031387235515548]}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-225", "y": "75", "z": "-55"}], "uuid": "89342721-844b-fae8-00bb-8c000968813c", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-215", "y": "72.5", "z": "-45"}], "uuid": "c9ed8528-0385-c01d-9583-fcc953864ce2", "time": 1.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "-225", "y": "75", "z": "-55"}], "uuid": "9b73c845-a2e2-61fd-ffd9-9d03400d572d", "time": 3, "color": -1, "interpolation": "linear"}]}, "069d4014-e378-cde6-ed3f-3b8fc92c9750": {"name": "MidSection", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -35, "y": "0", "z": "0"}], "uuid": "6adb5ada-d39d-356d-4544-0fce1fb5ba79", "time": 0, "color": -1, "interpolation": "linear"}]}, "*************-d6e3-2315-015aa3b6fb4d": {"name": "FrontScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -17.5, "y": "0", "z": "0"}], "uuid": "9518cbf9-cc60-5986-fb6f-2f2df0a7a79d", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": 0.6, "z": "1"}], "uuid": "0a315223-592d-a732-3b0c-73a9dece05bf", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "07f1c422-136b-88b2-3301-0d1a14dd6560": {"name": "LeftScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -7.5, "y": 0, "z": 0}], "uuid": "f5a3d7b0-1980-b45d-e35d-b0df62efd609", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": 0.8, "z": "1"}], "uuid": "6922f8a2-6e20-afc1-50de-497d4dbb4e84", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "fd2c92eb-7659-a0b9-2254-118d0dad40f5": {"name": "RightScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -7.5, "y": "0", "z": "0"}], "uuid": "5142bfb8-2774-526d-34fe-bc4e684a8d54", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": 0.8, "z": "1"}], "uuid": "f7d3bbc7-d62a-f139-5d7f-567bb5e2bbcd", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "ce3e2383-010d-03b5-2c18-1f82ea6055d8": {"name": "RightHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 12.5, "y": "0", "z": "0"}], "uuid": "7231d358-2af8-32f7-6064-3fb321400ff6", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": -0.75, "z": -2}], "uuid": "01aca005-5b9d-ae19-c1b0-13326f56049c", "time": 0, "color": -1, "interpolation": "linear"}]}, "fd4c51f4-3e9f-0590-04fe-6d3a2c8a4ee8": {"name": "Neck", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-7.5", "y": "10", "z": "15"}], "uuid": "01416e17-2e65-f0ae-5ec4-078f33ae6731", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.4106666666666666], "bezier_left_value": [0, 0, 1.8265047890812893], "bezier_right_time": [0.1, 0.1, 0.4106666666666666], "bezier_right_value": [0, 0, -1.8265047890812893]}, {"channel": "rotation", "data_points": [{"x": "-7.5", "y": "10", "z": "10"}], "uuid": "359258f4-1a32-096f-4e46-265aba9a063a", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.39146666666666663], "bezier_left_value": [0, 0, -1.323462695779034], "bezier_right_time": [0.1, 0.1, 0.39146666666666663], "bezier_right_value": [0, 0, 1.323462695779034]}, {"channel": "rotation", "data_points": [{"x": "-7.5", "y": "10", "z": "15"}], "uuid": "3f7aebaa-f378-0420-9192-e4fefa99d9d9", "time": 3, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.4106666666666666], "bezier_left_value": [0, 0, 1.8265047890812893], "bezier_right_time": [0.1, 0.1, 0.4106666666666666], "bezier_right_value": [0, 0, -1.8265047890812893]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "99d51211-6d17-cbcb-08ba-731e25b36433", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.5836666666666667, -0.4106666666666666, -0.26666666666666666], "bezier_left_value": [0.13678756476683937, -0.02616580310880831, 0], "bezier_right_time": [0.5836666666666667, 0.4106666666666666, 0.26666666666666666], "bezier_right_value": [-0.13678756476683937, 0.02616580310880831, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "f5f9f8b8-013f-7f03-526e-d3ead87f6705", "time": 3, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.5836666666666667, -0.4106666666666666, -0.26666666666666666], "bezier_left_value": [0.13678756476683937, -0.02616580310880831, 0], "bezier_right_time": [0.5836666666666667, 0.4106666666666666, 0.26666666666666666], "bezier_right_value": [-0.13678756476683937, 0.02616580310880831, 0]}, {"channel": "position", "data_points": [{"x": 0.5, "y": 0.5, "z": 0}], "uuid": "08bfed34-e678-5b1b-224c-3980ac91513a", "time": 2, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.35919999999999996, -0.26666666666666666, -0.26666666666666666], "bezier_left_value": [0.14922279792746113, -0.024870466321243522, 0], "bezier_right_time": [0.35919999999999996, 0.26666666666666666, 0.26666666666666666], "bezier_right_value": [-0.14922279792746113, 0.024870466321243522, 0]}]}, "9d7c0b25-cc44-6a43-9602-09df292fbfff": {"name": "FrontVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -7.5}], "uuid": "7f33d903-03d5-b1f0-014f-359235a77115", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -5}], "uuid": "2600a4e0-3508-5bbf-1832-2cb70d536417", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -7.5}], "uuid": "4d2af63f-63c2-d036-4554-11c3ab0321f3", "time": 3, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}]}, "f052b1a8-653c-610b-fba5-c6173a05c90b": {"name": "RightVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -10}], "uuid": "e61831c8-fdb0-d501-cab1-9f70d5a83118", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -7.5}], "uuid": "7d47ad78-0618-d1cf-5bef-f5b9b8b85fda", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -10}], "uuid": "7287e2f0-5244-7391-c87e-24253ff91b2d", "time": 3, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}]}, "9cb55e3c-c096-2ba6-c5bc-b3a31300ae63": {"name": "LeftVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -10}], "uuid": "a6297519-c085-60c0-1017-216dbd20c125", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -5}], "uuid": "ccda2849-a2b0-db5f-36f7-68dd5cddcdaf", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -10}], "uuid": "f6cc5d64-e06c-7649-8488-cf3e443a7a02", "time": 3, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}]}, "becce561-f622-7592-514f-5aede933e7be": {"name": "Ring", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 2.5, "z": "0"}], "uuid": "6e1fed7e-8e29-35fb-16da-23d3120b3a15", "time": 0, "color": -1, "interpolation": "linear"}]}, "e5e69819-52db-c577-1116-b04b331d7732": {"name": "LeftEye", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 0.2, "z": "0"}], "uuid": "bcaaa341-c7e6-67ec-2ddd-85b4c36c7dbb", "time": 0, "color": -1, "interpolation": "linear"}]}, "36145320-9f7b-a7be-1d1c-06f5ef5a57f7": {"name": "RightEye", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 0.2, "z": "0"}], "uuid": "2be41c6a-c268-5565-02ba-c0a0e000fb8f", "time": 0, "color": -1, "interpolation": "linear"}]}, "a247b8fc-3e11-172d-2179-4eebf17e6fe8": {"name": "Shoulders", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "073450fc-ee25-6ac1-83c2-618835db12fc", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.30833333333333335, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.30833333333333335, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "1.5", "z": "-0.75"}], "uuid": "d5c63219-1def-dfaf-1687-3c2b8d344db2", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.30833333333333335, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.30833333333333335, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0e9e436f-79d5-a33c-c990-6f10fc580f57", "time": 3, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.30833333333333335, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.30833333333333335, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}]}}}, {"uuid": "df37307f-e7c2-9b6f-63e3-f67a2857cf0b", "name": "crouchwalk", "loop": "loop", "override": false, "length": 1.5, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"d0f57d3a-5c99-127f-bb1e-dbd873765b47": {"name": "Noggle", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 25, "y": 0, "z": 0}], "uuid": "013c1468-ce2e-2e56-2c03-e1f2827330a8", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": -23, "z": -5}], "uuid": "49e4cbf1-5acb-6061-b2f1-ea9c0db577df", "time": 0, "color": -1, "interpolation": "linear"}]}, "a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "87.5", "y": 10, "z": -0.5}], "uuid": "65cd0227-f8b7-98e8-de58-7d26810a0309", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 77.5, "y": 0, "z": 0}], "uuid": "14642eea-d84b-0dcc-f050-153a20eaab2b", "time": 0.375, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [0, 4, 0], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [0, -4, 0]}, {"channel": "rotation", "data_points": [{"x": "87.5", "y": -10, "z": 0.5}], "uuid": "74f6bf58-f79a-fdc2-0608-1ada957ad6e6", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 77.5, "y": 0, "z": 0}], "uuid": "d39c243a-4e2f-7a85-e67a-09f7b6c72163", "time": 1.125, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [0, -4, 0], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [0, 4, 0]}, {"channel": "rotation", "data_points": [{"x": "87.5", "y": 10, "z": -0.5}], "uuid": "3357e104-5828-f1bc-a87e-6d5b15de1d2d", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-202.5", "y": -62.5, "z": 25}], "uuid": "1ae9c428-8450-0a84-07b9-a02aeb947a7f", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.225, -0.18333333333333335, -0.12456722190654683], "bezier_left_value": [-11, -47.25, 16.07586709485796], "bezier_right_time": [0.225, 0.18333333333333335, 0.12456722190654683], "bezier_right_value": [11, 47.25, -16.07586709485796]}, {"channel": "rotation", "data_points": [{"x": -85, "y": -35, "z": 15}], "uuid": "df6de379-ae78-d853-11a1-bf481a096303", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.14166666666666666, -0.11965377752523747], "bezier_left_value": [45.75, 22.75, -24.4063995085028], "bezier_right_time": [0.14166666666666666, 0.14166666666666666, 0.11965377752523747], "bezier_right_value": [-45.75, -22.75, 24.4063995085028]}, {"channel": "rotation", "data_points": [{"x": "-202.5", "y": -62.5, "z": 25}], "uuid": "7830fd4b-e3f8-350b-aa13-83243850449c", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.225, -0.18333333333333335, -0.12456722190654683], "bezier_left_value": [-11, -47.25, 16.07586709485796], "bezier_right_time": [0.225, 0.18333333333333335, 0.12456722190654683], "bezier_right_value": [11, 47.25, -16.07586709485796]}, {"channel": "position", "data_points": [{"x": 1, "y": 2, "z": 0}], "uuid": "bd5a1bd4-cafa-d4a0-b4e3-589fe7a28db1", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, -0.25], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0.25]}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 2.5}], "uuid": "9541fc30-0c1d-9b65-9404-3c7a8eab22bb", "time": 0.375, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0.48697394789579157, 1.5150300601202404, -0.6006371190310401], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [-0.48697394789579157, -1.5150300601202404, 0.6006371190310401]}, {"channel": "position", "data_points": [{"x": -1, "y": -6, "z": 2}], "uuid": "57649ee4-2090-a8a3-a666-57ec4d94b48b", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1219152785669663], "bezier_left_value": [0, 0, 1.0106569926510403], "bezier_right_time": [0.1, 0.1, 0.1219152785669663], "bezier_right_value": [0, 0, -1.0106569926510403]}, {"channel": "position", "data_points": [{"x": 1, "y": 2, "z": 0}], "uuid": "bc59fbd9-c250-8af0-d29e-024450d30ebb", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, -0.25], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0.25]}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-202.5", "y": 62.5, "z": -25}], "uuid": "b77a901b-1920-7096-2062-d1e2e42d3be6", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.225, -0.18333333333333335, -0.12456722190654683], "bezier_left_value": [-11, 47.25, -16.07586709485796], "bezier_right_time": [0.225, 0.18333333333333335, 0.12456722190654683], "bezier_right_value": [11, -47.25, 16.07586709485796]}, {"channel": "rotation", "data_points": [{"x": -85, "y": 35, "z": -15}], "uuid": "03235278-7b29-63e8-c412-ccb2f7600708", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.14166666666666666, -0.11965377752523747], "bezier_left_value": [45.75, -22.75, 24.4063995085028], "bezier_right_time": [0.14166666666666666, 0.14166666666666666, 0.11965377752523747], "bezier_right_value": [-45.75, 22.75, -24.4063995085028]}, {"channel": "rotation", "data_points": [{"x": -85, "y": 35, "z": -15}], "uuid": "ec48188a-4c12-944f-511b-6fd75e52fb36", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.14166666666666666, -0.11965377752523747], "bezier_left_value": [45.75, -22.75, 24.4063995085028], "bezier_right_time": [0.14166666666666666, 0.14166666666666666, 0.11965377752523747], "bezier_right_value": [-45.75, 22.75, -24.4063995085028]}, {"channel": "position", "data_points": [{"x": -1, "y": 2, "z": 0}], "uuid": "b50620f7-7dbb-c309-abe3-98a4630eab6f", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.18333333333333335], "bezier_left_value": [0, 0, -0.25], "bezier_right_time": [0.1, 0.1, 0.18333333333333335], "bezier_right_value": [0, 0, 0.25]}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 2.5}], "uuid": "9265ae07-ed6f-2702-c909-21e6e416e66f", "time": 1.125, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [-0.48697394789579157, 1.5150300601202404, -0.6006371190310401], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0.48697394789579157, -1.5150300601202404, 0.6006371190310401]}, {"channel": "position", "data_points": [{"x": 1, "y": -6, "z": 2}], "uuid": "ed6cb815-81f0-5feb-80a4-f5b2391ea7fe", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1219152785669663], "bezier_left_value": [0, 0, 1.0106569926510403], "bezier_right_time": [0.1, 0.1, 0.1219152785669663], "bezier_right_value": [0, 0, -1.0106569926510403]}, {"channel": "position", "data_points": [{"x": 1, "y": -6, "z": 2}], "uuid": "f502c613-ab3c-83f7-2c56-9999176a9273", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1219152785669663], "bezier_left_value": [0, 0, 1.0106569926510403], "bezier_right_time": [0.1, 0.1, 0.1219152785669663], "bezier_right_value": [0, 0, -1.0106569926510403]}]}, "069d4014-e378-cde6-ed3f-3b8fc92c9750": {"name": "MidSection", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-35", "y": 10, "z": 12.5}], "uuid": "330672f0-75d4-d4a8-e08a-c7324f132424", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.14166666666666666, -0.14166666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.14166666666666666, 0.14166666666666666, 0.14166666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -25, "y": 0, "z": 0}], "uuid": "7a80d978-3ddb-f7dd-7e25-06bab4c06eab", "time": 0.375, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [0, 4, 4], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [0, -4, -4]}, {"channel": "rotation", "data_points": [{"x": "-35", "y": -10, "z": -12.5}], "uuid": "63c7cfe4-3e6a-f6bb-e902-0948536ba6d1", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.14166666666666666, -0.14166666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.14166666666666666, 0.14166666666666666, 0.14166666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -25, "y": 0, "z": 0}], "uuid": "fc3c0a4f-cb6b-3a97-9556-f91ed98753f4", "time": 1.125, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [0, -4, -4], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [0, 4, 4]}, {"channel": "rotation", "data_points": [{"x": "-35", "y": 10, "z": 12.5}], "uuid": "7beef48c-23d8-0b80-aa16-b256a5f6154e", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.14166666666666666, -0.14166666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.14166666666666666, 0.14166666666666666, 0.14166666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 1, "z": "0"}], "uuid": "c036651b-97c9-3b6c-6925-3fe68445fb12", "time": 0, "color": -1, "interpolation": "linear"}]}, "*************-d6e3-2315-015aa3b6fb4d": {"name": "FrontScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -17.5, "y": 0, "z": 0}], "uuid": "31629d37-6b85-24f1-2fe7-3c8b3743fcd0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": 0.6, "z": "1"}], "uuid": "9216f6ff-746f-e23d-2370-93f6039f27ed", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "07f1c422-136b-88b2-3301-0d1a14dd6560": {"name": "LeftScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -7.5, "y": 0, "z": 0}], "uuid": "5259814b-9edb-e9d8-b41a-2266ee0fc0c0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": 0.8, "z": "1"}], "uuid": "af79725e-8c35-1a0c-432e-fc325049fb58", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "fd2c92eb-7659-a0b9-2254-118d0dad40f5": {"name": "RightScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -7.5, "y": 0, "z": 0}], "uuid": "70cf4865-e80e-c9df-9ba9-c49c8cbf6e82", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": 0.8, "z": "1"}], "uuid": "a8c8fd84-84bd-9145-2c9a-131e830752d8", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "ce3e2383-010d-03b5-2c18-1f82ea6055d8": {"name": "RightHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -12.5, "y": 0, "z": 1}], "uuid": "1ac5ebc9-9ad7-ed84-2a61-50816b61435b", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.225, -0.1, -0.14166666666666666], "bezier_left_value": [0.75, 0, -1], "bezier_right_time": [0.225, 0.1, 0.14166666666666666], "bezier_right_value": [-0.75, 0, 1]}, {"channel": "rotation", "data_points": [{"x": 5, "y": 0, "z": 7.5}], "uuid": "9f7da8b7-91f3-b382-5eae-67c29e8ae800", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.18333333333333335], "bezier_left_value": [-1, 0, 0.5], "bezier_right_time": [0.14166666666666666, 0.1, 0.18333333333333335], "bezier_right_value": [1, 0, -0.5]}, {"channel": "rotation", "data_points": [{"x": 3.75, "y": 0, "z": 2.5}], "uuid": "a89dc2a2-b24f-a026-436b-5f101c93d56e", "time": 1.125, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [2, 0, 2], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [-2, 0, -2]}, {"channel": "rotation", "data_points": [{"x": -12.5, "y": 0, "z": 1}], "uuid": "ce25995a-a49b-d200-403d-6139bdf0fe4e", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.225, -0.1, -0.14166666666666666], "bezier_left_value": [0.75, 0, -1], "bezier_right_time": [0.225, 0.1, 0.14166666666666666], "bezier_right_value": [-0.75, 0, 1]}, {"channel": "position", "data_points": [{"x": 0, "y": 4, "z": "0"}], "uuid": "0905e301-8576-c328-f195-ddf1bddb4e25", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.18333333333333335, -0.13930755505047493], "bezier_left_value": [0, 0.25, 0], "bezier_right_time": [0.1, 0.18333333333333335, 0.13930755505047493], "bezier_right_value": [0, -0.25, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -1.75, "z": -2}], "uuid": "2c7693a3-dc95-3ec5-c5fc-0cb2c87f9cef", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.18333333333333335, -0.1687882213383311], "bezier_left_value": [0, 0, 0.09796279821614196], "bezier_right_time": [0.1, 0.18333333333333335, 0.1687882213383311], "bezier_right_value": [0, 0, -0.09796279821614196]}, {"channel": "position", "data_points": [{"x": 0, "y": 1.88, "z": -1}], "uuid": "33319cd0-1656-97cb-ffb9-719c551d52f0", "time": 1.125, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, -1, -0.5], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 1, 0.5]}, {"channel": "position", "data_points": [{"x": 0, "y": 4, "z": "0"}], "uuid": "651987e4-5581-e77e-a116-c074a3855506", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.18333333333333335, -0.13930755505047493], "bezier_left_value": [0, 0.25, 0], "bezier_right_time": [0.1, 0.18333333333333335, 0.13930755505047493], "bezier_right_value": [0, -0.25, 0]}]}, "fd4c51f4-3e9f-0590-04fe-6d3a2c8a4ee8": {"name": "Neck", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-7.5", "y": 10, "z": 5}], "uuid": "2aac7f59-328d-b259-ab63-01b8bef66cdb", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.225, -0.3273333333333333], "bezier_left_value": [0, 0, -1.8265047890812893], "bezier_right_time": [0.1, 0.225, 0.3273333333333333], "bezier_right_value": [0, 0, 1.8265047890812893]}, {"channel": "rotation", "data_points": [{"x": "-7.5", "y": -10, "z": -5}], "uuid": "9c1975d4-c7b9-ce72-ab9a-a5ef638867bf", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.225, -0.3273333333333333], "bezier_left_value": [0, 0, -1.8265047890812893], "bezier_right_time": [0.1, 0.225, 0.3273333333333333], "bezier_right_value": [0, 0, 1.8265047890812893]}, {"channel": "rotation", "data_points": [{"x": "-7.5", "y": 10, "z": 5}], "uuid": "ee68fa3a-18e2-a72a-2d32-3e6c1f08cd98", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.225, -0.3273333333333333], "bezier_left_value": [0, 0, -1.8265047890812893], "bezier_right_time": [0.1, 0.225, 0.3273333333333333], "bezier_right_value": [0, 0, 1.8265047890812893]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "0feb762a-61b0-47d6-5aa3-d3d39b527d2f", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0.25, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [-0.25, 0, 0]}, {"channel": "position", "data_points": [{"x": -0.5, "y": 0, "z": 0}], "uuid": "190fd642-c563-e2bb-adde-9357f2042859", "time": 0.375, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "ada0ed5e-b4b7-0eaf-6be0-7cbb4f345d8c", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [-0.25, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0.25, 0, 0]}, {"channel": "position", "data_points": [{"x": 0.5, "y": 0, "z": 0}], "uuid": "9f19ba7e-30ad-85da-3c96-0a7740846da0", "time": 1.125, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "3fa1eafa-d98e-e664-d7d8-82da68da2ffe", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0.25, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [-0.25, 0, 0]}]}, "9d7c0b25-cc44-6a43-9602-09df292fbfff": {"name": "FrontVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -7.5}], "uuid": "fa34f6d6-e7e6-f3c0-d6ea-00fdaa23bdeb", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 7.5}], "uuid": "4038fe89-14b6-671c-dd2a-1abe53674b28", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -7.5}], "uuid": "0a0c307c-525c-8c14-0a3a-cc925cffce97", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}]}, "f052b1a8-653c-610b-fba5-c6173a05c90b": {"name": "RightVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -10}], "uuid": "1af7eda3-38bd-e968-6012-b206d734db3f", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 2.5}], "uuid": "f5656d9d-ec3e-e724-968b-952be19cc91f", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -10}], "uuid": "45b935ef-7b58-035b-b74e-480120420c6f", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}]}, "9cb55e3c-c096-2ba6-c5bc-b3a31300ae63": {"name": "LeftVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -2.5}], "uuid": "07c9c567-7612-d501-8b97-28ae2752cfa0", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 10}], "uuid": "186f5874-666f-b806-580c-0f53960d6b8d", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": -2.5}], "uuid": "6f6dc80b-d507-6dd1-99e5-4adfda26549f", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.26666666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.26666666666666666], "bezier_right_value": [0, 0, 0]}]}, "becce561-f622-7592-514f-5aede933e7be": {"name": "Ring", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 4, "z": "0"}], "uuid": "f0cd2433-b901-4934-4ded-2223adf75305", "time": 0, "color": -1, "interpolation": "linear"}]}, "e5e69819-52db-c577-1116-b04b331d7732": {"name": "LeftEye", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 0.2, "z": "0"}], "uuid": "98654c23-254a-e4cd-6457-b2a08f363f1e", "time": 0, "color": -1, "interpolation": "linear"}]}, "36145320-9f7b-a7be-1d1c-06f5ef5a57f7": {"name": "RightEye", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 0.2, "z": "0"}], "uuid": "9c512b7f-f2f1-4d90-84f6-5a5c709bb6aa", "time": 0, "color": -1, "interpolation": "linear"}]}, "a8267e42-e1ec-7aa4-67ce-f3079d498125": {"name": "BackScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": -2.5, "z": 0}], "uuid": "03959016-cfbb-e0c9-f0be-9e88719347ca", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0.5, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, -0.5, 0]}, {"channel": "rotation", "data_points": [{"x": -5, "y": -2.5, "z": 0}], "uuid": "af3cd2af-4d8a-2a84-2474-f3b03057fa78", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0.5, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, -0.5, 0]}, {"channel": "rotation", "data_points": [{"x": -5, "y": 2.5, "z": 0}], "uuid": "b7c6e4a7-9d60-87f6-b425-b8f3cc3e3a09", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, -0.5, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0.5, 0]}, {"channel": "rotation", "data_points": [{"x": -5, "y": 3, "z": 0}], "uuid": "391e48dc-6cd6-7c5c-1947-6b1954f7ca99", "time": 1.125, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.05833333333333334, -0.1], "bezier_left_value": [0, 0.5, 0], "bezier_right_time": [0.1, 0.05833333333333334, 0.1], "bezier_right_value": [0, -0.5, 0]}, {"channel": "rotation", "data_points": [{"x": -5, "y": -3, "z": 0}], "uuid": "8f224d62-c068-faac-670a-d0428fd1ef03", "time": 0.375, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.05833333333333334, -0.1], "bezier_left_value": [0, -0.5, 0], "bezier_right_time": [0.1, 0.05833333333333334, 0.1], "bezier_right_value": [0, 0.5, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": 0.25}], "uuid": "9428f9c0-19e0-9bf7-7da0-3a836a9458e0", "time": 0, "color": -1, "interpolation": "linear"}]}, "243936f8-b9c6-d179-6a92-d5c9a01b4340": {"name": "LeftHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": 0, "z": -7.5}], "uuid": "0c54933c-7752-8a67-fc44-36cd872d01d6", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.18333333333333335], "bezier_left_value": [-1, 0, -0.5], "bezier_right_time": [0.14166666666666666, 0.1, 0.18333333333333335], "bezier_right_value": [1, 0, 0.5]}, {"channel": "rotation", "data_points": [{"x": 3.75, "y": 0, "z": -2.5}], "uuid": "d8a1520a-715f-e511-d8d8-874f0d9c795b", "time": 0.375, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [2, 0, -2], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [-2, 0, 2]}, {"channel": "rotation", "data_points": [{"x": -12.5, "y": 0, "z": -1}], "uuid": "3e2e8d9a-e36d-ae79-ea39-f5ac969ee957", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.225, -0.1, -0.14166666666666666], "bezier_left_value": [0.75, 0, 1], "bezier_right_time": [0.225, 0.1, 0.14166666666666666], "bezier_right_value": [-0.75, 0, -1]}, {"channel": "rotation", "data_points": [{"x": 5, "y": 0, "z": -7.5}], "uuid": "24d52e34-4dfe-b450-216b-de8ff5d06006", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.18333333333333335], "bezier_left_value": [-1, 0, -0.5], "bezier_right_time": [0.14166666666666666, 0.1, 0.18333333333333335], "bezier_right_value": [1, 0, 0.5]}, {"channel": "position", "data_points": [{"x": 0, "y": -1.75, "z": -2}], "uuid": "83c72613-42e1-3265-5148-27833398a5c1", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.18333333333333335, -0.1687882213383311], "bezier_left_value": [0, 0, 0.09796279821614196], "bezier_right_time": [0.1, 0.18333333333333335, 0.1687882213383311], "bezier_right_value": [0, 0, -0.09796279821614196]}, {"channel": "position", "data_points": [{"x": 0, "y": 1.88, "z": -1}], "uuid": "c8731646-a7f7-395f-5d84-0da5dffa11ee", "time": 0.375, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, -1, -0.5], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 1, 0.5]}, {"channel": "position", "data_points": [{"x": 0, "y": 4, "z": "0"}], "uuid": "64f2bc4b-0609-8d8a-c394-a8d4b0e3d70a", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.18333333333333335, -0.13930755505047493], "bezier_left_value": [0, 0.25, 0], "bezier_right_time": [0.1, 0.18333333333333335, 0.13930755505047493], "bezier_right_value": [0, -0.25, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -1.75, "z": -2}], "uuid": "454266c1-5aa6-f3ae-9363-26c5b405a6b8", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.18333333333333335, -0.1687882213383311], "bezier_left_value": [0, 0, 0.09796279821614196], "bezier_right_time": [0.1, 0.18333333333333335, 0.1687882213383311], "bezier_right_value": [0, 0, -0.09796279821614196]}]}}}, {"uuid": "14124a22-2c0f-1490-b7f8-83aa5aee6d90", "name": "crouchwalkback", "loop": "loop", "override": false, "length": 1, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"d0f57d3a-5c99-127f-bb1e-dbd873765b47": {"name": "Noggle", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "05d13715-26ca-9b99-e0a6-618620beb724", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 20, "y": 0, "z": 0}], "uuid": "59fd1c0d-5667-5257-f6c7-643db033c245", "time": 0.2916666666666667, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "b5fc94f3-1eb3-c164-7bb4-732c266a8cd3", "time": 0.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": -20, "z": -5}], "uuid": "ffffdec7-a6a1-5cc5-e6be-9e3451c1f771", "time": 0, "color": -1, "interpolation": "linear"}]}, "a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 70, "y": "0", "z": "0"}], "uuid": "1571ee87-693a-cf27-1485-43821ff108f3", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [1.9616714915422926, 0, 0], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [-1.9616714915422926, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 82.5, "y": "0", "z": "0"}], "uuid": "580c7255-51b1-67d2-cac7-042c9d4503c9", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [1.9616714915422926, 0, 0], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [-1.9616714915422926, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 70, "y": "0", "z": "0"}], "uuid": "40ceee47-f29a-ca73-fa21-ca4f51cb1790", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [1.9616714915422926, 0, 0], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [-1.9616714915422926, 0, 0]}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-150", "y": -77.5, "z": -22.5}], "uuid": "32483edc-ae2a-7adb-1667-d32df1a7a308", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.14166666666666666, -0.1], "bezier_left_value": [3.75, -9, -7.25], "bezier_right_time": [0.1, 0.14166666666666666, 0.1], "bezier_right_value": [-3.75, 9, 7.25]}, {"channel": "rotation", "data_points": [{"x": "-135", "y": "-35", "z": "-25"}], "uuid": "f6929fa2-f5e5-61d5-dc74-806c9b7364f8", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.05833333333333334, -0.1], "bezier_left_value": [-17.75, -14, 8], "bezier_right_time": [0.1, 0.05833333333333334, 0.1], "bezier_right_value": [17.75, 14, -8]}, {"channel": "rotation", "data_points": [{"x": "-150", "y": -77.5, "z": -22.5}], "uuid": "538982df-c031-e1f0-d870-2d6aba11e063", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.14166666666666666, -0.1], "bezier_left_value": [3.75, -9, -7.25], "bezier_right_time": [0.1, 0.14166666666666666, 0.1], "bezier_right_value": [-3.75, 9, 7.25]}, {"channel": "rotation", "data_points": [{"x": "-105", "y": "-17.5", "z": "-40"}], "uuid": "0459a0af-30f8-11d1-2526-d668fa50363a", "time": 0.4583333333333333, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 9, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, -9, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 2, "z": "0"}], "uuid": "0d70d6bb-9d16-15fe-45ad-af6cebf56ccc", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.14166666666666666], "bezier_left_value": [0, 1, -0.5], "bezier_right_time": [0.1, 0.1, 0.14166666666666666], "bezier_right_value": [0, -1, 0.5]}, {"channel": "position", "data_points": [{"x": 0, "y": -6, "z": 4}], "uuid": "2b32fcd4-2b5f-da62-9ac3-96afaac4ece2", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, -0.5], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0.5]}, {"channel": "position", "data_points": [{"x": 0, "y": 2, "z": "0"}], "uuid": "2b4b7724-42ee-aa91-ca06-adcf0104640d", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.14166666666666666], "bezier_left_value": [0, 1, -0.5], "bezier_right_time": [0.1, 0.1, 0.14166666666666666], "bezier_right_value": [0, -1, 0.5]}, {"channel": "position", "data_points": [{"x": 0, "y": -4, "z": 3}], "uuid": "5370a0e1-9bf0-2b16-32f1-4d3c2dd13414", "time": 0.4583333333333333, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, -1.75, 1.25], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 1.75, -1.25]}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-150", "y": 77.5, "z": 22.5}], "uuid": "4336d9de-0287-1808-da46-38f3e5209b73", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.14166666666666666, -0.1], "bezier_left_value": [3.75, 9, 7.25], "bezier_right_time": [0.1, 0.14166666666666666, 0.1], "bezier_right_value": [-3.75, -9, -7.25]}, {"channel": "rotation", "data_points": [{"x": "-135", "y": 35, "z": 25}], "uuid": "fba0327f-5919-d614-802e-3692bbed8bef", "time": 0.3333333333333333, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.05833333333333334, -0.1], "bezier_left_value": [-17.75, 14, -8], "bezier_right_time": [0.1, 0.05833333333333334, 0.1], "bezier_right_value": [17.75, -14, 8]}, {"channel": "rotation", "data_points": [{"x": "-105", "y": 17.5, "z": 40}], "uuid": "aa2aafb5-7fe1-00b8-a068-98da7897f6e3", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, -9, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 9, 0]}, {"channel": "rotation", "data_points": [{"x": "-150", "y": 77.5, "z": 22.5}], "uuid": "8f954cda-1758-6f0a-d287-7dd9fa94792c", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.14166666666666666, -0.1], "bezier_left_value": [3.75, 9, 7.25], "bezier_right_time": [0.1, 0.14166666666666666, 0.1], "bezier_right_value": [-3.75, -9, -7.25]}, {"channel": "position", "data_points": [{"x": 0, "y": 2, "z": "0"}], "uuid": "d63bba83-92cd-3baa-a404-5011bb49ccd8", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.14166666666666666], "bezier_left_value": [0, 1, -0.5], "bezier_right_time": [0.1, 0.1, 0.14166666666666666], "bezier_right_value": [0, -1, 0.5]}, {"channel": "position", "data_points": [{"x": 0, "y": -6, "z": 4}], "uuid": "af936920-489f-3b51-f189-3a4c2b055691", "time": 0.3333333333333333, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, -0.5], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0.5]}, {"channel": "position", "data_points": [{"x": 0, "y": -4, "z": 3}], "uuid": "ce2e8c48-ff96-f8e3-3578-e4f7e49de570", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, -1.75, 1.25], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 1.75, -1.25]}, {"channel": "position", "data_points": [{"x": 0, "y": 2, "z": "0"}], "uuid": "3e65b750-2a3f-b96d-8f41-21508488f49c", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.14166666666666666], "bezier_left_value": [0, 1, -0.5], "bezier_right_time": [0.1, 0.1, 0.14166666666666666], "bezier_right_value": [0, -1, 0.5]}]}, "069d4014-e378-cde6-ed3f-3b8fc92c9750": {"name": "MidSection", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -22.5, "y": "0", "z": "0"}], "uuid": "2e1fd509-1b58-ff90-a26f-f075de9dab5a", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [1.9616714915422926, 0, 0], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [-1.9616714915422926, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -35, "y": "0", "z": "0"}], "uuid": "dae1fa94-c35c-cb91-f82c-76fd14006c00", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.13359096829905984, -0.1, -0.1], "bezier_left_value": [-2.782966606896872, 0, 0], "bezier_right_time": [0.13359096829905984, 0.1, 0.1], "bezier_right_value": [2.782966606896872, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -22.5, "y": "0", "z": "0"}], "uuid": "4a81ece2-cb22-3600-fc8f-f770806ebe56", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [1.9616714915422926, 0, 0], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [-1.9616714915422926, 0, 0]}]}, "*************-d6e3-2315-015aa3b6fb4d": {"name": "FrontScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -17.5, "y": "0", "z": "0"}], "uuid": "9518cbf9-cc60-5986-fb6f-2f2df0a7a79d", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": 0.6, "z": "1"}], "uuid": "0a315223-592d-a732-3b0c-73a9dece05bf", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "07f1c422-136b-88b2-3301-0d1a14dd6560": {"name": "LeftScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -7.5, "y": 0, "z": 0}], "uuid": "f5a3d7b0-1980-b45d-e35d-b0df62efd609", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": 0.8, "z": "1"}], "uuid": "6922f8a2-6e20-afc1-50de-497d4dbb4e84", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "fd2c92eb-7659-a0b9-2254-118d0dad40f5": {"name": "RightScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -7.5, "y": "0", "z": "0"}], "uuid": "5142bfb8-2774-526d-34fe-bc4e684a8d54", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": 0.8, "z": "1"}], "uuid": "f7d3bbc7-d62a-f139-5d7f-567bb5e2bbcd", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "ce3e2383-010d-03b5-2c18-1f82ea6055d8": {"name": "RightHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -12.5, "y": 0, "z": 1}], "uuid": "6f965304-33aa-1a16-6f3e-b0ff03973190", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.15075000000000002, -0.067, -0.09491666666666666], "bezier_left_value": [-0.75, 0, 1], "bezier_right_time": [0.15075000000000002, 0.067, 0.09491666666666666], "bezier_right_value": [0.75, 0, -1]}, {"channel": "rotation", "data_points": [{"x": 5, "y": 0, "z": 7.5}], "uuid": "91ad3c2b-f879-72b7-cdfa-e4989046172e", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.09491666666666666, -0.067, -0.12283333333333335], "bezier_left_value": [1, 0, -0.5], "bezier_right_time": [0.09491666666666666, 0.067, 0.12283333333333335], "bezier_right_value": [-1, 0, 0.5]}, {"channel": "rotation", "data_points": [{"x": 3.75, "y": 0, "z": 2.5}], "uuid": "ac3ae184-9b65-86c0-0546-0cf79d294edd", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.067, -0.067, -0.067], "bezier_left_value": [-2, 0, -2], "bezier_right_time": [0.067, 0.067, 0.067], "bezier_right_value": [2, 0, 2]}, {"channel": "rotation", "data_points": [{"x": -12.5, "y": 0, "z": 1}], "uuid": "694f3e89-804e-c1f1-538b-90e737b8dc98", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.15075000000000002, -0.067, -0.09491666666666666], "bezier_left_value": [-0.75, 0, 1], "bezier_right_time": [0.15075000000000002, 0.067, 0.09491666666666666], "bezier_right_value": [0.75, 0, -1]}, {"channel": "position", "data_points": [{"x": 0, "y": 4, "z": "0"}], "uuid": "b1e036ac-1a52-b4c2-88f9-2188cdfcc629", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.067, -0.12283333333333335, -0.09333606188381821], "bezier_left_value": [0, -0.25, 0], "bezier_right_time": [0.067, 0.12283333333333335, 0.09333606188381821], "bezier_right_value": [0, 0.25, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -1.75, "z": -2}], "uuid": "8209e29d-**************-85cca458a140", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.067, -0.12283333333333335, -0.11308810829668184], "bezier_left_value": [0, 0, -0.09796279821614196], "bezier_right_time": [0.067, 0.12283333333333335, 0.11308810829668184], "bezier_right_value": [0, 0, 0.09796279821614196]}, {"channel": "position", "data_points": [{"x": 0, "y": 1.88, "z": -1}], "uuid": "7dd24f2e-ec2f-08d3-c18d-7d7f4fee0e0a", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.067, -0.067, -0.067], "bezier_left_value": [0, 1, 0.5], "bezier_right_time": [0.067, 0.067, 0.067], "bezier_right_value": [0, -1, -0.5]}, {"channel": "position", "data_points": [{"x": 0, "y": 4, "z": "0"}], "uuid": "c6e5cc13-ded2-e43b-5963-4269b0c374fb", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.067, -0.12283333333333335, -0.09333606188381821], "bezier_left_value": [0, -0.25, 0], "bezier_right_time": [0.067, 0.12283333333333335, 0.09333606188381821], "bezier_right_value": [0, 0.25, 0]}]}, "fd4c51f4-3e9f-0590-04fe-6d3a2c8a4ee8": {"name": "Neck", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-7.5", "y": "0", "z": "0"}], "uuid": "359258f4-1a32-096f-4e46-265aba9a063a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "f5f9f8b8-013f-7f03-526e-d3ead87f6705", "time": 0, "color": -1, "interpolation": "linear"}]}, "9d7c0b25-cc44-6a43-9602-09df292fbfff": {"name": "FrontVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -30, "y": "0", "z": "0"}], "uuid": "2600a4e0-3508-5bbf-1832-2cb70d536417", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -40, "y": "0", "z": "0"}], "uuid": "05dabaed-4169-0f64-676b-5305195527ba", "time": 0.20833333333333334, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -30, "y": "0", "z": "0"}], "uuid": "8e338251-5959-c1c6-dcc9-02d206f54b0b", "time": 1, "color": -1, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "0fc17ddc-276d-e5e4-d04b-d65ffeb6f01b", "time": 0.125, "color": -1, "uniform": true, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.1, "z": 1}], "uuid": "00820df9-42fe-d50a-df4b-1f1d3df0de11", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "catmullrom"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "1059a9a8-e897-2e6e-d501-f0667a26fb40", "time": 0.6666666666666666, "color": -1, "uniform": true, "interpolation": "catmullrom"}]}, "f052b1a8-653c-610b-fba5-c6173a05c90b": {"name": "RightVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-22.5", "y": -5, "z": -10}], "uuid": "c8daac7f-25ee-c73b-1bde-89e9762db03b", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -32.5, "y": "0", "z": "0"}], "uuid": "28976d5e-5ca8-6477-640e-b6fd72eba8a3", "time": 0.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-22.5", "y": -5, "z": -10}], "uuid": "4a005a51-957c-3718-b786-06deb2cfc418", "time": 1, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -30, "y": 0, "z": 0}], "uuid": "967268a3-4d4b-ec23-88f1-be1c6c017b6f", "time": 0.2916666666666667, "color": -1, "interpolation": "catmullrom"}]}, "9cb55e3c-c096-2ba6-c5bc-b3a31300ae63": {"name": "LeftVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-22.5", "y": "5", "z": "10"}], "uuid": "f6cc5d64-e06c-7649-8488-cf3e443a7a02", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -30, "y": "0", "z": "0"}], "uuid": "a122164a-a794-a7e2-b6dc-e430fecce9b1", "time": 0.20833333333333334, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -22.5, "y": "0", "z": "0"}], "uuid": "834f7ca1-2aff-b5d9-94d7-3fc81f777e91", "time": 0.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-22.5", "y": "5", "z": "10"}], "uuid": "bdd0e857-8427-437a-d932-4e6e85114a43", "time": 1, "color": -1, "interpolation": "catmullrom"}]}, "becce561-f622-7592-514f-5aede933e7be": {"name": "Ring", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": "3.5", "z": "0"}], "uuid": "6e1fed7e-8e29-35fb-16da-23d3120b3a15", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.14166666666666666], "bezier_left_value": [0, 0, -0.5], "bezier_right_time": [0.1, 0.1, 0.14166666666666666], "bezier_right_value": [0, 0, 0.5]}, {"channel": "position", "data_points": [{"x": 0, "y": "4.5", "z": 2}], "uuid": "a9c3b045-**************-84653f558921", "time": 0.2916666666666667, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.11427129522041089, -0.1], "bezier_left_value": [0, 0.16869778740686764, -0.508955223880597], "bezier_right_time": [0.1, 0.11427129522041089, 0.1], "bezier_right_value": [0, -0.16869778740686764, 0.508955223880597]}, {"channel": "position", "data_points": [{"x": 0, "y": "3.5", "z": 2.5}], "uuid": "6abcb0db-5e46-52b5-a6f9-98dfb73b03e3", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "3.5", "z": "0"}], "uuid": "cac96b2f-4874-b376-e59b-280d1fcb6cdc", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.14166666666666666], "bezier_left_value": [0, 0, -0.5], "bezier_right_time": [0.1, 0.1, 0.14166666666666666], "bezier_right_value": [0, 0, 0.5]}]}, "a247b8fc-3e11-172d-2179-4eebf17e6fe8": {"name": "Shoulders", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 12.5, "y": "0", "z": "0"}], "uuid": "cc7ca08a-590d-b4b9-657e-5268183eac9b", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.30833333333333335, -0.26666666666666666], "bezier_left_value": [1.9616714915422926, 0, 0], "bezier_right_time": [0.14166666666666666, 0.30833333333333335, 0.26666666666666666], "bezier_right_value": [-1.9616714915422926, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "65aa454f-31a0-f1d6-9be4-6a8951e9b485", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.1, -0.1], "bezier_left_value": [1.9616714915422926, 0, 0], "bezier_right_time": [0.14166666666666666, 0.1, 0.1], "bezier_right_value": [-1.9616714915422926, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 12.5, "y": "0", "z": "0"}], "uuid": "40418b4e-7a99-e1b7-c6de-f71f50601476", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.30833333333333335, -0.26666666666666666], "bezier_left_value": [1.9616714915422926, 0, 0], "bezier_right_time": [0.14166666666666666, 0.30833333333333335, 0.26666666666666666], "bezier_right_value": [-1.9616714915422926, 0, 0]}]}, "e5e69819-52db-c577-1116-b04b331d7732": {"name": "LeftEye", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": "0.1", "z": "0"}], "uuid": "13d78a8d-7c16-0e47-b340-b08481f91315", "time": 0, "color": -1, "interpolation": "linear"}]}, "36145320-9f7b-a7be-1d1c-06f5ef5a57f7": {"name": "RightEye", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": "0.1", "z": "0"}], "uuid": "0d782085-c184-0c6f-cd48-89cd5d7711f3", "time": 0, "color": -1, "interpolation": "linear"}]}, "a8267e42-e1ec-7aa4-67ce-f3079d498125": {"name": "BackScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -7.5, "y": "0", "z": "0"}], "uuid": "34d62fa7-91e8-9720-036e-93e9b84095bb", "time": 0.20833333333333334, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -2.5, "y": "0", "z": "0"}], "uuid": "b98823ee-b442-a38b-2599-028abbd8fb50", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -7.5, "y": "0", "z": "0"}], "uuid": "b240e51d-b0d4-9173-2230-4b1859cc4238", "time": 0.4583333333333333, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": 0.25}], "uuid": "5d6f14aa-1ad3-c6bb-ab92-34ad86b57ffd", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}]}, "243936f8-b9c6-d179-6a92-d5c9a01b4340": {"name": "LeftHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -12.5, "y": 0, "z": -1}], "uuid": "fef4168a-2fe6-9ef3-069b-79e2b740fed9", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.15075000000000002, -0.067, -0.09491666666666666], "bezier_left_value": [-0.75, 0, -1], "bezier_right_time": [0.15075000000000002, 0.067, 0.09491666666666666], "bezier_right_value": [0.75, 0, 1]}, {"channel": "rotation", "data_points": [{"x": 3.75, "y": 0, "z": -2.5}], "uuid": "3738732a-6e0d-52e2-680b-564e855dae62", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.067, -0.067, -0.067], "bezier_left_value": [-2, 0, 2], "bezier_right_time": [0.067, 0.067, 0.067], "bezier_right_value": [2, 0, -2]}, {"channel": "rotation", "data_points": [{"x": 5, "y": 0, "z": -7.5}], "uuid": "e27600ab-4fcd-6eaa-0a31-4caa9c96b334", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.09491666666666666, -0.067, -0.12283333333333335], "bezier_left_value": [1, 0, 0.5], "bezier_right_time": [0.09491666666666666, 0.067, 0.12283333333333335], "bezier_right_value": [-1, 0, -0.5]}, {"channel": "rotation", "data_points": [{"x": -12.5, "y": 0, "z": -1}], "uuid": "c6d6abfa-6dc0-fbf8-44e6-be4618934c69", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.15075000000000002, -0.067, -0.09491666666666666], "bezier_left_value": [-0.75, 0, -1], "bezier_right_time": [0.15075000000000002, 0.067, 0.09491666666666666], "bezier_right_value": [0.75, 0, 1]}, {"channel": "position", "data_points": [{"x": 0, "y": 4, "z": "0"}], "uuid": "8693306d-4249-571b-cfe6-85a80d8a7463", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.067, -0.12283333333333335, -0.09333606188381821], "bezier_left_value": [0, -0.25, 0], "bezier_right_time": [0.067, 0.12283333333333335, 0.09333606188381821], "bezier_right_value": [0, 0.25, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 1.88, "z": -1}], "uuid": "5b508ce2-3008-a262-6a00-ce3f17d9dfdd", "time": 0.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.067, -0.067, -0.067], "bezier_left_value": [0, 1, 0.5], "bezier_right_time": [0.067, 0.067, 0.067], "bezier_right_value": [0, -1, -0.5]}, {"channel": "position", "data_points": [{"x": 0, "y": -1.75, "z": -2}], "uuid": "99b64f48-a815-278b-441d-9603d4c5b84f", "time": 0.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.067, -0.12283333333333335, -0.11308810829668184], "bezier_left_value": [0, 0, -0.09796279821614196], "bezier_right_time": [0.067, 0.12283333333333335, 0.11308810829668184], "bezier_right_value": [0, 0, 0.09796279821614196]}, {"channel": "position", "data_points": [{"x": 0, "y": 4, "z": "0"}], "uuid": "475c1077-65f6-1503-9d25-ebcbf8968060", "time": 1, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.067, -0.12283333333333335, -0.09333606188381821], "bezier_left_value": [0, -0.25, 0], "bezier_right_time": [0.067, 0.12283333333333335, 0.09333606188381821], "bezier_right_value": [0, 0.25, 0]}]}}}, {"uuid": "ea81e1f0-50c9-e788-0081-1bbb262d472a", "name": "fly", "loop": "loop", "override": false, "length": 5, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"becce561-f622-7592-514f-5aede933e7be": {"name": "Ring", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": -2, "z": "0"}], "uuid": "*************-852a-aa2f-8a370c738c24", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.166, -0.49599999999999983, -0.166], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.166, 0.49599999999999983, 0.166], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": "0"}], "uuid": "843644bf-f8d3-ed60-5aff-93c2f3800dba", "time": 5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.166, -0.49599999999999983, -0.166], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.166, 0.49599999999999983, 0.166], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 0, "z": "0"}], "uuid": "ae3da984-bc5e-ad2f-5028-e6b5296692d5", "time": 2.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.166, -0.49599999999999983, -0.166], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.166, 0.49599999999999983, 0.166], "bezier_right_value": [0, 0, 0]}]}, "fd4c51f4-3e9f-0590-04fe-6d3a2c8a4ee8": {"name": "Neck", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 15, "y": "0", "z": "0"}], "uuid": "e1b25621-c9e1-04ac-89bb-bf3627071b91", "time": 0, "color": -1, "interpolation": "linear"}]}, "0ce83a3b-f3e0-b7d1-64e1-8f5fd4c9f064": {"name": "NogginBits", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -10, "y": "0", "z": "0"}], "uuid": "522e00cd-6f8b-249b-07e0-39b02dd335ce", "time": 0, "color": -1, "interpolation": "catmullrom"}]}, "c419ee69-4273-618b-53a8-7c054f6c99a2": {"name": "TheVeil", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "164b9123-db7d-3a03-eb53-427728689f5b", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "750bcd46-e084-3891-39bc-aaff68f82907", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}, "a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 10, "y": 0, "z": "2.5"}], "uuid": "1d3ede9e-2bb5-6983-8807-118d761e4ed3", "time": 0, "color": -1, "interpolation": "catmullrom"}]}, "a247b8fc-3e11-172d-2179-4eebf17e6fe8": {"name": "Shoulders", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "e05fd646-2ca6-9ddb-57a4-839a98e76441", "time": 0, "color": -1, "interpolation": "linear"}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-25", "y": "15", "z": "-17.5"}], "uuid": "28f5e8ad-7c73-b32c-870b-f35ee3017b91", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -20, "y": 15, "z": "-15"}], "uuid": "d04fb14a-c700-25a9-2fd6-832235c35ec5", "time": 3, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-25", "y": "15", "z": "-17.5"}], "uuid": "63c96651-0b83-c68f-42cf-5ea78084b61b", "time": 5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -1, "y": -1, "z": 1}], "uuid": "833cf940-6e4a-d92b-4b94-12b44fce27d7", "time": 0, "color": -1, "interpolation": "linear"}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-25", "y": -15, "z": 17.5}], "uuid": "4e243f3f-8c24-9460-1726-63ce7692a1eb", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -20, "y": -15, "z": 15}], "uuid": "0dcae512-9caf-92a2-4c39-41c11582ba09", "time": 2, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-25", "y": -15, "z": 17.5}], "uuid": "f49dfe42-c94d-7b6b-5e0b-29768e9a7519", "time": 5, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 1, "y": -1, "z": 1}], "uuid": "00399cb0-2454-98e1-d8b9-39fd960ef6ee", "time": 0, "color": -1, "interpolation": "linear"}]}, "069d4014-e378-cde6-ed3f-3b8fc92c9750": {"name": "MidSection", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-15", "y": 0, "z": "-2.5"}], "uuid": "37c293a5-2bad-2d36-5e5b-bd21f1ad7646", "time": 0, "color": -1, "interpolation": "linear"}]}, "39b607ae-53e6-6156-41f8-11a554e1e77f": {"name": "<PERSON><PERSON><PERSON>", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "d7d9bdf1-9217-843f-6180-450e8fd1e637", "time": 0, "color": -1, "interpolation": "linear"}]}, "a8267e42-e1ec-7aa4-67ce-f3079d498125": {"name": "BackScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 10, "y": "0", "z": "0"}], "uuid": "347e6be6-8f75-271b-52b5-ef892c97decf", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": 1.5000000000000004}], "uuid": "ec0b741c-cc34-efb0-5f8c-b2a9a4b8b805", "time": 0, "color": -1, "uniform": false, "interpolation": "linear"}]}, "07f1c422-136b-88b2-3301-0d1a14dd6560": {"name": "LeftScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -5}], "uuid": "adb2e86d-b5d1-37c0-210d-f5138702f14e", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "e89d2b70-6b71-98ac-b53e-e0d1206d3c74", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}, "fd2c92eb-7659-a0b9-2254-118d0dad40f5": {"name": "RightScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": 5}], "uuid": "d4e52ebc-1d7e-29a5-67fb-f91d4cf1da57", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "93d6b8e5-79d2-1651-516f-017a002c7bf8", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}, "0c083c53-995e-71ab-f3e1-dcb04bd31c4a": {"name": "Legs", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 1, "z": "0"}], "uuid": "5daa1d81-e3d8-77d0-1234-1cc3514c6464", "time": 0, "color": -1, "interpolation": "linear"}]}, "243936f8-b9c6-d179-6a92-d5c9a01b4340": {"name": "LeftHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "7.5", "y": "15", "z": "7.5"}], "uuid": "aba8f323-3993-fecc-a54f-353972014fb0", "time": 0, "color": -1, "interpolation": "linear"}]}, "ce3e2383-010d-03b5-2c18-1f82ea6055d8": {"name": "RightHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-5", "y": "-15", "z": "-7.5"}], "uuid": "9e9534ae-be96-8476-60d9-bd037666152b", "time": 0, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "834cbb4b-3d88-2a9e-065e-d450a1dbd64c", "name": "sprint", "loop": "loop", "override": false, "length": 1.5, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"d0f57d3a-5c99-127f-bb1e-dbd873765b47": {"name": "Noggle", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "f253ea7a-67c4-1e39-298e-a6bc787b8152", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": "0"}], "uuid": "72eacb50-598b-ebcb-d5ec-6369f773460d", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.18333333333333335, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.18333333333333335, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2.5, "z": "0"}], "uuid": "090cf75c-0734-c95b-89d7-94801a969ec0", "time": 0.8333333333333334, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.19180534781950068, -0.1], "bezier_left_value": [0, 0.06787330316742081, 0], "bezier_right_time": [0.1, 0.19180534781950068, 0.1], "bezier_right_value": [0, -0.06787330316742081, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": "0"}], "uuid": "dd7677a2-19df-a50b-9287-ae575982b9d2", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.18333333333333335, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.18333333333333335, 0.1], "bezier_right_value": [0, 0, 0]}]}, "9d7c0b25-cc44-6a43-9602-09df292fbfff": {"name": "FrontVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "76d07198-0c73-ccdf-1456-e91f507f10f6", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1416666666666667, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1416666666666667, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 3, "y": "0", "z": "0"}], "uuid": "465bfdef-4884-814f-947d-7179c29eebd8", "time": 0.375, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1416666666666667, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1416666666666667, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 4, "y": "0", "z": "0"}], "uuid": "3b37c769-f298-f5de-44cb-a217bae889fa", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1416666666666667, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1416666666666667, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 3, "y": "0", "z": "0"}], "uuid": "b6992c0c-5169-bbbb-f858-969b5d4c6e0e", "time": 1.125, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1416666666666667, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1416666666666667, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "c39cfac7-0425-9d4c-834e-7ac6e1befea1", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1416666666666667, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1416666666666667, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "f052b1a8-653c-610b-fba5-c6173a05c90b": {"name": "RightVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "8", "y": -1, "z": "15"}], "uuid": "a767b7f9-632c-93ed-c013-2082fd960618", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 5.5, "y": -1, "z": "17.5"}], "uuid": "00057372-d549-1a8d-6818-42945bcec4d8", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "8", "y": -1, "z": "15"}], "uuid": "40d59b77-26c0-3d0e-7a8f-1e51e65d128c", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "9cb55e3c-c096-2ba6-c5bc-b3a31300ae63": {"name": "LeftVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 0.5, "y": 1, "z": -5}], "uuid": "a02605b0-dce0-d75f-8434-d886e2344645", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 0.5, "y": 1, "z": 0}], "uuid": "ef1ad07e-40d0-cba2-9bc6-a0de7a691bf7", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 0.5, "y": 1, "z": 0}], "uuid": "514971e6-6bc3-72cb-ef0d-d2051450bcf8", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "17.5", "y": "-10", "z": "-3"}], "uuid": "0b743120-eb01-2084-4b50-5c4df35d9650", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.26666666666666666, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.26666666666666666, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 16.5, "y": -10, "z": -3}], "uuid": "66bc8a3e-15b1-c0ee-e1ce-c237ea036c7e", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "17.5", "y": "-10", "z": "-3"}], "uuid": "f1cff347-9754-b20c-014d-a12acfc813e7", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-65", "y": "25", "z": "-40"}], "uuid": "994b7864-dfa2-89fa-5a0f-1505a03b42e4", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-65", "y": "27.5", "z": "-32.5"}], "uuid": "c07222fc-9f31-3e5a-9e09-296e64319cb0", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-65", "y": "25", "z": "-40"}], "uuid": "5490a05f-459c-e049-d8f3-f9574513dcb8", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.18333333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.18333333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": -1, "y": -2, "z": -1}], "uuid": "ab59c648-6e16-ab28-9ca0-ae6f03e54a7d", "time": 0, "color": -1, "interpolation": "linear"}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-90", "y": 5, "z": 47.5}], "uuid": "24290081-2f12-2052-12d3-8b39d482dab8", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.14166666666666666, -0.1], "bezier_left_value": [0, -2, 0], "bezier_right_time": [0.14166666666666666, 0.14166666666666666, 0.1], "bezier_right_value": [0, 2, 0]}, {"channel": "rotation", "data_points": [{"x": "-100", "y": 5, "z": 47.5}], "uuid": "46b2d5f9-c2af-73b6-e98f-3903df62cd97", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.14166666666666666, -0.1], "bezier_left_value": [0, 2, 0], "bezier_right_time": [0.14166666666666666, 0.14166666666666666, 0.1], "bezier_right_value": [0, -2, 0]}, {"channel": "rotation", "data_points": [{"x": "-90", "y": 5, "z": 47.5}], "uuid": "b5a7df8b-35de-d290-2029-d3b8055d9cbd", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.14166666666666666, -0.1], "bezier_left_value": [0, -2, 0], "bezier_right_time": [0.14166666666666666, 0.14166666666666666, 0.1], "bezier_right_value": [0, 2, 0]}, {"channel": "rotation", "data_points": [{"x": -95, "y": "10", "z": 47.5}], "uuid": "7cbf16da-cbb2-d845-8b19-6df318d6dfa9", "time": 0.375, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.14166666666666666, -0.1], "bezier_left_value": [2, 0, 0], "bezier_right_time": [0.14166666666666666, 0.14166666666666666, 0.1], "bezier_right_value": [-2, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-95", "y": "0", "z": 47.5}], "uuid": "bd7e79e3-e4f1-0095-30fa-3a61d96fb445", "time": 1.125, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.14166666666666666, -0.1], "bezier_left_value": [-2, 0, 0], "bezier_right_time": [0.14166666666666666, 0.14166666666666666, 0.1], "bezier_right_value": [2, 0, 0]}, {"channel": "position", "data_points": [{"x": 1, "y": "0", "z": -2}], "uuid": "a3fa1e9e-6a4a-abb8-7c40-3fac78c89f34", "time": 0, "color": -1, "interpolation": "linear"}]}, "069d4014-e378-cde6-ed3f-3b8fc92c9750": {"name": "MidSection", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 9.981069393700182, "y": 0.43523000247023447, "z": -4.9810693937001815}], "uuid": "82386bdc-00d7-0541-b3bc-4b1c59091bcd", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": 0.5, "z": "0"}], "uuid": "cec5be9d-871a-27ff-3a25-daf2c4b71002", "time": 0, "color": -1, "interpolation": "linear"}]}, "39b607ae-53e6-6156-41f8-11a554e1e77f": {"name": "<PERSON><PERSON><PERSON>", "type": "bone", "keyframes": [{"channel": "scale", "data_points": [{"x": 1, "y": "1.02", "z": 1}], "uuid": "77e25fe6-22ef-a78f-3774-411801e02c60", "time": 1.5, "color": -1, "uniform": false, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.14166666666666666, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.14166666666666666, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": 1, "y": 1, "z": 1}], "uuid": "7101461c-2e48-80d8-9082-edfa6c805b4c", "time": 1, "color": -1, "uniform": true, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.14166666666666666, -0.14166666666666666, -0.14166666666666666], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.14166666666666666, 0.14166666666666666, 0.14166666666666666], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": 1, "y": "1.02", "z": 1}], "uuid": "4e44cd50-**************-db8e5fb91893", "time": 0, "color": -1, "uniform": false, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.14166666666666666, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.14166666666666666, 0.1], "bezier_right_value": [0, 0, 0]}]}, "*************-d6e3-2315-015aa3b6fb4d": {"name": "FrontScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "84d28c71-cb6a-9268-6b90-d042a9b66ded", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -4, "y": "0", "z": "0"}], "uuid": "08147c7b-a4fe-29b6-7b6b-0db462e01857", "time": 0.375, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "d6ed90ff-9f3e-e17f-00c3-8fba8b087ea4", "time": 0.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "936151ee-db35-a39f-924f-1c5464fb8106", "time": 1.5, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -4, "y": "0", "z": "0"}], "uuid": "fd1a89d5-e52d-de2c-c6ad-ebe09325edc4", "time": 1.125, "color": -1, "interpolation": "catmullrom"}]}, "a8267e42-e1ec-7aa4-67ce-f3079d498125": {"name": "BackScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "5", "y": "0", "z": "0"}], "uuid": "c1d4b0c1-73a8-f3cb-21f9-1f43e89d09c2", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "6", "y": "0", "z": "0"}], "uuid": "2f4625ed-2516-0dfe-e05b-595c8fb9edab", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "5", "y": "0", "z": "0"}], "uuid": "7b581564-4907-9c1e-be14-702398d17e72", "time": 0.75, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "5", "y": "0", "z": "0"}], "uuid": "b8af76fd-1301-dbfd-ba39-92b0082d0f9a", "time": 1.5, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "6", "y": "0", "z": "0"}], "uuid": "d9eafff4-e6ca-741e-a2ff-783ebcb68e42", "time": 1.125, "color": -1, "interpolation": "linear"}]}, "07f1c422-136b-88b2-3301-0d1a14dd6560": {"name": "LeftScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7fbb17a4-65fb-90be-ebf3-ef1e1f3c343e", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 1, "y": 0, "z": 0}], "uuid": "20804271-ed54-3055-9398-9083853bfafc", "time": 0.375, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "2ceac6dc-8318-75ac-29c7-d063401f0650", "time": 0.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 1, "y": 0, "z": 0}], "uuid": "e4d8d0e5-7a0a-a731-df08-0441008d19b2", "time": 1.125, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "bd68e29d-afc7-86a8-38c5-6bcebfc94d47", "time": 1.5, "color": -1, "interpolation": "catmullrom"}]}, "fd2c92eb-7659-a0b9-2254-118d0dad40f5": {"name": "RightScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "b601e3a8-26d3-93d7-5124-68b15c609ac3", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 1, "y": 0, "z": 0}], "uuid": "a9fb73b3-49b7-f448-b36b-0355b2616c71", "time": 0.375, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "34a12ee7-6694-018f-c453-dbe4e7a0719e", "time": 0.75, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": 1, "y": 0, "z": 0}], "uuid": "e401b528-67ca-ccbf-09e9-0334917fbaaa", "time": 1.125, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "96d1e320-941b-a309-582b-e92191e0c59d", "time": 1.5, "color": -1, "interpolation": "catmullrom"}]}, "243936f8-b9c6-d179-6a92-d5c9a01b4340": {"name": "LeftHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 10, "y": "0", "z": "0"}], "uuid": "d33e4e60-fc5e-e9c9-0e26-7a0d8ecf60fb", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.26666666666666666, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.26666666666666666, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 2.5, "y": 0, "z": 0}], "uuid": "fb8d002a-576d-8ba9-e0b9-e362b472cf48", "time": 0.8333333333333334, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.26666666666666666, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.26666666666666666, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 10, "y": "0", "z": "0"}], "uuid": "6f7b09f9-1e95-cbfb-a79f-2425fd4257ae", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.26666666666666666, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.26666666666666666, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "ce3e2383-010d-03b5-2c18-1f82ea6055d8": {"name": "RightHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 1, "y": "0", "z": "0"}], "uuid": "6c2f0fbe-f21d-105c-d6ba-8542fee7b469", "time": 0.6666666666666666, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [-0.5, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0.5, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "1133a7a5-5854-eb12-33c1-045212599e08", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0.5, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [-0.5, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "cd8e0deb-9cb5-faff-4744-ad6c313ae9bd", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0.5, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [-0.5, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 2.5, "y": 0, "z": 0}], "uuid": "9f8f3fb5-9192-afa8-aa80-f053ceaf3d4a", "time": 1.125, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.17602616592706527, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.17602616592706527, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}}}, {"uuid": "136af3c9-f6c4-bf03-3619-06205859fa00", "name": "water", "loop": "loop", "override": false, "length": 3, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"a55b555a-6578-72c7-1034-18bf620ffd9a": {"name": "Chest", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 2.5, "y": "0", "z": "0"}], "uuid": "1998a0f2-0a71-2650-eb02-c42a59a47715", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.5166666666666667, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.5166666666666667, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-2.5", "y": "0", "z": "0"}], "uuid": "a052737f-4d40-ee25-01ac-************", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.5166666666666667, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.5166666666666667, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-2.5", "y": "0", "z": "0"}], "uuid": "5cc5469d-318b-324a-4109-1d6bb6edbd67", "time": 3, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.30833333333333335, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.30833333333333335, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": 12.5, "z": -20}], "uuid": "3a78036f-d523-2dbd-aeec-2b93da83db53", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.475, -0.1, -0.35], "bezier_left_value": [-5, 0, 0], "bezier_right_time": [0.475, 0.1, 0.35], "bezier_right_value": [5, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 12.5, "z": -16}], "uuid": "36846825-8aec-ea9d-0d34-dd053963f086", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.475, -0.1, -0.35], "bezier_left_value": [5, 0, 0], "bezier_right_time": [0.475, 0.1, 0.35], "bezier_right_value": [-5, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -5, "y": 12.5, "z": -20}], "uuid": "55c82b49-826a-3521-60a5-60c70b22e3c7", "time": 3, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.475, -0.1, -0.35], "bezier_left_value": [-5, 0, 0], "bezier_right_time": [0.475, 0.1, 0.35], "bezier_right_value": [5, 0, 0]}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": -12.5, "z": 20}], "uuid": "1e781aff-916c-89dd-b36a-957be9cf1a28", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.475, -0.1, -0.1], "bezier_left_value": [5, 0, 0], "bezier_right_time": [0.475, 0.1, 0.1], "bezier_right_value": [-5, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -5, "y": -12.5, "z": 20}], "uuid": "beeab3e5-d051-f517-c259-6d26a7ed4dda", "time": 3, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.475, -0.1, -0.1], "bezier_left_value": [-5, 0, 0], "bezier_right_time": [0.475, 0.1, 0.1], "bezier_right_value": [5, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -5, "y": -12.5, "z": 20}], "uuid": "f162a100-7e15-1969-cc60-de9514352e03", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.475, -0.1, -0.1], "bezier_left_value": [-5, 0, 0], "bezier_right_time": [0.475, 0.1, 0.1], "bezier_right_value": [5, 0, 0]}]}, "39b607ae-53e6-6156-41f8-11a554e1e77f": {"name": "<PERSON><PERSON><PERSON>", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "3a17528f-3da5-655f-8669-a411e9badc9c", "time": 0, "color": -1, "interpolation": "linear"}]}, "*************-d6e3-2315-015aa3b6fb4d": {"name": "FrontScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "58487438-3c19-f284-dc2f-c262d70817ad", "time": 0, "color": -1, "interpolation": "linear"}]}, "a8267e42-e1ec-7aa4-67ce-f3079d498125": {"name": "BackScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "3f64174e-bd3e-b6d1-aa09-ea88d7353d63", "time": 0, "color": -1, "interpolation": "linear"}]}, "07f1c422-136b-88b2-3301-0d1a14dd6560": {"name": "LeftScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -7.5}], "uuid": "fb9bd6df-795f-0cf9-9274-9371f8d5c6c1", "time": 0, "color": -1, "interpolation": "linear"}]}, "fd2c92eb-7659-a0b9-2254-118d0dad40f5": {"name": "RightScrubs", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 7.5}], "uuid": "220632fe-2add-da68-94cd-102e6cbe4a9a", "time": 0, "color": -1, "interpolation": "linear"}]}, "243936f8-b9c6-d179-6a92-d5c9a01b4340": {"name": "LeftHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "8df00ff7-b8aa-21c5-6021-87c22af1b75b", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.13333333333333333, -0.05, -0.05], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.13333333333333333, 0.05, 0.05], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -2.5, "y": 0, "z": 0}], "uuid": "baaec095-8486-3e43-7cce-3d798c1c652d", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.13333333333333333, -0.05, -0.05], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.13333333333333333, 0.05, 0.05], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "6b490fad-28fd-2513-87ab-dfdde7d37b69", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.13333333333333333, -0.05, -0.05], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.13333333333333333, 0.05, 0.05], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -2.5, "y": 0, "z": 0}], "uuid": "fd3a8564-cfd8-99e2-2379-cb2ae53d7f6b", "time": 2.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.13333333333333333, -0.05, -0.05], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.13333333333333333, 0.05, 0.05], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 5, "y": "0", "z": "0"}], "uuid": "596894ed-3061-0fed-f689-d2fed4b9785c", "time": 3, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.13333333333333333, -0.05, -0.05], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.13333333333333333, 0.05, 0.05], "bezier_right_value": [0, 0, 0]}]}, "ce3e2383-010d-03b5-2c18-1f82ea6055d8": {"name": "RightHip", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -2.5, "y": "0", "z": "0"}], "uuid": "d83a148b-63d2-49d1-4275-0fb8e6f373e7", "time": 0, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.13333333333333333, -0.05, -0.05], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.13333333333333333, 0.05, 0.05], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 5, "y": 0, "z": 0}], "uuid": "b12c6296-dd81-88db-6d44-f254da9a9714", "time": 0.75, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.13333333333333333, -0.05, -0.05], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.13333333333333333, 0.05, 0.05], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -2.5, "y": "0", "z": "0"}], "uuid": "9b7cae38-5b67-b1d3-0116-5ccad5e8e5d0", "time": 1.5, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.13333333333333333, -0.05, -0.05], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.13333333333333333, 0.05, 0.05], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 5, "y": 0, "z": 0}], "uuid": "7675dcdc-5414-c8a6-0097-f6bb35d1fa01", "time": 2.25, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.13333333333333333, -0.05, -0.05], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.13333333333333333, 0.05, 0.05], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -2.5, "y": "0", "z": "0"}], "uuid": "4b7133c7-459a-32e0-1f09-66c28b8788ce", "time": 3, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.13333333333333333, -0.05, -0.05], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.13333333333333333, 0.05, 0.05], "bezier_right_value": [0, 0, 0]}]}}}, {"uuid": "b1824729-3d13-3abe-5883-82d8a272f8cd", "name": "attackR", "loop": "once", "override": false, "length": 0.3333333333333333, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"f052b1a8-653c-610b-fba5-c6173a05c90b": {"name": "RightVeil", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": -7.5}], "uuid": "df56816f-9643-3c59-b0d8-b16fe0be9c22", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "eb499afb-7c79-a675-81d6-27b085d82d0c", "time": 0.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "ba1396f2-45f9-5209-ea06-eecec4836712", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom"}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-15", "y": "65", "z": "110"}], "uuid": "400bab91-5512-2cd7-e6ad-9ee85e341c6a", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-75", "y": "-25", "z": "45"}], "uuid": "675539cf-fc0a-24db-6bc8-d2ee73826d4a", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-90", "y": "5", "z": "35"}], "uuid": "389f5acf-802d-874e-d03f-bea07381b45b", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": 3}], "uuid": "d3895c23-82d5-9b8c-35d5-c09759b393ca", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": -1, "z": -2}], "uuid": "cd2ef62f-74b1-2f2a-7907-ecc3be9ceba9", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -1, "y": 0, "z": 4}], "uuid": "2ade5e55-2f3e-ed8d-c4e1-e1b3bbd972ab", "time": 0, "color": -1, "interpolation": "catmullrom"}]}, "effects": {"name": "Effects", "type": "effect", "keyframes": [{"channel": "timeline", "data_points": [{"script": "sounds:playSound(\"entity.warden.sonic_boom\", player:get<PERSON><PERSON>(), 0.1, .6, false)"}], "uuid": "d6c25f6c-4390-9781-da84-2592dd210be1", "time": 0.125, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "98dc92e4-915b-8c77-4cab-2c4be0c275d2", "name": "mineR", "loop": "once", "override": false, "length": 0.20833333333333334, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -77.5, "y": -27.5, "z": 15}], "uuid": "675539cf-fc0a-24db-6bc8-d2ee73826d4a", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": -27.5, "y": -15, "z": 20}], "uuid": "a9364a0a-cfc4-228b-cae4-69ad968ad024", "time": 0.20833333333333334, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": -1}], "uuid": "2ade5e55-2f3e-ed8d-c4e1-e1b3bbd972ab", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": -0.5, "y": -2, "z": -1}], "uuid": "ba1aace3-5a6d-5b6a-444c-5a817a29f9a0", "time": 0.20833333333333334, "color": -1, "interpolation": "catmullrom"}]}}}, {"uuid": "e4979073-108d-7e57-ea30-00bc4104894d", "name": "useR", "loop": "once", "override": false, "length": 0.375, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"3126f4e1-836b-4366-ae9f-f7f183d03b33": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-15", "y": 45, "z": -2.5}], "uuid": "cc3ce785-cae6-dd36-ff00-f1813e15fea7", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-25", "y": 5, "z": -15}], "uuid": "7484b0bc-7ac6-90c2-3657-3660e5d1e67e", "time": 0.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "34e87a62-4f35-afda-58c8-ee4893c80871", "time": 0.375, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-35", "y": 15, "z": -5}], "uuid": "48c59b08-8da2-552c-bace-b2824b15876f", "time": 0.125, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "2e6843ba-cb43-a55a-b6e9-11ad931a849b", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": "-1"}], "uuid": "7919d312-aec6-68ef-ef56-732240b85163", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "34282a60-99ea-d31b-2b5f-afff5c43e25a", "time": 0.375, "color": -1, "interpolation": "catmullrom"}]}, "b5cb972a-0c76-6ec0-1d8c-978a07c87f24": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "-15", "y": "-45", "z": "2.5"}], "uuid": "7ece56f4-1744-d5e2-d800-1d95d231f68a", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "253aa15c-19d7-1a71-88b7-93760e19b797", "time": 0.375, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-35", "y": "-15", "z": "5"}], "uuid": "ac6b13e3-40e1-5c81-ba7d-c3bb943b7851", "time": 0.125, "color": -1, "interpolation": "catmullrom"}, {"channel": "rotation", "data_points": [{"x": "-25", "y": "-5", "z": "15"}], "uuid": "caf792fe-7c2a-a636-c4c2-404722d17681", "time": 0.25, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": 0}], "uuid": "8c8a29f5-841a-92be-2cd3-1d57e495cd1a", "time": 0, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "3b63b503-b7bf-c5fd-2a5e-94909e8c4593", "time": 0.375, "color": -1, "interpolation": "catmullrom"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": "-1"}], "uuid": "d7f9be85-205c-e65b-2a72-640445f412c3", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom"}]}}}]}