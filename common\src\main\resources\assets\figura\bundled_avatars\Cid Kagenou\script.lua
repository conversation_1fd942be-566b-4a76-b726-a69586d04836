-- Auto generated script file --

--hide vanilla armor model
vanilla_model.ARMOR:setVisible(false)

--hide vanilla cape model
vanilla_model.CAPE:setVisible(false)

--hide vanilla elytra model
vanilla_model.ELYTRA:setVisible(false)
vanilla_model.PLAYER:setVisible(false)
models.shadow:setVisible(false)

local runLater = require("runLater")


models.shadow.Shadow.RightArm.RightForearm.Sword.Glow:setPrimaryRenderType("glint")
models.shadow.Shadow.RightArm.RightForearm.Sword.Glow:setSecondaryRenderType("emissive_solid")

models.shadow.WorldAtomic:setPrimaryRenderType("glint2")
models.shadow.WorldAtomic:setSecondaryRenderType("emissive_solid")

local mainPage = action_wheel:newPage()
action_wheel:setPage(mainPage)

local mySound = sounds["introshadow"]

local action = mainPage:newAction()
    :title("Shadow")
    :toggleTexture(textures["icons.cid"], 0, 0, 180, 179, 0.1)
    :texture(textures["icons.shadow"], 0, 0, 176, 170, 0.1)
    :hoverColor(1, 0, 1)

    function pings.shadow(state)
        if state then
            models.shadow:setVisible(true)
            models.cid:setVisible(false)
            sounds:playSound("introshadow", player:getPos())
        else
            models.shadow:setVisible(false)
            models.cid:setVisible(true)
            sounds:stopSound("introshadow", player:getPos())
        end
    end

    action:onToggle(pings.shadow)


local action = mainPage:newAction()
    :title("Atomic")
    :texture(textures["icons.atomic"], 0, 0, 192, 192, 0.1)
    :hoverColor(1, 0, 1)

    function pings.atomic()
        models.shadow.WorldAtomic:setPos(player:getPos()*16)
        animations.shadow.atomic:play()
        sounds:playSound("atomic", player:getPos())
        runLater(210, function() renderer:offsetCameraPivot(0, 200, 0) renderer:setCameraRot(90, 90, 90) end)
    end
    
    action:onLeftClick(function()
        if models.shadow:getVisible() and not animations.shadow.atomic:isPlaying() then
          pings.atomic()
        end
      end)

function events.render()
    if animations.shadow.atomic:isPlaying() then
        vanilla_model.HELD_ITEMS:setVisible(false)
         models.shadow.Shadow.RightArm.RightForearm.Sword:setVisible(true)
         models.shadow.ItemSword:setVisible(false)	
         models.shadow.WorldAtomic:setVisible(true)
    else
        vanilla_model.HELD_ITEMS:setVisible(true)
        models.shadow.Shadow.RightArm.RightForearm.Sword:setVisible(false)
        models.shadow.ItemSword:setVisible(true)
        models.shadow.ItemSword.Glow2:setVisible(false)   
        models.shadow.WorldAtomic:setVisible(false)
          end
    end

    local wasPlaying = false

    function events.tick()
        local isPlaying = animations.shadow.atomic:isPlaying()
        if isPlaying == false and wasPlaying == true then
        renderer:offsetCameraPivot(0, 0, 0) renderer:setCameraRot()
        end
         wasPlaying = isPlaying
    end

    keybinds:fromVanilla("key.forward").press = function() return animations.shadow.atomic:isPlaying() end 
    keybinds:fromVanilla("key.back").press = function() return animations.shadow.atomic:isPlaying() end 
    keybinds:fromVanilla("key.left").press = function() return animations.shadow.atomic:isPlaying() end 
    keybinds:fromVanilla("key.right").press = function() return animations.shadow.atomic:isPlaying() end
    keybinds:fromVanilla("key.jump").press = function() return animations.shadow.atomic:isPlaying() end
    keybinds:fromVanilla("key.sneak").press = function() return animations.shadow.atomic:isPlaying() end

    function events.item_render(item)
        if item.id == "minecraft:netherite_sword" then
            return models.shadow.ItemSword
        end
    end


local SwingingPhysics = require("swinging_physics")
local swingOnHead = SwingingPhysics.swingOnHead
local swingOnBody = SwingingPhysics.swingOnBody

swingOnBody(models.cid.Body.Tie, 0, { 0, 50, 0, 0, -30, 30})

Cloak = models.shadow.Shadow.Body.Cloak

swingOnBody(Cloak.a1, 0)
swingOnBody(Cloak.a1, 0, nil, Cloak.a1.b1, 1)
swingOnBody(Cloak.a1.b1, 0, nil, Cloak.a1.b1.c1, 2)
swingOnBody(Cloak.a1.b1.c1, 0, nil, Cloak.a1.b1.c1.d1, 3)

swingOnBody(Cloak.a2, 0)
swingOnBody(Cloak.a2, 0, nil, Cloak.a2.b2, 1)
swingOnBody(Cloak.a2.b2, 0, nil, Cloak.a2.b2.c2, 2)
swingOnBody(Cloak.a2.b2.c2, 0, nil, Cloak.a2.b2.c2.d2, 3)

swingOnBody(Cloak.a3, 0)
swingOnBody(Cloak.a3, 0, nil, Cloak.a3.b3, 1)
swingOnBody(Cloak.a3.b3, 0, nil, Cloak.a3.b3.c3, 2)
swingOnBody(Cloak.a3.b3.c3, 0, nil, Cloak.a3.b3.c3.d3, 3)

swingOnBody(Cloak.a4, 0)
swingOnBody(Cloak.a4, 0, nil, Cloak.a4.b4, 1)
swingOnBody(Cloak.a4.b4, 0, nil, Cloak.a4.b4.c4, 2)
swingOnBody(Cloak.a4.b4.c4, 0, nil, Cloak.a4.b4.c4.d4, 3)

swingOnBody(Cloak.a5, 0)
swingOnBody(Cloak.a5, 0, nil, Cloak.a5.b5, 1)
swingOnBody(Cloak.a5.b5, 0, nil, Cloak.a5.b5.c5, 2)
swingOnBody(Cloak.a5.b5.c5, 0, nil, Cloak.a5.b5.c5.d5, 3)

swingOnBody(Cloak.a6, 0)
swingOnBody(Cloak.a6, 0, nil, Cloak.a6.b6, 1)
swingOnBody(Cloak.a6.b6, 0, nil, Cloak.a6.b6.c6, 2)
swingOnBody(Cloak.a6.b6.c6, 0, nil, Cloak.a6.b6.c6.d6, 3)

swingOnBody(Cloak.a7, 0)
swingOnBody(Cloak.a7, 0, nil, Cloak.a7.b7, 1)
swingOnBody(Cloak.a7.b7, 0, nil, Cloak.a7.b7.c7, 2)
swingOnBody(Cloak.a7.b7.c7, 0, nil, Cloak.a7.b7.c7.d7, 3)

swingOnBody(Cloak.a8, 0)
swingOnBody(Cloak.a8, 0, nil, Cloak.a8.b8, 1)
swingOnBody(Cloak.a8.b8, 0, nil, Cloak.a8.b8.c8, 2)
swingOnBody(Cloak.a8.b8.c8, 0, nil, Cloak.a8.b8.c8.d8, 3)

swingOnBody(Cloak.a9, 0)
swingOnBody(Cloak.a9, 0, nil, Cloak.a9.b9, 1)
swingOnBody(Cloak.a9.b9, 0, nil, Cloak.a9.b9.c9, 2)
swingOnBody(Cloak.a9.b9.c9, 0, nil, Cloak.a9.b9.c9.d9, 3)

Ribbons = models.shadow.Shadow.Body.Ribbons

swingOnBody(Ribbons.L1, 90)
swingOnBody(Ribbons.L1, 90, nil, Ribbons.L1.L2, 1)
swingOnBody(Ribbons.L1.L2, 90, nil, Ribbons.L1.L2.L3, 2)

swingOnBody(Ribbons.R1, 90)
swingOnBody(Ribbons.R1, 90, niR, Ribbons.R1.R2, 1)
swingOnBody(Ribbons.R1.R2, 90, niR, Ribbons.R1.R2.R3, 2)