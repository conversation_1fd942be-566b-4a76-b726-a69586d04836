-- Auto generated script file --
vanilla_model.ARMOR:setVisible(false)
vanilla_model.CAPE:setVisible(false)
vanilla_model.ELYTRA:setVisible(false)
vanilla_model.PLAYER:setVisible(false)

--Action Page--

local mainPage = action_wheel:newPage()
action_wheel:setPage(mainPage)


--Blindfold--

local action = mainPage:newAction()
action:title("Blindfold")
action:item("minecraft:black_wool")
action:hoverColor(1, 1, 1)

function pings.blindfold(state)
	if state then
	models.model.Adult.Upper.Head.blindfold:setVisible(false)
	else
	models.model.Adult.Upper.Head.blindfold:setVisible(true)
	end
end

action:onToggle(pings.blindfold)

--Hollow Purple--

local action = mainPage:newAction()
action:title("Hollow Purple")
action:item("minecraft:purple_wool")
action:hoverColor(1, 0, 1)

function pings.purple()
	animations.model.purple:play()
	sounds:playSound("sound", player:getPos()):setVolume(100)
	
end

action:onLeftClick(pings.purple)

--Hollow Purple Anim--

function events.render()
if animations.model.purple:isPlaying() then

 	models.model.orb:setVisible(true)
	
	renderer:setPostEffect("deconverge")		

else

 	models.model.orb:setVisible(false)

	renderer:setPostEffect("")


  end
end

keybinds:fromVanilla("key.forward").press = function() return animations.model.purple:isPlaying() end 
keybinds:fromVanilla("key.back").press = function() return animations.model.purple:isPlaying() end 
keybinds:fromVanilla("key.left").press = function() return animations.model.purple:isPlaying() end 
keybinds:fromVanilla("key.right").press = function() return animations.model.purple:isPlaying() end
keybinds:fromVanilla("key.jump").press = function() return animations.model.purple:isPlaying() end
keybinds:fromVanilla("key.sneak").press = function() return animations.model.purple:isPlaying() end


local anims = require("JimmyAnims")
anims.addAllAnims(animations.model.purple)
anims(animations.model)


