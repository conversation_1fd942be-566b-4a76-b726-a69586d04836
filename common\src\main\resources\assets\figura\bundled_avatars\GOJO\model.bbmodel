{"meta": {"format_version": "4.5", "model_format": "free", "box_uv": false}, "name": "model", "model_identifier": "", "visible_box": [1, 1, 0], "variable_placeholders": "", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 128, "height": 128}, "elements": [{"name": "Head", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 29, -4], "to": [4, 37, 4], "autouv": 0, "color": 0, "origin": [0, 5, 0], "uv_offset": [64, 0], "faces": {"north": {"uv": [72, 8, 80, 16], "texture": 0}, "east": {"uv": [64, 8, 72, 16], "texture": 0}, "south": {"uv": [88, 8, 96, 16], "texture": 0}, "west": {"uv": [80, 8, 88, 16], "texture": 0}, "up": {"uv": [80, 8, 72, 0], "texture": 0}, "down": {"uv": [88, 0, 80, 8], "texture": 0}}, "type": "cube", "uuid": "8836f10d-ad74-a373-b095-805b330f0485"}, {"name": "<PERSON> Layer", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 29, -4], "to": [4, 37, 4], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 5, 0], "uv_offset": [96, 0], "faces": {"north": {"uv": [104, 8, 112, 16], "texture": 0}, "east": {"uv": [96, 8, 104, 16], "texture": 0}, "south": {"uv": [120, 8, 128, 16], "texture": 0}, "west": {"uv": [112, 8, 120, 16], "texture": 0}, "up": {"uv": [112, 8, 104, 0], "texture": 0}, "down": {"uv": [120, 0, 112, 8], "texture": 0}}, "type": "cube", "uuid": "c16b4b94-2577-a614-8b6e-07da7770ca8c"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 15, -2], "to": [4, 29, 2], "autouv": 0, "color": 0, "origin": [0, 3, 0], "uv_offset": [80, 16], "faces": {"north": {"uv": [84, 20, 92, 32], "texture": 0}, "east": {"uv": [80, 20, 84, 32], "texture": 0}, "south": {"uv": [96, 20, 104, 32], "texture": 0}, "west": {"uv": [92, 20, 96, 32], "texture": 0}, "up": {"uv": [92, 20, 84, 16], "texture": 0}, "down": {"uv": [100, 16, 92, 20], "texture": 0}}, "type": "cube", "uuid": "cd3dc122-7717-708b-2abd-1b0acd2a1369"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 15, -2], "to": [4, 29, 2], "autouv": 0, "color": 0, "inflate": 0.25, "origin": [0, 3, 0], "uv_offset": [80, 32], "faces": {"north": {"uv": [84, 36, 92, 47], "texture": 0}, "east": {"uv": [80, 36, 84, 47], "texture": 0}, "south": {"uv": [96, 36, 104, 47], "texture": 0}, "west": {"uv": [92, 36, 96, 47], "texture": 0}, "up": {"uv": [92, 36, 84, 32], "texture": 0}, "down": {"uv": [100, 32, 92, 36], "texture": 0}}, "type": "cube", "uuid": "ee418ab8-cc9a-86a8-454f-3a03a441a9a7"}, {"name": "RightArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 22, -2], "to": [8, 28, 2], "autouv": 0, "color": 0, "origin": [6, 26, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [108, 20, 112, 26], "texture": 0}, "east": {"uv": [104, 20, 108, 26], "texture": 0}, "south": {"uv": [116, 20, 120, 26], "texture": 0}, "west": {"uv": [112, 20, 116, 26], "texture": 0}, "up": {"uv": [112, 20, 108, 16], "texture": 0}, "down": {"uv": [112, 16, 108, 20], "texture": 0}}, "type": "cube", "uuid": "b06c71bb-34d7-b890-e949-f5f4d1cda149"}, {"name": "LeftArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 21, -2], "to": [-4, 28, 2], "autouv": 0, "color": 0, "origin": [-6, 24.5, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [100, 52, 104, 58], "texture": 0}, "east": {"uv": [96, 52, 100, 58], "texture": 0}, "south": {"uv": [108, 52, 112, 58], "texture": 0}, "west": {"uv": [104, 52, 108, 58], "texture": 0}, "up": {"uv": [104, 52, 100, 48], "texture": 0}, "down": {"uv": [104, 48, 100, 52], "texture": 0}}, "type": "cube", "uuid": "08b347ba-d53f-b0c9-ca8b-0021d67bbbae"}, {"name": "RightThigh", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.10000000000000009, 8, -2], "to": [3.9, 16, 2], "autouv": 0, "color": 0, "origin": [1.9, 12, 0], "uv_offset": [80, 48], "faces": {"north": {"uv": [68, 20, 72, 26], "texture": 0}, "east": {"uv": [64, 20, 68, 26], "texture": 0}, "south": {"uv": [76, 20, 80, 26], "texture": 0}, "west": {"uv": [72, 20, 76, 26], "texture": 0}, "up": {"uv": [72, 20, 68, 16], "texture": 0}, "down": {"uv": [76, 16, 72, 20], "texture": 0}}, "type": "cube", "uuid": "5ec784f1-94a3-6ef0-350e-753fbf978f82"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.10000000000000009, 0, -2], "to": [3.9, 14, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.25, "origin": [0, 0, 0], "uv_offset": [64, 32], "faces": {"north": {"uv": [68, 36, 72, 50], "texture": 0}, "east": {"uv": [64, 36, 68, 50], "texture": 0}, "south": {"uv": [76, 36, 80, 50], "texture": 0}, "west": {"uv": [72, 36, 76, 50], "texture": 0}, "up": {"uv": [72, 36, 68, 32], "texture": 0}, "down": {"uv": [76, 32, 72, 36], "texture": 0}}, "type": "cube", "uuid": "96831a0a-cdd5-65e1-c7cc-e3b1e917fefa"}, {"name": "LeftShin", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9, 0, -2], "to": [0.10000000000000009, 8, 2], "autouv": 0, "color": 0, "origin": [-1.9, 4, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [84, 58, 88, 64], "texture": 0}, "east": {"uv": [80, 58, 84, 64], "texture": 0}, "south": {"uv": [92, 58, 96, 64], "texture": 0}, "west": {"uv": [88, 58.1, 92, 64.1], "texture": 0}, "up": {"uv": [88, 52, 84, 48], "texture": 0}, "down": {"uv": [92, 48, 88, 52], "texture": 0}}, "type": "cube", "uuid": "43ab0a37-4e55-ae76-cd24-db0bd538a867"}, {"name": "LeftLegLayer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9, 0, -2], "to": [0.10000000000000009, 14, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.25, "origin": [0, 0, 0], "uv_offset": [64, 48], "faces": {"north": {"uv": [68, 52.1, 72, 66.1], "texture": 0}, "east": {"uv": [64, 52.1, 68, 66.1], "texture": 0}, "south": {"uv": [76, 52.1, 80, 66.1], "texture": 0}, "west": {"uv": [72, 52.1, 76, 66.1], "texture": 0}, "up": {"uv": [72, 52.1, 68, 48.1], "texture": 0}, "down": {"uv": [76, 48.1, 72, 52.1], "texture": 0}}, "type": "cube", "uuid": "aafaa54e-f993-71d5-2130-1eceb5faa3d0"}, {"name": "a", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.75, 31, -4.75], "to": [4.75, 34, -3], "autouv": 0, "color": 6, "origin": [0, 6, 0], "uv_offset": [1, 1], "faces": {"north": {"uv": [44, 0, 64, 6], "texture": 0}, "east": {"uv": [18, 56, 22, 62], "texture": 0}, "south": {"uv": [44, 8, 64, 14], "texture": 0}, "west": {"uv": [24, 56, 28, 62], "texture": 0}, "up": {"uv": [64, 36, 44, 32], "texture": 0}, "down": {"uv": [20, 46, 0, 50], "texture": 0}}, "type": "cube", "uuid": "c7ebed34-8da9-e187-a3f3-2b6a0d63517e"}, {"name": "c", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.75, 32, -3.5], "to": [4.75, 33, 0], "autouv": 0, "color": 6, "rotation": [-22.5, 0, 0], "origin": [0, 32.5, -0.75], "uv_offset": [-1, -1], "faces": {"north": {"uv": [18, 52, 38, 54], "texture": 0}, "east": {"uv": [30, 56, 38, 58], "texture": 0}, "south": {"uv": [40, 52, 60, 54], "texture": 0}, "west": {"uv": [40, 56, 48, 58], "texture": 0}, "up": {"uv": [42, 26, 22, 18], "texture": 0}, "down": {"uv": [42, 28, 22, 36], "texture": 0}}, "type": "cube", "uuid": "5cc2089e-9f80-8ed6-0e2a-c9d4945770fc"}, {"name": "b", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.75, 32, -3], "to": [4.75, 34, 4.75], "autouv": 0, "color": 6, "origin": [0, 6, 0], "uv_offset": [-4, -4], "faces": {"north": {"uv": [22, 46, 42, 50], "texture": 0}, "east": {"uv": [48, 38, 64, 42], "texture": 0}, "south": {"uv": [44, 46, 64, 50], "texture": 0}, "west": {"uv": [0, 52, 16, 56], "texture": 0}, "up": {"uv": [20, 36, 0, 20], "texture": 0}, "down": {"uv": [42, 0, 22, 16], "texture": 0}}, "type": "cube", "uuid": "e3376eb1-3462-4947-4ff7-d3ae5593b5c8"}, {"name": "collar", "color": 6, "origin": [0, 24.000000000000007, -2.886579864025407e-15], "rotation": [-180, 0, 0], "visibility": true, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "vertices": {"4Q9o": [5.125, -2.5000000000000098, 4.659090909090907], "wWHX": [5.125, -2.500000000000007, -4.659090909090906], "HbVb": [5.5, -5.499999999999994, 4.999999999999994], "hd9Q": [5.5, -5.500000000000007, -4.999999999999997], "J8yl": [-5.125, -2.5000000000000098, 4.659090909090907], "u6tT": [-5.125, -2.500000000000007, -4.659090909090906], "3fW8": [-5.5, -5.499999999999999, 4.999999999999996], "ztUJ": [-5.5, -5.500000000000007, -4.999999999999997]}, "faces": {"Yo6L5QRu": {"uv": {"4Q9o": [44.6818, 24], "HbVb": [44, 30], "wWHX": [63.3182, 24], "hd9Q": [64, 30]}, "vertices": ["4Q9o", "HbVb", "wWHX", "hd9Q"], "texture": 0}, "nJR2WKnO": {"uv": {"J8yl": [63.3182, 16], "u6tT": [44.6818, 16], "3fW8": [64, 22], "ztUJ": [44, 22]}, "vertices": ["J8yl", "u6tT", "3fW8", "ztUJ"], "texture": 0}, "1m6Zm8eD": {"uv": {"4Q9o": [20, 18], "wWHX": [20, 0], "J8yl": [0, 18], "u6tT": [0, 0]}, "vertices": ["4Q9o", "wWHX", "J8yl", "u6tT"], "texture": 0}, "sO0jDBcG": {"uv": {"4Q9o": [21.25, 38], "J8yl": [0.75, 38], "HbVb": [22, 44], "3fW8": [0, 44]}, "vertices": ["4Q9o", "J8yl", "HbVb", "3fW8"], "texture": 0}, "HsR2ZzkT": {"uv": {"wWHX": [24.75, 38], "hd9Q": [24, 44], "u6tT": [45.25, 38], "ztUJ": [46, 44]}, "vertices": ["wWHX", "hd9Q", "u6tT", "ztUJ"], "texture": 0}}, "type": "mesh", "uuid": "4aac8e5c-92b0-3fae-c9bc-2548b91910c1"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-22, 20, 12], "to": [-10, 32, 24], "autouv": 0, "color": 0, "visibility": false, "origin": [-16, 26, 18], "faces": {"north": {"uv": [0, 64, 8, 72], "texture": 0}, "east": {"uv": [0, 72, 8, 80], "texture": 0}, "south": {"uv": [8, 64, 16, 72], "texture": 0}, "west": {"uv": [8, 72, 16, 80], "texture": 0}, "up": {"uv": [8, 88, 0, 80], "texture": 0}, "down": {"uv": [24, 64, 16, 72], "texture": 0}}, "type": "cube", "uuid": "0b089287-78b4-14a7-3ee6-a0cd9ab15b2c"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [10, 20, 12], "to": [22, 32, 24], "autouv": 0, "color": 0, "visibility": false, "origin": [0, 6, 36], "faces": {"north": {"uv": [8, 80, 16, 88], "texture": 0}, "east": {"uv": [16, 72, 24, 80], "texture": 0}, "south": {"uv": [16, 80, 24, 88], "texture": 0}, "west": {"uv": [0, 88, 8, 96], "texture": 0}, "up": {"uv": [32, 72, 24, 64], "texture": 0}, "down": {"uv": [16, 88, 8, 96], "texture": 0}}, "type": "cube", "uuid": "a2db08bd-f692-2435-e345-9f4e1bac0ae9"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 20, 12], "to": [6, 32, 24], "autouv": 0, "color": 0, "visibility": false, "origin": [0, 26, -56], "faces": {"north": {"uv": [32, 64, 40, 72], "texture": 0}, "east": {"uv": [32, 72, 40, 80], "texture": 0}, "south": {"uv": [40, 64, 48, 72], "texture": 0}, "west": {"uv": [40, 72, 48, 80], "texture": 0}, "up": {"uv": [40, 88, 32, 80], "texture": 0}, "down": {"uv": [56, 64, 48, 72], "texture": 0}}, "type": "cube", "uuid": "2c78b486-d871-f90a-4f1f-3f469195c6d8"}, {"name": "LeftForeArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 15, -2], "to": [-4, 22, 2], "autouv": 0, "color": 0, "origin": [-6, 18.5, 0], "uv_offset": [32, 54], "faces": {"north": {"uv": [100, 58, 104, 64], "texture": 0}, "east": {"uv": [96, 58, 100, 64], "texture": 0}, "south": {"uv": [108, 58, 112, 64], "texture": 0}, "west": {"uv": [104, 58, 108, 64], "texture": 0}, "up": {"uv": [104, 52, 100, 48], "texture": 0}, "down": {"uv": [108, 48, 104, 52], "texture": 0}}, "type": "cube", "uuid": "5b8667bf-0ad6-efaa-049c-d12e498171b8"}, {"name": "RightForeArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 15, -2], "to": [8, 22, 2], "autouv": 0, "color": 0, "origin": [6, 18.5, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [108, 26, 112, 32], "texture": 0}, "east": {"uv": [104, 26, 108, 32], "texture": 0}, "south": {"uv": [116, 26, 120, 32], "texture": 0}, "west": {"uv": [112, 26, 116, 32], "texture": 0}, "up": {"uv": [112, 20, 108, 16], "texture": 0}, "down": {"uv": [116, 16, 112, 20], "texture": 0}}, "type": "cube", "uuid": "e9269513-a5fd-e26d-64e4-208e53b85312"}, {"name": "LeftThigh", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9, 8, -2], "to": [0.10000000000000009, 16, 2], "autouv": 0, "color": 0, "origin": [-1.9, 12, 0], "uv_offset": [64, 16], "faces": {"north": {"uv": [84, 52, 88, 58], "texture": 0}, "east": {"uv": [80, 52, 84, 58], "texture": 0}, "south": {"uv": [92, 52, 96, 58], "texture": 0}, "west": {"uv": [88, 52.1, 92, 58.1], "texture": 0}, "up": {"uv": [88, 52, 84, 48], "texture": 0}, "down": {"uv": [92, 48, 88, 52], "texture": 0}}, "type": "cube", "uuid": "345ba904-99be-90a9-966d-7cf1db9d86e0"}, {"name": "RightShin", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.10000000000000009, 0, -2], "to": [3.9, 8, 2], "autouv": 0, "color": 0, "origin": [1.9, 4, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [68, 26, 72, 32], "texture": 0}, "east": {"uv": [64, 26, 68, 32], "texture": 0}, "south": {"uv": [76, 26, 80, 32], "texture": 0}, "west": {"uv": [72, 26, 76, 32], "texture": 0}, "up": {"uv": [72, 20, 68, 16], "texture": 0}, "down": {"uv": [76, 16, 72, 20], "texture": 0}}, "type": "cube", "uuid": "29854449-7a69-7476-6b23-ed295e6d1d49"}, {"name": "LForearmLayer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 15, -2], "to": [-4, 22, 2], "autouv": 0, "color": 0, "inflate": 0.25, "origin": [-6, 18.5, 0], "uv_offset": [112, 54], "faces": {"north": {"uv": [116, 58, 120, 64], "texture": 0}, "east": {"uv": [112, 58, 116, 64], "texture": 0}, "south": {"uv": [124, 58, 128, 64], "texture": 0}, "west": {"uv": [120, 58, 124, 64], "texture": 0}, "up": {"uv": [120, 52, 116, 48], "texture": 0}, "down": {"uv": [124, 48, 120, 52], "texture": 0}}, "type": "cube", "uuid": "df3bff7f-3021-1f87-9fce-e0e9872bac86"}, {"name": "LArmLayer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 22, -2], "to": [-4, 28, 2], "autouv": 0, "color": 0, "inflate": 0.25, "origin": [-6, 25, 0], "uv_offset": [112, 48], "faces": {"north": {"uv": [116, 52, 120, 58], "texture": 0}, "east": {"uv": [112, 52, 116, 58], "texture": 0}, "south": {"uv": [124, 52, 128, 58], "texture": 0}, "west": {"uv": [120, 52, 124, 58], "texture": 0}, "up": {"uv": [120, 52, 116, 48], "texture": 0}, "down": {"uv": [124, 48, 120, 52], "texture": 0}}, "type": "cube", "uuid": "fb914d80-f874-2a7c-8da2-ff2bcfa6b67a"}, {"name": "RForearmLayer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 15, -2], "to": [8, 22, 2], "autouv": 0, "color": 0, "inflate": 0.25, "origin": [6, 18.5, 0], "uv_offset": [104, 37], "faces": {"north": {"uv": [108, 42, 112, 48], "texture": 0}, "east": {"uv": [104, 42, 108, 48], "texture": 0}, "south": {"uv": [116, 42, 120, 48], "texture": 0}, "west": {"uv": [112, 42, 116, 48], "texture": 0}, "up": {"uv": [112, 36, 108, 32], "texture": 0}, "down": {"uv": [116, 32, 112, 36], "texture": 0}}, "type": "cube", "uuid": "fd7b1c54-c42d-6956-80e1-e61af9ad8f2e"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 22, -2], "to": [8, 28, 2], "autouv": 0, "color": 0, "inflate": 0.25, "origin": [0, 4, 0], "uv_offset": [104, 32], "faces": {"north": {"uv": [108, 36, 112, 42], "texture": 0}, "east": {"uv": [104, 36, 108, 42], "texture": 0}, "south": {"uv": [116, 36, 120, 42], "texture": 0}, "west": {"uv": [112, 36, 116, 42], "texture": 0}, "up": {"uv": [112, 36, 108, 32], "texture": 0}, "down": {"uv": [116, 32, 112, 36], "texture": 0}}, "type": "cube", "uuid": "54cdc5dd-5216-5285-c92d-eeb04a4d3893"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.10000000000000009, 9, -2], "to": [3.9, 16, 2], "autouv": 0, "color": 0, "inflate": 0.25, "origin": [1.9, 12.5, 0], "uv_offset": [64, 32], "faces": {"north": {"uv": [68, 36, 72, 43], "texture": 0}, "east": {"uv": [64, 36, 68, 43], "texture": 0}, "south": {"uv": [76, 36, 80, 43], "texture": 0}, "west": {"uv": [72, 36, 76, 43], "texture": 0}, "up": {"uv": [72, 36, 68, 32], "texture": 0}, "down": {"uv": [76, 32, 72, 36], "texture": 0}}, "type": "cube", "uuid": "11330f08-9f23-d2e6-d710-65915dfd4f79"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.10000000000000009, 1, -2], "to": [3.9, 8, 2], "autouv": 0, "color": 0, "inflate": 0.25, "origin": [1.9, 4.5, 0], "uv_offset": [64, 32], "faces": {"north": {"uv": [68, 42, 72, 47], "texture": 0}, "east": {"uv": [64, 42, 68, 47], "texture": 0}, "south": {"uv": [76, 42, 80, 47], "texture": 0}, "west": {"uv": [72, 42, 76, 47], "texture": 0}, "up": {"uv": [72, 36, 68, 32], "texture": 0}, "down": {"uv": [76, 32, 72, 36], "texture": 0}}, "type": "cube", "uuid": "662fa1f7-8d2a-09b0-9828-3cf29408a85e"}, {"name": "LeftThighLayer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9, 8, -2], "to": [0.10000000000000009, 15, 2], "autouv": 0, "color": 0, "inflate": 0.25, "origin": [-1.9, 12.5, 0], "uv_offset": [64, 48], "faces": {"north": {"uv": [68, 52, 72, 58], "texture": 0}, "east": {"uv": [64, 52, 68, 58], "texture": 0}, "south": {"uv": [76, 52, 80, 58], "texture": 0}, "west": {"uv": [72, 52, 76, 58], "texture": 0}, "up": {"uv": [72, 52, 68, 48], "texture": 0}, "down": {"uv": [76, 48, 72, 52], "texture": 0}}, "type": "cube", "uuid": "88e9ebd0-bdeb-a6ee-3393-db6ad17d2b86"}, {"name": "LeftShinLayer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9, 1, -2], "to": [0.10000000000000009, 8, 2], "autouv": 0, "color": 0, "inflate": 0.25, "origin": [-1.9, 4.5, 0], "uv_offset": [64, 48], "faces": {"north": {"uv": [68, 58, 72, 64], "texture": 0}, "east": {"uv": [64, 58, 68, 64], "texture": 0}, "south": {"uv": [76, 58, 80, 64], "texture": 0}, "west": {"uv": [72, 58, 76, 64], "texture": 0}, "up": {"uv": [72, 52, 68, 48], "texture": 0}, "down": {"uv": [76, 48, 72, 52], "texture": 0}}, "type": "cube", "uuid": "b35ce4c6-3118-c3f3-5c5b-b9eb403af729"}], "outliner": [{"name": "Adult", "origin": [0, 0, 0], "color": 0, "uuid": "ce3399e2-f4e2-2b14-3731-1e86aae19b7c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "Upper", "origin": [0, 15, 0], "color": 0, "uuid": "3f40c689-921d-3dae-e7c8-477d94e2bba6", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "Head", "origin": [0, 29, 0], "color": 0, "uuid": "aacded47-fe3a-9184-6a1e-a9b169feeb8c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["8836f10d-ad74-a373-b095-805b330f0485", "c16b4b94-2577-a614-8b6e-07da7770ca8c", {"name": "blindfold", "origin": [0, 32.5, -0.75], "color": 0, "uuid": "9ca62ef7-09d8-954a-9b30-f4edac731cba", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c7ebed34-8da9-e187-a3f3-2b6a0d63517e", "e3376eb1-3462-4947-4ff7-d3ae5593b5c8", "5cc2089e-9f80-8ed6-0e2a-c9d4945770fc"]}]}, {"name": "Body", "origin": [0, 27, 0], "color": 0, "uuid": "42010b6c-4a17-70a1-d68c-3280f4f9d4db", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["cd3dc122-7717-708b-2abd-1b0acd2a1369", "ee418ab8-cc9a-86a8-454f-3a03a441a9a7", "4aac8e5c-92b0-3fae-c9bc-2548b91910c1"]}, {"name": "RightArm", "origin": [6, 28, 0], "color": 0, "uuid": "2c1299be-a255-b6b9-2896-c54d54ed6afc", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "origin": [6, 27, 0], "color": 0, "uuid": "41adb89d-5f91-8a98-dd08-32bc7838ee78", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "ArmR", "origin": [6, 28, 0], "color": 0, "uuid": "5d5eb734-b7ba-4a67-7472-be58611e14b0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "RightShoulder", "origin": [6, 28, 0], "color": 0, "uuid": "95fe918e-9ea9-5722-79e0-aa2d9a95fbc1", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["b06c71bb-34d7-b890-e949-f5f4d1cda149", "54cdc5dd-5216-5285-c92d-eeb04a4d3893"]}, {"name": "RightForeArm", "origin": [6, 22, 0], "color": 0, "uuid": "4456008a-ff94-4184-466c-30b325f3adea", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "RIGHT_ITEM_PIVOT", "origin": [6, 15, 0], "rotation": [-35, 0, 0], "color": 0, "uuid": "360eb96b-53df-e431-59c1-e299bbbace57", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, "e9269513-a5fd-e26d-64e4-208e53b85312", "fd7b1c54-c42d-6956-80e1-e61af9ad8f2e"]}]}]}]}, {"name": "LeftArm", "origin": [-6, 25, 0], "color": 0, "uuid": "63d4216b-0f6f-31cd-1b0a-a4b10cd7fbb1", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "armaniml", "origin": [0, -1, 0], "color": 0, "uuid": "9b638422-9d9a-e9b6-28bf-85b1b2b51cef", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "ArmL", "origin": [-6, 28, 0], "color": 0, "uuid": "09beb120-7c1f-417d-15eb-0b9fe41dbad0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "LeftShoulder", "origin": [-6, 28, 0], "color": 0, "uuid": "31c3cd66-9bcd-13f9-0015-be821dce69ce", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["fb914d80-f874-2a7c-8da2-ff2bcfa6b67a", "08b347ba-d53f-b0c9-ca8b-0021d67bbbae"]}, {"name": "LeftForeArm", "origin": [-6, 22, 0], "color": 0, "uuid": "c06cb0df-2f23-3573-91fe-e54fd0242131", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "LEFT_ITEM_PIVOT", "origin": [-6, 15, 0], "rotation": [-35, 0, 0], "color": 0, "uuid": "e936603c-b5ed-92be-88ef-288f10c67c5f", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, "5b8667bf-0ad6-efaa-049c-d12e498171b8", "df3bff7f-3021-1f87-9fce-e0e9872bac86"]}]}]}]}]}, {"name": "LeftLeg", "origin": [-1.9, 14, 0], "color": 0, "uuid": "627f20f3-1b81-f4c3-7529-a1517078ac00", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "LeftThigh", "origin": [-1.9, 16, 0], "color": 0, "uuid": "16eab9d4-3d4a-2537-899b-56bb0fe531ca", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["345ba904-99be-90a9-966d-7cf1db9d86e0", "88e9ebd0-bdeb-a6ee-3393-db6ad17d2b86"]}, {"name": "LeftShin", "origin": [-1.9, 8, 0], "color": 0, "uuid": "f19b5ba5-8a75-d9a6-9837-3cb6745ab312", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["43ab0a37-4e55-ae76-cd24-db0bd538a867", "b35ce4c6-3118-c3f3-5c5b-b9eb403af729"]}, "aafaa54e-f993-71d5-2130-1eceb5faa3d0"]}, {"name": "RightLeg", "origin": [1.9, 14, 0], "color": 0, "uuid": "4a425455-781c-7bf1-309c-0892212ef029", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "RightThigh", "origin": [1.9, 16, 0], "color": 0, "uuid": "7fbee9a1-51cc-d70c-1f91-8873240ba5c1", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5ec784f1-94a3-6ef0-350e-753fbf978f82", "11330f08-9f23-d2e6-d710-65915dfd4f79"]}, {"name": "RightShin", "origin": [1.9, 8, 0], "color": 0, "uuid": "925fd3d7-56af-f560-255d-4c3f14af59c0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["29854449-7a69-7476-6b23-ed295e6d1d49", "662fa1f7-8d2a-09b0-9828-3cf29408a85e"]}, "96831a0a-cdd5-65e1-c7cc-e3b1e917fefa"]}]}, {"name": "orb", "origin": [0, 6, -40], "color": 0, "uuid": "e196d4e0-6b78-c3b8-56cc-4a62229e97c8", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": [{"name": "purple", "origin": [0, 26, 18], "color": 0, "uuid": "c64b214e-372f-3442-db55-c6869c472187", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": false, "autouv": 0, "children": ["2c78b486-d871-f90a-4f1f-3f469195c6d8"]}, {"name": "red", "origin": [16, 26, 18], "color": 0, "uuid": "ab79a035-1e98-5f41-8358-c2aea36e2214", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": false, "autouv": 0, "children": ["a2db08bd-f692-2435-e345-9f4e1bac0ae9"]}, {"name": "blue", "origin": [-16, 26, 18], "color": 0, "uuid": "ebff5484-022e-adc8-f175-98420d5d779e", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": false, "autouv": 0, "children": ["0b089287-78b4-14a7-3ee6-a0cd9ab15b2c"]}]}], "textures": [{"path": "C:\\Users\\<USER>\\AppData\\Roaming\\PrismLauncher\\instances\\goober\\.minecraft\\figura\\avatars\\Gojo\\stiched_texture.png", "name": "stiched_texture.png", "folder": "", "namespace": "", "id": "4", "particle": false, "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "mode": "bitmap", "saved": false, "uuid": "358a8b20-8445-8ebe-1d9c-e335596f2a34", "source": "data:image/png;base64,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", "relative_path": "../../Gojo/stiched_texture.png"}, {"path": "C:\\Users\\<USER>\\AppData\\Roaming\\PrismLauncher\\instances\\goober\\.minecraft\\figura\\avatars\\Gojo\\stiched_texture_e.png", "name": "stiched_texture_e.png", "folder": "", "namespace": "", "id": "4", "particle": false, "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "mode": "bitmap", "saved": false, "uuid": "0b3e61e1-f91e-edbb-de28-ce0990d39184", "source": "data:image/png;base64,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", "relative_path": "../../Gojo/stiched_texture_e.png"}], "animations": [{"uuid": "b55c323d-ba63-d721-2da1-0f45856c15da", "name": "purple", "loop": "once", "override": true, "length": 30, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"4a425455-781c-7bf1-309c-0892212ef029": {"name": "RightLeg", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0fef358d-e382-538c-c6a7-d317036cf654", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "-2", "z": 0}], "uuid": "b8d0221a-cc98-b226-0c4f-1ab0f734c0e1", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "-2", "z": 0}], "uuid": "f546caa4-70cf-e4f8-b58e-6ec9d99dfefd", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "9697fe02-6315-a703-5ee1-88bcb41adddc", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "627f20f3-1b81-f4c3-7529-a1517078ac00": {"name": "LeftLeg", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "4fd650d0-82e8-c06c-2d5c-ec930fddc14e", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "-2", "z": 0}], "uuid": "fc8c7c02-34c7-c263-2595-dde19ca77962", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "39863360-84c2-48d0-f7c2-013aec415148", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "-2", "z": "0"}], "uuid": "092de921-41c1-cb17-e263-447af9f056ae", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "ab79a035-1e98-5f41-8358-c2aea36e2214": {"name": "red", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "b4d2f9fb-c120-ae5b-6bf6-3dd5d2955cb9", "time": 9, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "10", "y": 0, "z": 0}], "uuid": "0bd60a53-3aee-7184-0b01-58583ff6a7e4", "time": 11, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-2.5560554475519996, -0.1, -0.1], "bezier_left_value": [-1.9006211180124224, 0, 0], "bezier_right_time": [2.5560554475519996, 0.1, 0.1], "bezier_right_value": [1.9006211180124224, 0, 0]}, {"channel": "position", "data_points": [{"x": "16", "y": 0, "z": 0}], "uuid": "bb34e55f-43fc-9d58-85db-4fa55335af18", "time": 14, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "46fb7d39-2f6d-e466-061b-2449be14af15", "time": 14.875, "color": -1, "uniform": true, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": 1, "y": 1, "z": 1}], "uuid": "9d61c39e-4edb-045f-2405-53b6eb3389a1", "time": 13.75, "color": -1, "uniform": true, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "e00f657d-**************-ee78e4c32529", "time": 7.708333333333333, "color": -1, "uniform": true, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "40a7583d-f0b3-30fe-964b-c15092e63f34", "time": 8.333333333333334, "color": -1, "uniform": true, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "1.1", "y": "1.1", "z": "1.1"}], "uuid": "2e4796bf-cb7f-442e-c473-ae385bfef549", "time": 8.083333333333334, "color": -1, "uniform": true, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "ebff5484-022e-adc8-f175-98420d5d779e": {"name": "blue", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "3433d0b7-573c-8a5e-6098-91e21407a461", "time": 9, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "-10", "y": 0, "z": 0}], "uuid": "e15766d9-3f82-1aae-936a-db3e3c9af71f", "time": 11, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-2.************, -0.1, -0.1], "bezier_left_value": [0.8975155279503106, 0, 0], "bezier_right_time": [2.************, 0.1, 0.1], "bezier_right_value": [-0.8975155279503106, 0, 0]}, {"channel": "position", "data_points": [{"x": "-16", "y": 0, "z": 0}], "uuid": "2ddb16e9-52f0-3a78-da11-4d5852276b60", "time": 14, "color": -1, "interpolation": "bezier", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": 1, "y": 1, "z": 1}], "uuid": "76d2c5e6-cfc6-bcf6-0e90-e3cb09258587", "time": 13.75, "color": -1, "uniform": true, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7163af56-46c4-0145-b1e2-dae01f1255e7", "time": 14.875, "color": -1, "uniform": true, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "9faf34d8-7098-e5e2-3de9-835ea7840165", "time": 4.875, "color": -1, "uniform": true, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "76aa2783-becb-3752-e528-f44cd3d3ccad", "time": 5.5, "color": -1, "uniform": true, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "1.1", "y": "1.1", "z": "1.1"}], "uuid": "507f9726-9c0e-49ef-b1b1-7d6e600cdbbf", "time": 5.25, "color": -1, "uniform": true, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "c64b214e-372f-3442-db55-c6869c472187": {"name": "purple", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "be12c8a6-14c7-3291-e858-dab39f85bc46", "time": 17, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": "2280"}], "uuid": "f794d6ad-4f4d-ce7c-a3fc-deb33b7e8a39", "time": 30, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 16, "z": -72}], "uuid": "af65424b-1bf1-ff27-f28a-8cd4dff5389c", "time": 19.625, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "4eb42568-19e9-1e61-c85f-8e4f6f03c5dd", "time": 16, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -6, "z": -10}], "uuid": "2bc0b368-d3b2-33f2-63cd-538b619b6bac", "time": 16.75, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 16, "z": -72}], "uuid": "9fa20705-be36-23e7-edb3-1fc1de1020fc", "time": 17, "color": -1, "interpolation": "step", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 16, "z": "-5000"}], "uuid": "81d1cfc3-fdff-45dc-3616-2fb18202abff", "time": 30, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "6000f665-f9ff-7655-485f-a41d6170d202", "time": 14.375, "color": -1, "uniform": true, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "663ac15a-d397-13ed-b57c-ef7a675f23e8", "time": 14.75, "color": -1, "uniform": true, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "de51f628-07b6-591d-7c1a-fd78c9feac7f", "time": 16.75, "color": -1, "uniform": true, "interpolation": "step", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "d71792b2-d9a8-1999-c28e-05a718a369e8", "time": 16, "color": -1, "uniform": true, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "f44c408d-16ef-e280-1c96-70a358b82e38", "time": 17, "color": -1, "uniform": true, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "scale", "data_points": [{"x": "5", "y": "5", "z": "5"}], "uuid": "a4fa64e8-ed21-4bb0-17a7-7013994a34fc", "time": 19.208333333333332, "color": -1, "uniform": true, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "c06cb0df-2f23-3573-91fe-e54fd0242131": {"name": "LeftForeArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "917647bd-ba46-ce6b-3888-aea6ad9404f1", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": "0"}], "uuid": "4c0518c2-d92c-88ae-de8c-1ef0f1bc04f3", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-120", "y": "0", "z": "-45"}], "uuid": "92ec90fb-914a-9103-8816-ac44b0140f56", "time": 0.9166666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-120", "y": "0", "z": "-45"}], "uuid": "33becf16-b8b6-99a7-c7e9-24410eb4d4ba", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "072feeea-bdbc-5b43-9b37-b45f6a1e64a3", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": "0"}], "uuid": "e58de25e-cc6a-bb6a-5dae-40c8d3487a11", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": -1, "y": -2, "z": 0}], "uuid": "155e7ca5-b349-5e44-e92a-97aba9327d0d", "time": 0.9166666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": -1, "y": -2, "z": 0}], "uuid": "d1f6c3cb-f4cf-e2cf-2b04-b1ae0d018985", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "4456008a-ff94-4184-466c-30b325f3adea": {"name": "RightForeArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "6b772559-c0a0-f173-4f89-6ee5676e6317", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": "0"}], "uuid": "090fcd30-9658-d7fc-e290-ad1f08335432", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-120", "y": "0", "z": "45"}], "uuid": "4dc16b41-64c8-1579-72bb-340cb345ca01", "time": 0.9166666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "-10"}], "uuid": "fb9a6392-dbc0-115e-6b71-f915f0ed4597", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-120", "y": "0", "z": "45"}], "uuid": "dd68e7fb-10f2-cbd3-4725-8033d1d8f9d0", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "-10"}], "uuid": "bb0250c8-1777-db3c-6582-6213354e4bec", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "705bef4a-217e-72bb-52a6-4fd0b67d21ab", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": "0"}], "uuid": "9e66e44b-bc74-9b3c-f66a-81ad72458b80", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "-1", "z": "-1"}], "uuid": "2ef8a563-cb0d-0fa5-e2f2-64af1e5c54fa", "time": 0.9166666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "b648fc24-ed68-028b-e33f-2006a5af3598", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "-1", "z": "-1"}], "uuid": "2476f40b-98e8-bcd0-c507-57f3eaf8cb6c", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "950ed33b-1bba-536c-e761-8a23c97616e3", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "5d5eb734-b7ba-4a67-7472-be58611e14b0": {"name": "ArmR", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -90, "y": 0, "z": -10}], "uuid": "fb105914-d352-e25a-b7e1-8d07e21fc993", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -90, "y": 0, "z": -10}], "uuid": "35ff947b-ad29-1c9b-d70b-e026075da08b", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "22135fed-0b13-f468-88f3-2584740e2ae3", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -45, "y": 0, "z": -10}], "uuid": "eea8f964-3bcd-81c3-9afb-c0937583a7ea", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "86c04d89-7169-b516-63ad-d5194998aa1c", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -45, "y": 0, "z": "10"}], "uuid": "41872568-c5c2-f585-0350-2284c4b86d2e", "time": 0.9166666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "-1", "z": "0"}], "uuid": "eca2b06d-3ab3-6121-e35b-78110ef60435", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "58d5c40c-0e1d-af19-7a7b-25ffb2b64445", "time": 17, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "23b3eee3-85aa-f44e-55f4-de91c566eff6", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "-1", "z": "0"}], "uuid": "e335f212-1d35-faae-fc61-993a6ef00647", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "09beb120-7c1f-417d-15eb-0b9fe41dbad0": {"name": "ArmL", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -45, "y": 0, "z": 10}], "uuid": "27826758-d775-db2a-33c4-b72c37c002c0", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "026731d0-48d3-7b23-2f2a-6ef2b230b4f9", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0de4bb45-da40-3a1a-dad2-21f71f0d5402", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -45, "y": 0, "z": "-10"}], "uuid": "e47bc7a8-a46d-a0ed-2fa9-28a32ced075b", "time": 0.9166666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "7fbee9a1-51cc-d70c-1f91-8873240ba5c1": {"name": "RightThigh", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "b1c687a3-87e2-aaaf-1dc7-a97a63164351", "time": 7.875, "color": -1, "interpolation": "step", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "ae9d8e81-add6-b9bf-f306-8f53d1a01047", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "2ebe95e4-182e-e008-426c-6702afc3244d", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-15", "y": "20", "z": "30"}], "uuid": "95997109-40f7-a266-d5d1-a612a9b95182", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-15", "y": "20", "z": "30"}], "uuid": "f88c4ce6-f7a6-7003-a01c-8e3a95391cae", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "148f405f-0acd-75e0-2542-c9904a60b0b8", "time": 7.875, "color": -1, "interpolation": "step", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": -1.5}], "uuid": "8e58c007-89f8-bbb6-fd8c-c64d3ffc8f68", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0916b97b-d52f-a430-78aa-cecaefa6a3cb", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "3c24aecb-02f3-e45a-5b5d-e2faf25d00b2", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": -1.5}], "uuid": "e5a15880-9a83-949e-becd-72dfc35b2138", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "925fd3d7-56af-f560-255d-4c3f14af59c0": {"name": "RightShin", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "42d5a2ea-8dca-9bdf-b2a7-d4fa5c5869b1", "time": 7.875, "color": -1, "interpolation": "step", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "5527808b-03b0-f670-94d5-dea29b4cb60b", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 5, "y": 0, "z": 20}], "uuid": "f1ffa518-86e9-7a4d-8e16-2d4423163ede", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7cd29be0-01f0-47a6-04b5-61df013c863e", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 5, "y": 0, "z": 20}], "uuid": "f9e159ec-21e1-b2df-4aa2-9dff57e00cdb", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "5076e45f-e6c1-af07-ccb4-3ac89b979b5b", "time": 7.875, "color": -1, "interpolation": "step", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "dd407862-f477-33eb-f782-253ec7438574", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": -4, "y": 1.25, "z": -3}], "uuid": "c46abe7c-33d3-f8d6-b2cb-6cc3233bc98c", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "5bbe49f6-2b4b-b724-5758-f20e0ab0eedd", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": -4, "y": 1.25, "z": -3}], "uuid": "f31a723a-3bd1-8efd-90c5-3ef4de8e53c6", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "16eab9d4-3d4a-2537-899b-56bb0fe531ca": {"name": "LeftThigh", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0e7e9192-818b-69d4-8f33-cbc25b28dd95", "time": 3, "color": -1, "interpolation": "step", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "3c0ae66e-bcfa-a8df-ac5c-b65abc866e8e", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-15", "y": "-20", "z": "-30"}], "uuid": "b8b20014-29e8-f5f7-1c1d-5fa093efee87", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "1d5e57e2-440d-2f58-8180-deec2f4b7888", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-15", "y": "-20", "z": "-30"}], "uuid": "f1f8b280-7d24-6dcb-dafa-7408bbf81719", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "a2a1c8ec-30c2-ad5a-5457-9952a2f2fa42", "time": 3, "color": -1, "interpolation": "step", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "d6f16906-0c2a-7b5d-0d7b-1f8308a762df", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": -1.5}], "uuid": "99bd4db8-d468-98fb-9357-34d315c22a4e", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "25f6dff7-d576-c3aa-3f48-16c74f034b24", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": -1.5}], "uuid": "11ec952c-e09d-e92d-1926-6a3b8deef405", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "f19b5ba5-8a75-d9a6-9837-3cb6745ab312": {"name": "LeftShin", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "4b637f56-1960-afd5-2374-3edd77e20459", "time": 3, "color": -1, "interpolation": "step", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "e45cd396-bb3a-08f3-f7c2-80b89ff69ec6", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "a5f14603-d176-3b6b-1eed-5383f796f3f5", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 5, "y": 0, "z": -20}], "uuid": "047bd03f-6de5-46ce-7f94-f5a3b5217df7", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 5, "y": 0, "z": -20}], "uuid": "334097fd-d7ff-dc30-524b-bbf405665640", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "27f96e60-d9cd-1a9f-6459-b171e1c5df2c", "time": 3, "color": -1, "interpolation": "step", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9a70287-fa93-4ff7-50b4-f7b560e03fca", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "c2fdd942-b8c2-fd16-eab7-027f57e3ef18", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 4, "y": 1.25, "z": -3}], "uuid": "2da2effe-ff21-8d32-0da9-8994de8169c4", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 4, "y": 1.25, "z": -3}], "uuid": "9d0bae4a-5da8-b2d5-4333-e1c3215bb2b8", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "3f40c689-921d-3dae-e7c8-477d94e2bba6": {"name": "Upper", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "da264d51-2a98-37ea-efa3-e80faaea8d1d", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -2.5, "y": 0, "z": 0}], "uuid": "45c067e7-26d3-2d27-b170-9371549fa70c", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "c45f8ee8-f989-10c3-8ec2-55164a40ab96", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -5, "y": 0, "z": 0}], "uuid": "ab2eea3b-627a-8300-cdeb-131095c2b7dd", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "fad83894-a0a4-8e2e-666f-ed3344ec00b0", "time": 17, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -3, "z": -2}], "uuid": "1c8c95b3-40ae-9dca-c1fa-6d7d74eec4d3", "time": 18, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "a1ce10d5-372d-90b2-ddec-e0477e248f2a", "time": 21.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -3, "z": -2}], "uuid": "3b3b62b7-78ae-2273-3f89-88abb3b9260e", "time": 20.625, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "ce3399e2-f4e2-2b14-3731-1e86aae19b7c": {"name": "Adult", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "e62734c0-158e-91a0-a616-77adb99a8dfb", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "c7526391-5b69-23cf-1db3-88a83a25dc80", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "42010b6c-4a17-70a1-d68c-3280f4f9d4db": {"name": "Body", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "87386376-cf5f-2be9-796e-778538af2fc5", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "fe6df5f9-6c1d-248a-1d4f-e45c37c2cf3a", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "2c1299be-a255-b6b9-2896-c54d54ed6afc": {"name": "RightArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "3c2708c5-6d64-4170-6efa-c1e3a6d289b2", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "5f133426-b659-0ffb-f245-c3771744ab21", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "41adb89d-5f91-8a98-dd08-32bc7838ee78": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "428fc9e2-09c2-466e-99ec-c4a0c13cb1eb", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "6d86197a-7a3c-4256-421c-901ea6d54969", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "63d4216b-0f6f-31cd-1b0a-a4b10cd7fbb1": {"name": "LeftArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "8954a3b3-53d4-6453-7afb-fb496554342b", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "637a01c4-b561-cdf3-acfc-fbd5740bebd0", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "9b638422-9d9a-e9b6-28bf-85b1b2b51cef": {"name": "armaniml", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "34288e28-c0c0-3f7a-a5e1-c6535d444acd", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "85aa74b6-5747-46ef-6f20-459814bcd027", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "e196d4e0-6b78-c3b8-56cc-4a62229e97c8": {"name": "orb", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7618b3fe-af2c-7fdd-10a5-f36a220c1367", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "8ac8fa82-4fec-a76c-8d77-5a5ecdef64cd", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}}}, {"uuid": "400caedd-495b-6939-3e13-fa6257d7e7e6", "name": "void", "loop": "once", "override": false, "length": 3, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}, {"uuid": "ccf84a8c-ec11-3f59-2519-fe90c27efb57", "name": "sprint", "loop": "loop", "override": true, "length": 0.5, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"3f40c689-921d-3dae-e7c8-477d94e2bba6": {"name": "Upper", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": 0, "z": 0}], "uuid": "2635e5d2-a878-82df-1580-07267d2f9118", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "40", "y": 0, "z": 0}], "uuid": "e30bffa6-4868-a3ef-bf6a-91b51a561dbc", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": 0, "z": 0}], "uuid": "6d0e0e00-bb19-d1d0-df4f-3bce25abb2a8", "time": 0.08333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": 0, "z": 0}], "uuid": "d6c20f44-239d-855a-2a17-3d9b4e628011", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "40", "y": 0, "z": 0}], "uuid": "*************-8560-adb4-8a150a02f744", "time": 0.4166666666666667, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": 0, "z": 0}], "uuid": "816bde59-37fb-87d7-7be7-2907bf81fe2f", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": 0, "z": 0}], "uuid": "dd8e4cc8-2826-c1e9-1cb6-c699fd4dad86", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "3ba5cdbe-c7f2-4833-4435-6c666c2d735b", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "a865e109-9abf-c1e6-893b-57903fa892f2", "time": 0.16666666666666666, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "0c8ea5ef-ec55-b5f8-99e4-c7b5b5396816", "time": 0.08333333333333333, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "42fbf08f-ef5d-ce47-8b7a-af16adfc7eed", "time": 0.25, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "3a9d7bb7-f936-82e1-2810-ddd3e1d23d7a", "time": 0.4166666666666667, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "b17093ac-83a8-9d4a-5f6a-80e89979c554", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "6003e602-950d-5ec8-0f42-48703e7694be", "time": 0.5, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "4456008a-ff94-4184-466c-30b325f3adea": {"name": "RightForeArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "7dec86a4-6267-ce70-f8c0-925e1b978468", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "d2b3a0b7-f59e-d3aa-f56a-3ff4cee90af1", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "088c2a05-3052-fd0f-10db-86e5209ba835", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "c95e5358-4fc0-64ad-466f-768e6431c7bf", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "c06cb0df-2f23-3573-91fe-e54fd0242131": {"name": "LeftForeArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "93b50087-d2d5-98f8-248d-0a6d90e298ab", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "11987d55-fa0a-6d86-9c83-8a9c402ebd6d", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "b428c445-9ee7-d268-c931-68c1e58fff3d", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "f3a66249-67a8-d429-be06-f631d8e38fcd", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "7fbee9a1-51cc-d70c-1f91-8873240ba5c1": {"name": "RightThigh", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "c4144ff5-e818-0962-5113-6c329b98b0b0", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "67dbeac4-e175-3d65-d780-712bd7dce137", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "cbe6a2aa-839a-78ed-0c8c-39fc68455517", "time": 0.08333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-55", "y": "0", "z": "0"}], "uuid": "9faeebe5-bc05-a59c-3f3e-eead512e26f6", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -45, "y": 0, "z": 0}], "uuid": "a7550882-49ca-632e-3e63-a5dfe0a84532", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "773f454d-1f38-fc37-886e-e23527bba673", "time": 0.4166666666666667, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "51c3bcf8-0ddf-4f44-d875-ee362a1470b7", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "8c35d400-3809-7aa5-e020-283e8d413944", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -1, "z": 0}], "uuid": "8d92e9b7-b4fd-eff3-9d75-bc9bb67dee30", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "7464fa00-8094-2d6d-9867-1dc836a205dd", "time": 0.08333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "eee613c4-3c5b-fc79-d0b6-ff1178b1702b", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "9ed9a8f5-6de1-202c-4b70-facdc2295abf", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -1, "z": 0}], "uuid": "366fd89f-f6db-195b-0ed6-68205de7688c", "time": 0.4166666666666667, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "a9bb3146-0906-903a-68a9-1163f8fc9868", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "925fd3d7-56af-f560-255d-4c3f14af59c0": {"name": "RightShin", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "8406d68b-f88d-440e-fb8e-a6dec6c26280", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "9375366c-92e7-d43e-7f0d-fd19e5782f1e", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "0efe5989-dac0-cc3a-1942-8cf4596c4127", "time": 0.08333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -12.5, "y": "0", "z": "0"}], "uuid": "d99ff4e0-ea63-3fab-46ff-d8a05954a91d", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -2.5, "y": 0, "z": 0}], "uuid": "9bf483f4-72ed-5895-4266-820b429520a7", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "90e9e16a-f417-c0f9-189c-862d1cd68c9c", "time": 0.4166666666666667, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "c797d4a3-f6b7-b24c-2aee-71ab4c2c1b43", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 3}], "uuid": "63c01798-448d-b59f-ae47-5eb6537c787c", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "32b9aab0-10c8-0842-1eab-e6b388c7c5e3", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 3}], "uuid": "611ed447-5784-4c68-4bbf-d776e7323ecb", "time": 0.08333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 2, "z": -7}], "uuid": "307c40d8-7b56-7e6c-74da-bc6e95f14339", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 1, "z": -7}], "uuid": "6a978dbc-1a54-fd35-cc27-449e01abad43", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "ddda1795-20b9-8bbd-d729-f30b45ae53a0", "time": 0.4166666666666667, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 3}], "uuid": "3819e9f3-fcab-5d62-7365-8789551aadde", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "16eab9d4-3d4a-2537-899b-56bb0fe531ca": {"name": "LeftThigh", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -55, "y": "0", "z": "0"}], "uuid": "efb86f60-7321-53fa-a0a0-49f7cbdb6c3b", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "052b0339-db37-ed7f-2240-c274f960e110", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -47.5, "y": "0", "z": "0"}], "uuid": "b7051711-de5b-283c-58a2-29c46135ae93", "time": 0.08333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "8d41a09f-2aea-7dd9-4c6e-4e16cd58645b", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "ff85fc37-9bee-0b18-d60e-f6846f466f10", "time": 0.4166666666666667, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -55, "y": "0", "z": "0"}], "uuid": "74cdbe6e-91c1-f7d5-49e6-36ae74ef8ab5", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "46e45b68-2155-0f0f-6af4-28bdc5359eee", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "465bcc62-cd80-1008-f8a7-21ee6804958d", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "26d29d7d-f893-9982-fc9c-a940ef02c651", "time": 0.08333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "6b187ece-aa83-098f-16bb-603603f9aef1", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "35355d0c-4fbd-4ad2-3c79-187a22ee99a8", "time": 0.4166666666666667, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "fe6a16e1-56b3-950d-34a6-a1ec9aede488", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "f19b5ba5-8a75-d9a6-9837-3cb6745ab312": {"name": "LeftShin", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -10, "y": "0", "z": "0"}], "uuid": "ab53e9c8-51d8-bd4d-fc36-acd89c7c7078", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "94b75ae5-6796-24b2-4d5e-2c804b6b9d66", "time": 0.08333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "228f4f61-1f5e-7e1d-adb7-de3ac0732d24", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "98f9a8a7-edde-1f21-52cb-f2249bef6fd4", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "4281f403-8e2d-ac0c-f281-23d21c8c774e", "time": 0.4166666666666667, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -10, "y": "0", "z": "0"}], "uuid": "eb1c4527-a8df-f397-7638-5ceb9cf52de9", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 2, "z": -7}], "uuid": "b62b8b40-697e-57b3-f731-f2118ebe93b3", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": -7}], "uuid": "23e1c0c8-6d43-9e97-795e-54bec97eb50e", "time": 0.08333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 3}], "uuid": "e3fff174-f829-30c0-1aa1-2df4b4ff7972", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": "0"}], "uuid": "db09ceef-c658-69e1-959a-c180f089d632", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": "0"}], "uuid": "8a0b0212-eec8-e016-aeac-49c84df57abb", "time": 0.4166666666666667, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 2, "z": -7}], "uuid": "fdd68154-96ec-cb2d-40fc-3bffae6f2583", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "31c3cd66-9bcd-13f9-0015-be821dce69ce": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "2427e6a6-8310-ab03-5e2f-7a36bf7b0a4c", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "81594361-c43d-d69e-fa3f-b82818d8ad77", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "17a32b2b-61a2-5ca8-e2e7-f7c1685476f5", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "bed2a706-fbcb-0074-238b-c096a15c8cdd", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "95fe918e-9ea9-5722-79e0-aa2d9a95fbc1": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "37e583ca-bf59-ffee-d752-c6f98f9027ba", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "6da9ff74-f296-322a-9bdf-4d9610a8e930", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "b020db5a-7323-e497-4578-97d37fb10156", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "df084af9-363b-c4eb-5009-24d53d6fb94e", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "2c1299be-a255-b6b9-2896-c54d54ed6afc": {"name": "RightArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "e20a6245-6fc0-a37e-055d-b8dd72032b90", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "c5383daa-7df9-8c38-79c2-0d7345a845de", "time": 0.5, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "43a5b687-36e0-1a68-5de8-1e4771607633", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "df725f2b-a96d-40b9-124f-9864af26785f", "time": 0.5, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "63d4216b-0f6f-31cd-1b0a-a4b10cd7fbb1": {"name": "LeftArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7109a5cb-9bea-46e6-6290-78e2ee1df3d3", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "d7f0e963-706e-7d68-b085-eb2a2a43eccb", "time": 0.5, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "65034631-c4ba-faaf-287e-70a6fc5fc011", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "4e18e2c5-dca1-fd6d-3192-32d23d37acfe", "time": 0.5, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "4a425455-781c-7bf1-309c-0892212ef029": {"name": "RightLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "5ede2e7b-dfa1-6d9d-f432-d48006b9a65e", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "12123f67-cd09-aa78-67cd-3968a3040321", "time": 0.08333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 2.5, "y": 0, "z": 0}], "uuid": "471bcb09-7861-b13b-218a-5e2c5c94865f", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "cad6694a-950a-d790-5dee-3b3eb9896b84", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0a500c6e-4283-c3a2-6baf-367fa454f43e", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "32ede07c-ed3e-2b5a-76db-01028393ba23", "time": 0.08333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -1, "z": 0}], "uuid": "bb0de6f0-937e-dc79-bfb3-ab3911f22581", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "ec68ee5d-0dc5-a3de-8c09-31b0652313f9", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "627f20f3-1b81-f4c3-7529-a1517078ac00": {"name": "LeftLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 2.5, "y": "0", "z": "0"}], "uuid": "3869a830-b341-16fb-0745-dd3240c5cd4c", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 2.5, "y": "0", "z": "0"}], "uuid": "30488505-a0b8-f1f9-7d73-40eda962a644", "time": 0.08333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "4d4dcc9e-96bb-645d-455c-979c6b9c8dfb", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 2.5, "y": "0", "z": "0"}], "uuid": "5cba1493-25d3-0d45-3ea0-041e4ff5a606", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "fce31a7c-1296-09c4-bf2d-136cd9a00af6", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "aa0e7baf-65c3-ab90-d7f2-c791b95aac4b", "time": 0.08333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "f6cf4bf0-7174-3293-b7d1-e73c0fa8fb26", "time": 0.25, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "36e25d9c-284b-039a-3054-cb183385f136", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "aacded47-fe3a-9184-6a1e-a9b169feeb8c": {"name": "Head", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "ee162e1b-2ab6-880c-49ae-f47290c52881", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "42010b6c-4a17-70a1-d68c-3280f4f9d4db": {"name": "Body", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "90f86162-16be-e59a-1908-968f0bef5387", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "6bbfe020-5697-aaf4-4a0a-2cb8a584fce5", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}}}, {"uuid": "44f3108c-287e-e13a-257d-a8ace3037d00", "name": "walk", "loop": "loop", "override": true, "length": 0.6666666666666666, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"3f40c689-921d-3dae-e7c8-477d94e2bba6": {"name": "Upper", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "6d0e0e00-bb19-d1d0-df4f-3bce25abb2a8", "time": 0.16666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-10", "y": 0, "z": 0}], "uuid": "75caca0c-62a2-7f70-e00e-cf30b76aae2e", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-10", "y": 0, "z": 0}], "uuid": "b764b433-2041-2943-32aa-dbbee180a4cf", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "361ab165-7665-bf9e-76cf-86f0d8971b70", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-10", "y": 0, "z": 0}], "uuid": "dbbfdf2b-d5ab-bdbd-5110-520e7a3efee4", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "3ba5cdbe-c7f2-4833-4435-6c666c2d735b", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "a865e109-9abf-c1e6-893b-57903fa892f2", "time": 0.20833333333333334, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "0c8ea5ef-ec55-b5f8-99e4-c7b5b5396816", "time": 0.125, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "42fbf08f-ef5d-ce47-8b7a-af16adfc7eed", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "3a9d7bb7-f936-82e1-2810-ddd3e1d23d7a", "time": 0.5416666666666666, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "b17093ac-83a8-9d4a-5f6a-80e89979c554", "time": 0.4583333333333333, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "6003e602-950d-5ec8-0f42-48703e7694be", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "4456008a-ff94-4184-466c-30b325f3adea": {"name": "RightForeArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "7dec86a4-6267-ce70-f8c0-925e1b978468", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "d2b3a0b7-f59e-d3aa-f56a-3ff4cee90af1", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "088c2a05-3052-fd0f-10db-86e5209ba835", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "c95e5358-4fc0-64ad-466f-768e6431c7bf", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "c06cb0df-2f23-3573-91fe-e54fd0242131": {"name": "LeftForeArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "93b50087-d2d5-98f8-248d-0a6d90e298ab", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "11987d55-fa0a-6d86-9c83-8a9c402ebd6d", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "b428c445-9ee7-d268-c931-68c1e58fff3d", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "f3a66249-67a8-d429-be06-f631d8e38fcd", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "7fbee9a1-51cc-d70c-1f91-8873240ba5c1": {"name": "RightThigh", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "c4144ff5-e818-0962-5113-6c329b98b0b0", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "67dbeac4-e175-3d65-d780-712bd7dce137", "time": 0.20833333333333334, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "cbe6a2aa-839a-78ed-0c8c-39fc68455517", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-55", "y": "0", "z": "0"}], "uuid": "9faeebe5-bc05-a59c-3f3e-eead512e26f6", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -45, "y": 0, "z": 0}], "uuid": "a7550882-49ca-632e-3e63-a5dfe0a84532", "time": 0.4583333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "773f454d-1f38-fc37-886e-e23527bba673", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "51c3bcf8-0ddf-4f44-d875-ee362a1470b7", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "8c35d400-3809-7aa5-e020-283e8d413944", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -1, "z": 0}], "uuid": "8d92e9b7-b4fd-eff3-9d75-bc9bb67dee30", "time": 0.20833333333333334, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "7464fa00-8094-2d6d-9867-1dc836a205dd", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "eee613c4-3c5b-fc79-d0b6-ff1178b1702b", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "9ed9a8f5-6de1-202c-4b70-facdc2295abf", "time": 0.4583333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -1, "z": 0}], "uuid": "366fd89f-f6db-195b-0ed6-68205de7688c", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "a9bb3146-0906-903a-68a9-1163f8fc9868", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "925fd3d7-56af-f560-255d-4c3f14af59c0": {"name": "RightShin", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "8406d68b-f88d-440e-fb8e-a6dec6c26280", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "9375366c-92e7-d43e-7f0d-fd19e5782f1e", "time": 0.20833333333333334, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "0efe5989-dac0-cc3a-1942-8cf4596c4127", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -12.5, "y": "0", "z": "0"}], "uuid": "d99ff4e0-ea63-3fab-46ff-d8a05954a91d", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -2.5, "y": 0, "z": 0}], "uuid": "9bf483f4-72ed-5895-4266-820b429520a7", "time": 0.4583333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "90e9e16a-f417-c0f9-189c-862d1cd68c9c", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "c797d4a3-f6b7-b24c-2aee-71ab4c2c1b43", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 3}], "uuid": "63c01798-448d-b59f-ae47-5eb6537c787c", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "32b9aab0-10c8-0842-1eab-e6b388c7c5e3", "time": 0.20833333333333334, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 3}], "uuid": "611ed447-5784-4c68-4bbf-d776e7323ecb", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 2, "z": -7}], "uuid": "307c40d8-7b56-7e6c-74da-bc6e95f14339", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 1, "z": -7}], "uuid": "6a978dbc-1a54-fd35-cc27-449e01abad43", "time": 0.4583333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "ddda1795-20b9-8bbd-d729-f30b45ae53a0", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 3}], "uuid": "3819e9f3-fcab-5d62-7365-8789551aadde", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "16eab9d4-3d4a-2537-899b-56bb0fe531ca": {"name": "LeftThigh", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -55, "y": "0", "z": "0"}], "uuid": "efb86f60-7321-53fa-a0a0-49f7cbdb6c3b", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "052b0339-db37-ed7f-2240-c274f960e110", "time": 0.20833333333333334, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -47.5, "y": "0", "z": "0"}], "uuid": "b7051711-de5b-283c-58a2-29c46135ae93", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "8d41a09f-2aea-7dd9-4c6e-4e16cd58645b", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "ff85fc37-9bee-0b18-d60e-f6846f466f10", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -55, "y": "0", "z": "0"}], "uuid": "74cdbe6e-91c1-f7d5-49e6-36ae74ef8ab5", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "46e45b68-2155-0f0f-6af4-28bdc5359eee", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "465bcc62-cd80-1008-f8a7-21ee6804958d", "time": 0.20833333333333334, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "26d29d7d-f893-9982-fc9c-a940ef02c651", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "6b187ece-aa83-098f-16bb-603603f9aef1", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "35355d0c-4fbd-4ad2-3c79-187a22ee99a8", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "fe6a16e1-56b3-950d-34a6-a1ec9aede488", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "f19b5ba5-8a75-d9a6-9837-3cb6745ab312": {"name": "LeftShin", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -10, "y": "0", "z": "0"}], "uuid": "ab53e9c8-51d8-bd4d-fc36-acd89c7c7078", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "94b75ae5-6796-24b2-4d5e-2c804b6b9d66", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "228f4f61-1f5e-7e1d-adb7-de3ac0732d24", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "98f9a8a7-edde-1f21-52cb-f2249bef6fd4", "time": 0.20833333333333334, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "4281f403-8e2d-ac0c-f281-23d21c8c774e", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -10, "y": "0", "z": "0"}], "uuid": "eb1c4527-a8df-f397-7638-5ceb9cf52de9", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 2, "z": -7}], "uuid": "b62b8b40-697e-57b3-f731-f2118ebe93b3", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": -7}], "uuid": "23e1c0c8-6d43-9e97-795e-54bec97eb50e", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 3}], "uuid": "e3fff174-f829-30c0-1aa1-2df4b4ff7972", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": "0"}], "uuid": "db09ceef-c658-69e1-959a-c180f089d632", "time": 0.20833333333333334, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": "0"}], "uuid": "8a0b0212-eec8-e016-aeac-49c84df57abb", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 2, "z": -7}], "uuid": "fdd68154-96ec-cb2d-40fc-3bffae6f2583", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "31c3cd66-9bcd-13f9-0015-be821dce69ce": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "2427e6a6-8310-ab03-5e2f-7a36bf7b0a4c", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "81594361-c43d-d69e-fa3f-b82818d8ad77", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "17a32b2b-61a2-5ca8-e2e7-f7c1685476f5", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "bed2a706-fbcb-0074-238b-c096a15c8cdd", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "95fe918e-9ea9-5722-79e0-aa2d9a95fbc1": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "37e583ca-bf59-ffee-d752-c6f98f9027ba", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "6da9ff74-f296-322a-9bdf-4d9610a8e930", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "b020db5a-7323-e497-4578-97d37fb10156", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "df084af9-363b-c4eb-5009-24d53d6fb94e", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "2c1299be-a255-b6b9-2896-c54d54ed6afc": {"name": "RightArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "e20a6245-6fc0-a37e-055d-b8dd72032b90", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "c5383daa-7df9-8c38-79c2-0d7345a845de", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "43a5b687-36e0-1a68-5de8-1e4771607633", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "df725f2b-a96d-40b9-124f-9864af26785f", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "63d4216b-0f6f-31cd-1b0a-a4b10cd7fbb1": {"name": "LeftArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7109a5cb-9bea-46e6-6290-78e2ee1df3d3", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "d7f0e963-706e-7d68-b085-eb2a2a43eccb", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "65034631-c4ba-faaf-287e-70a6fc5fc011", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "4e18e2c5-dca1-fd6d-3192-32d23d37acfe", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "4a425455-781c-7bf1-309c-0892212ef029": {"name": "RightLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "5ede2e7b-dfa1-6d9d-f432-d48006b9a65e", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "12123f67-cd09-aa78-67cd-3968a3040321", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 2.5, "y": 0, "z": 0}], "uuid": "471bcb09-7861-b13b-218a-5e2c5c94865f", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "cad6694a-950a-d790-5dee-3b3eb9896b84", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0a500c6e-4283-c3a2-6baf-367fa454f43e", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "32ede07c-ed3e-2b5a-76db-01028393ba23", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -1, "z": 0}], "uuid": "bb0de6f0-937e-dc79-bfb3-ab3911f22581", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "ec68ee5d-0dc5-a3de-8c09-31b0652313f9", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "627f20f3-1b81-f4c3-7529-a1517078ac00": {"name": "LeftLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 2.5, "y": "0", "z": "0"}], "uuid": "3869a830-b341-16fb-0745-dd3240c5cd4c", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 2.5, "y": "0", "z": "0"}], "uuid": "30488505-a0b8-f1f9-7d73-40eda962a644", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "4d4dcc9e-96bb-645d-455c-979c6b9c8dfb", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 2.5, "y": "0", "z": "0"}], "uuid": "5cba1493-25d3-0d45-3ea0-041e4ff5a606", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "fce31a7c-1296-09c4-bf2d-136cd9a00af6", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "aa0e7baf-65c3-ab90-d7f2-c791b95aac4b", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "f6cf4bf0-7174-3293-b7d1-e73c0fa8fb26", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "36e25d9c-284b-039a-3054-cb183385f136", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "42010b6c-4a17-70a1-d68c-3280f4f9d4db": {"name": "Body", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "6bfd92f1-3890-a6cf-8034-9c104ae56a0e", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "aecb8419-e5db-44c9-3706-059244f7e34b", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}}}, {"uuid": "267646b4-3574-b6dd-81bb-5549a36b2205", "name": "idle", "loop": "loop", "override": false, "length": 8, "snapping": 24, "selected": true, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"3f40c689-921d-3dae-e7c8-477d94e2bba6": {"name": "Upper", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -10, "y": 0, "z": "-2.5"}], "uuid": "75caca0c-62a2-7f70-e00e-cf30b76aae2e", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-5", "y": 0, "z": "2.5"}], "uuid": "f4d9bb3d-c0ed-9d68-53d4-bf1745d78299", "time": 4, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -10, "y": 0, "z": "-2.5"}], "uuid": "fea157ca-b23b-5e18-76b8-9b316f1b80d4", "time": 8, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "3ba5cdbe-c7f2-4833-4435-6c666c2d735b", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "4456008a-ff94-4184-466c-30b325f3adea": {"name": "RightForeArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "7dec86a4-6267-ce70-f8c0-925e1b978468", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "088c2a05-3052-fd0f-10db-86e5209ba835", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "c06cb0df-2f23-3573-91fe-e54fd0242131": {"name": "LeftForeArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "93b50087-d2d5-98f8-248d-0a6d90e298ab", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "b428c445-9ee7-d268-c931-68c1e58fff3d", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "31c3cd66-9bcd-13f9-0015-be821dce69ce": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "2427e6a6-8310-ab03-5e2f-7a36bf7b0a4c", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "17a32b2b-61a2-5ca8-e2e7-f7c1685476f5", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "95fe918e-9ea9-5722-79e0-aa2d9a95fbc1": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "37e583ca-bf59-ffee-d752-c6f98f9027ba", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "b020db5a-7323-e497-4578-97d37fb10156", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "2c1299be-a255-b6b9-2896-c54d54ed6afc": {"name": "RightArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "e20a6245-6fc0-a37e-055d-b8dd72032b90", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "43a5b687-36e0-1a68-5de8-1e4771607633", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "63d4216b-0f6f-31cd-1b0a-a4b10cd7fbb1": {"name": "LeftArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7109a5cb-9bea-46e6-6290-78e2ee1df3d3", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "65034631-c4ba-faaf-287e-70a6fc5fc011", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "4a425455-781c-7bf1-309c-0892212ef029": {"name": "RightLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "5ede2e7b-dfa1-6d9d-f432-d48006b9a65e", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0a500c6e-4283-c3a2-6baf-367fa454f43e", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "627f20f3-1b81-f4c3-7529-a1517078ac00": {"name": "LeftLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 2.5, "y": "0", "z": "0"}], "uuid": "3869a830-b341-16fb-0745-dd3240c5cd4c", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "fce31a7c-1296-09c4-bf2d-136cd9a00af6", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "42010b6c-4a17-70a1-d68c-3280f4f9d4db": {"name": "Body", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "6bfd92f1-3890-a6cf-8034-9c104ae56a0e", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "aecb8419-e5db-44c9-3706-059244f7e34b", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}}}, {"uuid": "9f83ec00-5256-8ea2-e9ad-b4c019fb54f2", "name": "fall", "loop": "loop", "override": true, "length": 1.3333333333333333, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"4456008a-ff94-4184-466c-30b325f3adea": {"name": "RightForeArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "7dec86a4-6267-ce70-f8c0-925e1b978468", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "088c2a05-3052-fd0f-10db-86e5209ba835", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "c06cb0df-2f23-3573-91fe-e54fd0242131": {"name": "LeftForeArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "93b50087-d2d5-98f8-248d-0a6d90e298ab", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "b428c445-9ee7-d268-c931-68c1e58fff3d", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "31c3cd66-9bcd-13f9-0015-be821dce69ce": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "2427e6a6-8310-ab03-5e2f-7a36bf7b0a4c", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "17a32b2b-61a2-5ca8-e2e7-f7c1685476f5", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "95fe918e-9ea9-5722-79e0-aa2d9a95fbc1": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "37e583ca-bf59-ffee-d752-c6f98f9027ba", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "b020db5a-7323-e497-4578-97d37fb10156", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "aacded47-fe3a-9184-6a1e-a9b169feeb8c": {"name": "Head", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "7124bd02-bfe6-c066-6fc9-8f2085fc820d", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "16eab9d4-3d4a-2537-899b-56bb0fe531ca": {"name": "LeftThigh", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -15, "y": "0", "z": "0"}], "uuid": "98a71a2b-b0e9-e090-b904-4216b261a07b", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "f19b5ba5-8a75-d9a6-9837-3cb6745ab312": {"name": "LeftShin", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 22.5, "y": "0", "z": "0"}], "uuid": "cb025679-d8b2-451c-39fc-27a8ea406791", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": -1}], "uuid": "d5b5f30d-4f08-4fff-7097-f8d0a6b7ec3c", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}}}, {"uuid": "e1361828-8c00-f450-e76e-2db6c4d230b5", "name": "walkback", "loop": "loop", "override": true, "length": 0.6666666666666666, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"3f40c689-921d-3dae-e7c8-477d94e2bba6": {"name": "Upper", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "6d0e0e00-bb19-d1d0-df4f-3bce25abb2a8", "time": 0.5, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-10", "y": 0, "z": 0}], "uuid": "75caca0c-62a2-7f70-e00e-cf30b76aae2e", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "361ab165-7665-bf9e-76cf-86f0d8971b70", "time": 0.16666666666666663, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-10", "y": 0, "z": 0}], "uuid": "5357c776-c810-a4d8-5cda-************", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-10", "y": 0, "z": 0}], "uuid": "d82a33b4-8e5b-ef20-3faa-c9c15212919d", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "3ba5cdbe-c7f2-4833-4435-6c666c2d735b", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "a865e109-9abf-c1e6-893b-57903fa892f2", "time": 0.45833333333333326, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "0c8ea5ef-ec55-b5f8-99e4-c7b5b5396816", "time": 0.5416666666666666, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "42fbf08f-ef5d-ce47-8b7a-af16adfc7eed", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "3a9d7bb7-f936-82e1-2810-ddd3e1d23d7a", "time": 0.125, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "b17093ac-83a8-9d4a-5f6a-80e89979c554", "time": 0.20833333333333331, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "6003e602-950d-5ec8-0f42-48703e7694be", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "4456008a-ff94-4184-466c-30b325f3adea": {"name": "RightForeArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "7dec86a4-6267-ce70-f8c0-925e1b978468", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "d2b3a0b7-f59e-d3aa-f56a-3ff4cee90af1", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "088c2a05-3052-fd0f-10db-86e5209ba835", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "c95e5358-4fc0-64ad-466f-768e6431c7bf", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "c06cb0df-2f23-3573-91fe-e54fd0242131": {"name": "LeftForeArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "93b50087-d2d5-98f8-248d-0a6d90e298ab", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -45, "y": "0", "z": "0"}], "uuid": "11987d55-fa0a-6d86-9c83-8a9c402ebd6d", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "b428c445-9ee7-d268-c931-68c1e58fff3d", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": 5}], "uuid": "f3a66249-67a8-d429-be06-f631d8e38fcd", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "7fbee9a1-51cc-d70c-1f91-8873240ba5c1": {"name": "RightThigh", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "c4144ff5-e818-0962-5113-6c329b98b0b0", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "67dbeac4-e175-3d65-d780-712bd7dce137", "time": 0.45833333333333326, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "cbe6a2aa-839a-78ed-0c8c-39fc68455517", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "-55", "y": "0", "z": "0"}], "uuid": "9faeebe5-bc05-a59c-3f3e-eead512e26f6", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -45, "y": 0, "z": 0}], "uuid": "a7550882-49ca-632e-3e63-a5dfe0a84532", "time": 0.20833333333333331, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "773f454d-1f38-fc37-886e-e23527bba673", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "51c3bcf8-0ddf-4f44-d875-ee362a1470b7", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "8c35d400-3809-7aa5-e020-283e8d413944", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -1, "z": 0}], "uuid": "8d92e9b7-b4fd-eff3-9d75-bc9bb67dee30", "time": 0.45833333333333326, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "7464fa00-8094-2d6d-9867-1dc836a205dd", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "eee613c4-3c5b-fc79-d0b6-ff1178b1702b", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "9ed9a8f5-6de1-202c-4b70-facdc2295abf", "time": 0.20833333333333331, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -1, "z": 0}], "uuid": "366fd89f-f6db-195b-0ed6-68205de7688c", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "a9bb3146-0906-903a-68a9-1163f8fc9868", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "925fd3d7-56af-f560-255d-4c3f14af59c0": {"name": "RightShin", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "8406d68b-f88d-440e-fb8e-a6dec6c26280", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "9375366c-92e7-d43e-7f0d-fd19e5782f1e", "time": 0.45833333333333326, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "0efe5989-dac0-cc3a-1942-8cf4596c4127", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -12.5, "y": "0", "z": "0"}], "uuid": "d99ff4e0-ea63-3fab-46ff-d8a05954a91d", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -2.5, "y": 0, "z": 0}], "uuid": "9bf483f4-72ed-5895-4266-820b429520a7", "time": 0.20833333333333331, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "90e9e16a-f417-c0f9-189c-862d1cd68c9c", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "c797d4a3-f6b7-b24c-2aee-71ab4c2c1b43", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 3}], "uuid": "63c01798-448d-b59f-ae47-5eb6537c787c", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "32b9aab0-10c8-0842-1eab-e6b388c7c5e3", "time": 0.45833333333333326, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 3}], "uuid": "611ed447-5784-4c68-4bbf-d776e7323ecb", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 2, "z": -7}], "uuid": "307c40d8-7b56-7e6c-74da-bc6e95f14339", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 1, "z": -7}], "uuid": "6a978dbc-1a54-fd35-cc27-449e01abad43", "time": 0.20833333333333331, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "ddda1795-20b9-8bbd-d729-f30b45ae53a0", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 3}], "uuid": "3819e9f3-fcab-5d62-7365-8789551aadde", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "16eab9d4-3d4a-2537-899b-56bb0fe531ca": {"name": "LeftThigh", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -55, "y": "0", "z": "0"}], "uuid": "efb86f60-7321-53fa-a0a0-49f7cbdb6c3b", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "052b0339-db37-ed7f-2240-c274f960e110", "time": 0.45833333333333326, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -47.5, "y": "0", "z": "0"}], "uuid": "b7051711-de5b-283c-58a2-29c46135ae93", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 25, "y": "0", "z": "0"}], "uuid": "8d41a09f-2aea-7dd9-4c6e-4e16cd58645b", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "ff85fc37-9bee-0b18-d60e-f6846f466f10", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -55, "y": "0", "z": "0"}], "uuid": "74cdbe6e-91c1-f7d5-49e6-36ae74ef8ab5", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "46e45b68-2155-0f0f-6af4-28bdc5359eee", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "465bcc62-cd80-1008-f8a7-21ee6804958d", "time": 0.45833333333333326, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "26d29d7d-f893-9982-fc9c-a940ef02c651", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "6b187ece-aa83-098f-16bb-603603f9aef1", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "35355d0c-4fbd-4ad2-3c79-187a22ee99a8", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -2, "z": -1}], "uuid": "fe6a16e1-56b3-950d-34a6-a1ec9aede488", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "f19b5ba5-8a75-d9a6-9837-3cb6745ab312": {"name": "LeftShin", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": -10, "y": "0", "z": "0"}], "uuid": "ab53e9c8-51d8-bd4d-fc36-acd89c7c7078", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -5, "y": "0", "z": "0"}], "uuid": "94b75ae5-6796-24b2-4d5e-2c804b6b9d66", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "228f4f61-1f5e-7e1d-adb7-de3ac0732d24", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "98f9a8a7-edde-1f21-52cb-f2249bef6fd4", "time": 0.45833333333333326, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "4281f403-8e2d-ac0c-f281-23d21c8c774e", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": -10, "y": "0", "z": "0"}], "uuid": "eb1c4527-a8df-f397-7638-5ceb9cf52de9", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 2, "z": -7}], "uuid": "b62b8b40-697e-57b3-f731-f2118ebe93b3", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 1, "z": -7}], "uuid": "23e1c0c8-6d43-9e97-795e-54bec97eb50e", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": 3}], "uuid": "e3fff174-f829-30c0-1aa1-2df4b4ff7972", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": "0"}], "uuid": "db09ceef-c658-69e1-959a-c180f089d632", "time": 0.45833333333333326, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": "0"}], "uuid": "8a0b0212-eec8-e016-aeac-49c84df57abb", "time": 0.125, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": 2, "z": -7}], "uuid": "fdd68154-96ec-cb2d-40fc-3bffae6f2583", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "31c3cd66-9bcd-13f9-0015-be821dce69ce": {"name": "LeftShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "2427e6a6-8310-ab03-5e2f-7a36bf7b0a4c", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "81594361-c43d-d69e-fa3f-b82818d8ad77", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "17a32b2b-61a2-5ca8-e2e7-f7c1685476f5", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "bed2a706-fbcb-0074-238b-c096a15c8cdd", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "95fe918e-9ea9-5722-79e0-aa2d9a95fbc1": {"name": "RightShoulder", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "37e583ca-bf59-ffee-d752-c6f98f9027ba", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 45, "y": "0", "z": "0"}], "uuid": "6da9ff74-f296-322a-9bdf-4d9610a8e930", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "b020db5a-7323-e497-4578-97d37fb10156", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": -1}], "uuid": "df084af9-363b-c4eb-5009-24d53d6fb94e", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "2c1299be-a255-b6b9-2896-c54d54ed6afc": {"name": "RightArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "e20a6245-6fc0-a37e-055d-b8dd72032b90", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "c5383daa-7df9-8c38-79c2-0d7345a845de", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "43a5b687-36e0-1a68-5de8-1e4771607633", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "df725f2b-a96d-40b9-124f-9864af26785f", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "63d4216b-0f6f-31cd-1b0a-a4b10cd7fbb1": {"name": "LeftArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7109a5cb-9bea-46e6-6290-78e2ee1df3d3", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "d7f0e963-706e-7d68-b085-eb2a2a43eccb", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "65034631-c4ba-faaf-287e-70a6fc5fc011", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "4e18e2c5-dca1-fd6d-3192-32d23d37acfe", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "4a425455-781c-7bf1-309c-0892212ef029": {"name": "RightLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "5ede2e7b-dfa1-6d9d-f432-d48006b9a65e", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "12123f67-cd09-aa78-67cd-3968a3040321", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 2.5, "y": 0, "z": 0}], "uuid": "471bcb09-7861-b13b-218a-5e2c5c94865f", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "cad6694a-950a-d790-5dee-3b3eb9896b84", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0a500c6e-4283-c3a2-6baf-367fa454f43e", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "32ede07c-ed3e-2b5a-76db-01028393ba23", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": -1, "z": 0}], "uuid": "bb0de6f0-937e-dc79-bfb3-ab3911f22581", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "ec68ee5d-0dc5-a3de-8c09-31b0652313f9", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "627f20f3-1b81-f4c3-7529-a1517078ac00": {"name": "LeftLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 2.5, "y": "0", "z": "0"}], "uuid": "3869a830-b341-16fb-0745-dd3240c5cd4c", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 2.5, "y": "0", "z": "0"}], "uuid": "30488505-a0b8-f1f9-7d73-40eda962a644", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": "0", "y": 0, "z": 0}], "uuid": "4d4dcc9e-96bb-645d-455c-979c6b9c8dfb", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "rotation", "data_points": [{"x": 2.5, "y": "0", "z": "0"}], "uuid": "5cba1493-25d3-0d45-3ea0-041e4ff5a606", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "fce31a7c-1296-09c4-bf2d-136cd9a00af6", "time": 0.6666666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "aa0e7baf-65c3-ab90-d7f2-c791b95aac4b", "time": 0.5416666666666666, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "f6cf4bf0-7174-3293-b7d1-e73c0fa8fb26", "time": 0.3333333333333333, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": -1, "z": "0"}], "uuid": "36e25d9c-284b-039a-3054-cb183385f136", "time": 0, "color": -1, "interpolation": "catmullrom", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "42010b6c-4a17-70a1-d68c-3280f4f9d4db": {"name": "Body", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "ee7fa3d2-ac9f-3c5a-2890-c2cd3d13df32", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "6b79ade9-5029-3fe3-9a59-4896629e4bdd", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}}}, {"uuid": "b7f706b7-baa9-4557-cc02-f26a3a70903e", "name": "crouchwalk", "loop": "once", "override": true, "length": 0, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}, {"uuid": "8fc23e39-8b46-4387-9235-46e78920fc57", "name": "crouchwalkback", "loop": "once", "override": true, "length": 0, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}, {"uuid": "5e2324ad-d747-186b-4a11-e67ac91836a7", "name": "crouch", "loop": "once", "override": true, "length": 0, "snapping": 24, "selected": false, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}]}