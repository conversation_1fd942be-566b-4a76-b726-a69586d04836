--  _____      _  __ _        _____                 
-- /  ___|    | |/ _(_)      /  __ \                
-- \ `--.  ___| | |_ _  ___  | /  \/ __ _ _ __ ___  
--  `--. \/ _ \ |  _| |/ _ \ | |    / _` | '_ ` _ \ 
-- /\__/ /  __/ | | | |  __/ | \__/\ (_| | | | | | |
-- \____/ \___|_|_| |_|\___|  \____/\__,_|_| |_| |_|
--                                 .--.--.-----.----.---.-.    .-----.--.--.---.-.-----.
--▄▀█ █▀▄ █▀█ █ █▀ ▀█▀ █▀▀ █░░     |  |  |  -__|   _|  _  |    |     |  |  |  _  |     |
--█▀█ █▄▀ █▀▄ █ ▄█ ░█░ ██▄ █▄▄      \___/|_____|__| |___._|____|__|__|___  |___._|__|__|
--                                                       |______|    |_____|
isInSelfieCam = false
isBackwards = false
getSelfieCamTimerThing = 0
pocketSelfieCamTimerThing = 0
keySelfieCam = keybinds:newKeybind("Toggle Selfie Cam", "key.keyboard.f4")
models.selfieCamGUI:setParentType("HUD")
models.selfieCamGUI:setVisible(false)
animations.selfieCamGUI.getPhone:setSpeed(0.8)
animations.selfieCamGUI.pocketPhone:setSpeed(0.8)
vanilla_model.PLAYER:setVisible(false)
animations.player.pocketPhoneArm:overrideRot(true)
animations.player.getPhoneArm:overrideRot(true)
models.player.Head:setPrimaryTexture("SKIN")
models.player.Body:setPrimaryTexture("SKIN")
models.player.RightArm:setPrimaryTexture("SKIN")
models.player.LeftArm.LA.steve:setPrimaryTexture("SKIN")
models.player.LeftArm.LA.alex:setPrimaryTexture("SKIN")
models.player.RightLeg:setPrimaryTexture("SKIN")
models.player.LeftLeg:setPrimaryTexture("SKIN")

function selfieCheck()
	if isInSelfieCam == true then
		vanilla_model.LEFT_ITEM:setVisible(false)
		animations.player.pocketPhoneArm:stop()
		animations.player.pocketPhoneHead:stop()
		animations.player.getPhoneArm:stop()
		animations.player.getPhoneHead:stop()
		animations.player.getPhoneArm:play()
		animations.player.getPhoneHead:play()
		animations.selfieCamGUI.pocketPhone:stop()
		animations.selfieCamGUI.getPhone:stop()
		animations.selfieCamGUI.getPhone:play()
		local coords = (client.getScaledWindowSize()):div(-2, -2)
		models.selfieCamGUI:setPos(coords.xy_)
		models.selfieCamGUI:setVisible(true)
	elseif isInSelfieCam == false then
		vanilla_model.LEFT_ITEM:setVisible(true)
		animations.player.getPhoneArm:stop()
		animations.player.getPhoneHead:stop()
		animations.player.pocketPhoneArm:stop()
		animations.player.pocketPhoneHead:stop()
		animations.player.pocketPhoneArm:play()
		animations.player.pocketPhoneHead:play()
		animations.selfieCamGUI.getPhone:stop()
		animations.selfieCamGUI.pocketPhone:stop()
		animations.selfieCamGUI.pocketPhone:play()
		local coords = (client.getScaledWindowSize()):div(-2, -2)
		models.selfieCamGUI:setPos(coords.xy_)
		models.selfieCamGUI:setVisible(false)
	end
end

function pings.selfieCamEnabled(state)
	isInSelfieCam = (state)
	selfieCheck()
end

function pings.isBackwards(state)
	isBackwards = (state)
end

function events.tick()
	if renderer:isCameraBackwards() and isBackwards == false then
		pings.isBackwards(true)
	elseif not renderer:isCameraBackwards() and isBackwards == true then
		pings.isBackwards(false)
	end
end

function keySelfieCam.press()
	if renderer:isCameraBackwards() then
		function pings.selfieCam()
			if not isInSelfieCam then
				pings.selfieCamEnabled(true)
			elseif isInSelfieCam then
				pings.selfieCamEnabled(false)
			end
		end
		pings.selfieCam()
	else
		host:setActionbar('{"text":"Cant use, not in backwards third-person","color":"red"}')
	end
end

function events.tick()
	if isInSelfieCam then
		getSelfieCamTimerThing = getSelfieCamTimerThing+1
		pocketSelfieCamTimerThing = 0
	else
		getSelfieCamTimerThing = 0
		pocketSelfieCamTimerThing = pocketSelfieCamTimerThing+1
	end
	if isInSelfieCam and getSelfieCamTimerThing >= 15 then
      renderer:offsetCameraPivot(0,-0.05,0)
      renderer:setCameraPos(-0.2,0.07,-3.15)
      renderer:offsetCameraRot(0,-10,-3)
      renderer:setFOV(70 / client:getFOV())
    end
	if not isInSelfieCam and pocketSelfieCamTimerThing >= 5 then
		renderer:offsetCameraPivot(0,0,0)
		renderer:setCameraPos(0,0,0)
		renderer:offsetCameraRot(0,0,0)
		renderer:setFOV(1)
	end
	if player:getModelType() == "DEFAULT" then
		models.player.LeftArm.LA.steve:setVisible(true)
		models.player.RightArm.steve2:setVisible(true)
		models.player.LeftArm.LA.alex:setVisible(false)
		models.player.RightArm.alex2:setVisible(false)
	else
		models.player.LeftArm.LA.steve:setVisible(false)
		models.player.RightArm.steve2:setVisible(false)
		models.player.LeftArm.LA.alex:setVisible(true)
		models.player.RightArm.alex2:setVisible(true)
	end
end
events.RENDER:register(function(delta)
	local headrotation = vanilla_model.HEAD:getOriginRot()
	if isInSelfieCam and getSelfieCamTimerThing >= 14 then
		models.player.LeftArm:setRot(headrotation.x*0.7,headrotation.y*0.8,nil)
	else
		models.player.LeftArm:setRot(0,0,0)
	end
end)
function events.tick()
	if isInSelfieCam and not isBackwards then
			pings.selfieCamEnabled(false)
	end
end

