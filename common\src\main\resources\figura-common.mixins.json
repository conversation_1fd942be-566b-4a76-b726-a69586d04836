{"required": true, "minVersion": "0.8", "package": "org.figuramc.figura.mixin", "compatibilityLevel": "JAVA_17", "mixins": [], "client": ["AABBInvoker", "BiomeAccessor", "BlockBehaviourAccessor", "ClientCommandSourceMixin", "ClientLevelInvoker", "ClientPacketListenerMixin", "EntityAccessor", "EntityMixin", "ItemStackMixin", "LivingEntityAccessor", "LivingEntityMixin", "MinecraftMixin", "ReloadableResourceManagerMixin", "SkullBlockEntityAccessor", "SNIHelperMixin", "TextDisplayMixin", "compat.GeckolibGeoArmorRendererMixin", "compat.GeckolibGeoRendererMixin", "compat.SimpleVCMixin", "font.BakedGlyphMixin", "font.BitmapGlyphMixin", "font.FontSetMixin", "gui.ChatComponentAccessor", "gui.ChatComponentMixin", "gui.ChatScreenAccessor", "gui.ChatScreenMixin", "gui.ClickEventActionMixin", "gui.DebugScreenOverlayMixin", "gui.<PERSON><PERSON>essor", "gui.GuiMessageLineMixin", "gui.Gui<PERSON>essageM<PERSON>in", "gui<PERSON>", "gui.InventoryScreenMixin", "gui.PauseScreenMixin", "gui.PlayerTabOverlayAccessor", "gui.PlayerTabOverlayMixin", "gui.ScreenAccessor", "gui.ScreenMixin", "gui.SplashManagerMixin", "gui.SplashRendererMixin", "gui.SuggestionsListMixin", "gui.books.BookViewScreenMixin", "gui.books.LineInfoMixin", "input.InputConstantsTypeMixin", "input.KeyboardHandlerMixin", "input.KeyMappingAccessor", "input.KeyMappingMixin", "input.MouseHandlerMixin", "particle.ParticleAccessor", "particle.ParticleEngineMixin", "particle.SingleQuadParticleMixin", "render.CameraMixin", "render.EntityRenderDispatcherMixin", "render.GameRendererAccessor", "render.GameRendererMixin", "render.LevelRendererMixin", "render.MissingTextureAtlasSpriteAccessor", "render.ModelManagerAccessor", "render.PlayerModelMixin", "render.TextureAtlasAccessor", "render.TextureManagerAccessor", "render.layers.CapeLayerMixin", "render.layers.CustomHeadLayerMixin", "render.layers.HumanoidArmorLayerAccessor", "render.layers.HumanoidArmorLayerMixin", "render.layers.ParrotOnShoulderLayerMixin", "render.layers.elytra.ElytraLayerAccessor", "render.layers.elytra.ElytraLayerMixin", "render.layers.elytra.ElytraModelAccessor", "render.layers.items.ItemInHandLayerMixin", "render.layers.items.PlayerItemInHandLayerMixin", "render.renderers.ArrowRendererMixin", "render.renderers.BlockEntityWithoutLevelRendererMixin", "render.renderers.EntityRendererMixin", "render.renderers.HandRenderSelectionAccessor", "render.renderers.ItemInHandRendererMixin", "render.renderers.ItemRendererMixin", "render.renderers.LivingEntityRendererMixin", "render.renderers.PlayerRendererMixin", "render.renderers.ScreenEffectRendererMixin", "render.renderers.SignRendererMixin", "render.renderers.SkullBlockRendererMixin", "render.renderers.TridentRendererMixin", "render.PoseStackAccessor", "sound.ChannelHandleMixin", "sound.SoundEngineMixin", "sound.SoundManagerAccessor", "sound.SubtitleOverlayMixin"], "server": [], "injectors": {"defaultRequire": 1}}