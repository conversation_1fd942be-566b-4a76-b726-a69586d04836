{"figura": "<PERSON><PERSON><PERSON>", "figura.tab": "  ", "figura.ellipsis": "…", "figura.backend.connected": "Cloud connected!", "figura.backend.disconnected": "Cloud disconnected!", "figura.backend.user_not_found": "User not found", "figura.backend.avatar_not_found": "Avatar not found", "figura.backend.upload_success": "Avatar uploaded!", "figura.backend.upload_too_big": "Avatar is too big", "figura.backend.upload_too_many": "You have too many Avatars", "figura.backend.upload_error": "Error on uploading Avatar", "figura.backend.delete_success": "Avatar deleted!", "figura.backend.delete_error": "Error on deleting Avatar", "figura.backend.equip_error": "Failed to set equipped Avatars", "figura.backend.warning": "Warning", "figura.backend.ping_size": "Ping too Large", "figura.backend.ping_rate": "Sending too many pings", "figura.toast.reload": "Avatar reloaded!", "figura.toast.reload_all": "Reloaded ALL Avatars!", "figura.toast.panic_enabled": "Panic mode enabled!", "figura.toast.panic_disabled": "Panic mode disabled!", "figura.toast.clipboard": "Copied to clipboard!", "figura.toast.permission_change": "Permissions changed to", "figura.toast.new_version": "New version available!", "figura.toast.avatar_wizard.error": "Failed to create Avatar!", "figura.toast.avatar_wizard.success": "Avatar Created!", "figura.toast.load_error": "Failed to load avatar!", "figura.toast.wardrobe_copy.success": "Copied %s file(s)!", "figura.toast.wardrobe_copy.error": "Failed to copy files!", "figura.toast.cache_clear": "Cleared C<PERSON>!", "figura.toast.avatar_data_clear": "Cleared Avatar Data!", "figura.permissions.category.blocked": "Blocked", "figura.permissions.category.blocked.info": "No permissions\nAll Figura features are disabled\nAs if no Avatar is present", "figura.permissions.category.low": "Low", "figura.permissions.category.low.info": "Low permissions\nMost features are disabled\nIdeal for very simple Avatars", "figura.permissions.category.default": "<PERSON><PERSON><PERSON>", "figura.permissions.category.default.info": "Moderate permissions\nMost features are enabled, however, important ones are disabled\nCan edit vanilla models but not nameplate\nIdeal for general and public Avatars", "figura.permissions.category.high": "High", "figura.permissions.category.high.info": "High permissions\nEvery feature is enabled, but script related things are still limited\nIdeal for complex Avatars", "figura.permissions.category.max": "Max", "figura.permissions.category.max.info": "Maximum permissions\nEverything is enabled and maxed out\nAvatars have the full machine resources\nIdeal for very complex Avatars", "figura.permissions.infinity": "INFINITY", "figura.permissions.enabled": "Enabled", "figura.permissions.disabled": "Disabled", "figura.permissions.value.init_inst": "Init instructions", "figura.permissions.value.init_inst.tooltip": "Amount of allowed code instructions during the script initialization phase", "figura.permissions.value.world_tick_inst": "World tick instructions", "figura.permissions.value.world_tick_inst.tooltip": "Amount of allowed code instructions during the World Tick event", "figura.permissions.value.tick_inst": "Tick Instructions", "figura.permissions.value.tick_inst.tooltip": "Amount of allowed code instructions during the Tick event", "figura.permissions.value.world_render_inst": "World Render Instructions", "figura.permissions.value.world_render_inst.tooltip": "Amount of allowed code instructions during the World Render event", "figura.permissions.value.render_inst": "Render Instructions", "figura.permissions.value.render_inst.tooltip": "Amount of allowed code instructions during the Render event", "figura.permissions.value.complexity": "Max Complexity", "figura.permissions.value.complexity.tooltip": "Maximum complexity allowed for this Avatar\nComplexity is calculated as 1 face -> 1 complexity\nthat means a cube takes 6 complexity\nHidden parts do not add to complexity", "figura.permissions.value.particles": "<PERSON>", "figura.permissions.value.particles.tooltip": "The maximum allowed Particles per second that this Avatar can spawn", "figura.permissions.value.sounds": "Max Sounds", "figura.permissions.value.sounds.tooltip": "The maximum allowed Sounds per second that this Avatar can spawn", "figura.permissions.value.volume": "Avatar Sounds Volume", "figura.permissions.value.volume.tooltip": "Changes the volume of this Avatar Sounds", "figura.permissions.value.bb_animations": "Animations Complexity", "figura.permissions.value.bb_animations.tooltip": "Maximum Animation complexity allowed for this Avatar\nComplexity is calculated as 1 per used animation channel\nComplexity is only calculated for playing Animations", "figura.permissions.value.animation_inst": "Animations Instructions", "figura.permissions.value.animation_inst.tooltip": "Amount of allowed code instructions during Animations code events, or keyframe code data", "figura.permissions.value.texture_size": "Max Texture Size", "figura.permissions.value.texture_size.tooltip": "Maximum allowed size for newly created textures through the script\nTextures pre-loaded from the Avatar are ignored", "figura.permissions.value.vanilla_model_edit": "Vanilla Model Change", "figura.permissions.value.vanilla_model_edit.tooltip": "Toggles if the Ava<PERSON> can control changing vanilla model things, like the visibility of the vanilla player, armor, elytra, among other things", "figura.permissions.value.nameplate_edit": "Nameplate Change", "figura.permissions.value.nameplate_edit.tooltip": "Toggles if the Avatar can change its nameplate, allowing for completely custom names, positioning and even disabling its rendering", "figura.permissions.value.offscreen_rendering": "Render Offscreen", "figura.permissions.value.offscreen_rendering.tooltip": "Toggles if the Avatar should render even when you (the viewer) are not looking at them, ie behind you", "figura.permissions.value.cancel_sounds": "Cancel Sounds", "figura.permissions.value.cancel_sounds.tooltip": "Toggles if the Avatar can stop any sound from playing via the ON_PLAY_SOUND event", "figura.permissions.value.custom_render_layer": "Custom Render Layers", "figura.permissions.value.custom_render_layer.tooltip": "Toggles if the Avatar can create its own Render Layers, allowing for custom GLSL code (shaders)", "figura.permissions.value.custom_sounds": "Custom Sounds", "figura.permissions.value.custom_sounds.tooltip": "Toggles if the Avatar can play Custom Sounds, provided by the Avatar itself", "figura.permissions.value.custom_skull": "Custom Player Skull", "figura.permissions.value.custom_skull.tooltip": "Toggles if Player Heads should render the Avatar instead of the vanilla skin", "figura.permissions.value.buffer_size": "<PERSON><PERSON><PERSON>", "figura.permissions.value.buffer_size.tooltip": "Max size of byte buffer that can be created by Avatar", "figura.permissions.value.buffers_count": "Buffers Count", "figura.permissions.value.buffers_count.tooltip": "Max amount of buffers that can be created by this avatar", "figura.permissions.value.networking": "Networking", "figura.permissions.value.networking.tooltip": "Allows this avatar to use networking features", "figura.permissions.value.max_sockets_count": "Max Sockets", "figura.permissions.value.max_sockets_count.tooltip": "Max Amount of open sockets", "figura.popup_menu.cancel": "Cancel", "figura.popup_menu.reload": "Reload Avatar", "figura.popup_menu.increase_permissions": "Increase Permissions", "figura.popup_menu.decrease_permissions": "Decrease Permissions", "figura.popup_menu.permission_screen": "Open in the Permissions Screen", "figura.badges.system.default": "Figura Mark!", "figura.badges.system.permissions": "This Avatar utilizes a higher Permission category!", "figura.badges.system.warning": "This Avatar targets a newer version of Figura, compatibility might be limited!", "figura.badges.system.error": "This Avatar Script contains an error!", "figura.badges.system.sound": "This Avatar is playing a sound!", "figura.badges.pride.agender": "Figura Agender Mark!", "figura.badges.pride.aroace": "Figura Aroace Mark!", "figura.badges.pride.aromantic": "Figura Aromantic Mark!", "figura.badges.pride.asexual": "Figura Asexual Mark!", "figura.badges.pride.bigender": "Figura Bigender Mark!", "figura.badges.pride.bisexual": "Figura Bisexual Mark!", "figura.badges.pride.demiboy": "Figura Demiboy Mark!", "figura.badges.pride.demigender": "Figura Demigender Mark!", "figura.badges.pride.demigirl": "Figura Demigirl Mark!", "figura.badges.pride.demiromantic": "Fi<PERSON>ra Demiromantic Mark!", "figura.badges.pride.demisexual": "Figura Demisexual Mark!", "figura.badges.pride.disability": "Figura Disability Mark!", "figura.badges.pride.finsexual": "Figura Finsexual Mark!", "figura.badges.pride.gaymen": "Figura Gay Men Mark!", "figura.badges.pride.genderfae": "Figura Genderfae Mark!", "figura.badges.pride.genderfluid": "Figura Genderfluid Mark!", "figura.badges.pride.genderqueer": "Figura Genderqueer Mark!", "figura.badges.pride.intersex": "Figura Intersex Mark!", "figura.badges.pride.lesbian": "Figura Lesbian Mark!", "figura.badges.pride.nonbinary": "Figura Nonbinary Mark!", "figura.badges.pride.pansexual": "Figura Pansexual Mark!", "figura.badges.pride.plural": "Figura Plural Mark!", "figura.badges.pride.polysexual": "Figura Polysexual Mark!", "figura.badges.pride.pride": "Figura Pride Mark!", "figura.badges.pride.transgender": "Figura Transgender Mark!", "figura.badges.special.dev": "Figura Developer!", "figura.badges.special.discord_staff": "Official Figura Discord Staff!", "figura.badges.special.contest": "Figura contest winner! GG!", "figura.badges.special.donator": "Thank you for supporting the Figura mod!", "figura.badges.special.translator": "Figura mod Translator!", "figura.badges.special.texture_artist": "Figura mod Texture Artist!", "figura.badges.special.immortalized": "Immortalized Figura Avatar!", "figura.badges.no_permissions.init_inst": "Script overran resource limits", "figura.badges.no_permissions.complexity": "Model too complex", "figura.badges.no_permissions.nameplate_edit": "Could not edit nameplate", "figura.badges.no_permissions.bb_animations": "Model Animations too complex", "figura.badges.no_permissions.texture_size": "Custom Textures too huge", "figura.badges.no_permissions.particles": "Reached Particles limit", "figura.badges.no_permissions.sounds": "Reached Sounds limit", "figura.badges.no_permissions.cancel_sounds": "Could not cancel a sound", "figura.badges.no_permissions.custom_sounds": "Could not use Custom Sounds", "figura.badges.no_permissions.vanilla_model_edit": "Tried to change the Vanilla Model", "figura.emoji.face_angry": "Angry", "figura.emoji.face_biba": "Biba", "figura.emoji.face_fear": "Fear", "figura.emoji.face_happy": "Happy", "figura.emoji.face_misc": "Miscellaneous Faces", "figura.emoji.face_nervous": "Nervous", "figura.emoji.face_sad": "Sad", "figura.emoji.face_silly": "Silly", "figura.emoji.animal": "Animals", "figura.emoji.food": "Foods", "figura.emoji.hand": "Hands", "figura.emoji.heart": "Hearts", "figura.emoji.nature": "Nature", "figura.emoji.animated": "Animated", "figura.emoji.symbol": "Symbols", "figura.emoji.object": "Objects", "figura.emoji.logo": "Logos", "figura.emoji.misc": "Miscellaneous", "figura.emoji.portrait": "Portraits", "figura.emoji.pride": "Pride", "figura.emoji.reference": "References", "figura.emoji.reference_bloons": "Bloons References", "figura.emoji.reference_logo": "Logo References", "figura.emoji.reference_retro": "Retro References", "figura.emoji.meme": "<PERSON><PERSON>", "figura.gui.on": "ON", "figura.gui.off": "OFF", "figura.gui.create": "Create", "figura.gui.name": "Name", "figura.gui.authors": "Authors", "figura.gui.size": "Size", "figura.gui.complexity": "Complexity", "figura.gui.done": "Done", "figura.gui.cancel": "Cancel", "figura.gui.expand": "Expand", "figura.gui.minimise": "Minimize", "figura.gui.reset_all": "Reset All", "figura.gui.panic": "Panic mode active! Figura features are disabled!\ntoggle panic mode with the [%s] key", "figura.gui.avatar_settings.tooltip": "Avatar Settings", "figura.gui.duplicate_keybind": "This key is also used for:\n%s", "figura.gui.favorited": "Favorited", "figura.gui.new_version.tooltip": "A new version of Figura is available! %s\nClick to download it in your browser!", "figura.gui.old_version.tooltip": "Are you a time traveler? The latest version is %s", "figura.gui.clear": "Clear", "figura.gui.not_available_yet": "Not available yet", "figura.gui.text_hint.any": "Text", "figura.gui.text_hint.int": "Integer", "figura.gui.text_hint.positive_int": "Integer", "figura.gui.text_hint.float": "Number", "figura.gui.text_hint.positive_float": "Number", "figura.gui.text_hint.hex_color": "Color", "figura.gui.text_hint.folder_path": "Path", "figura.gui.text_hint.ip": "IP Address", "figura.gui.text_hint.search": "Search", "figura.gui.text_hint.name": "Name/UUID", "figura.gui.error.no_avatar": "No Avatar selected!", "figura.gui.error.no_keybinds": "Avatar does not have any keybinds!", "figura.gui.error.no_sounds": "Avatar does not have custom sounds!", "figura.gui.error.no_wheel_page": "Action Wheel has no active Page!", "figura.gui.action_wheel.slots_indicator": "Slots %s-%s of %s", "figura.gui.context.set_permissions": "Set Permissions", "figura.gui.context.reload": "Reload Avatar", "figura.gui.context.copy_name": "Copy Name", "figura.gui.context.copy_uuid": "Copy UUID", "figura.gui.context.copy_path": "Copy Path", "figura.gui.context.open_folder": "Open Folder", "figura.gui.context.favorite.add": "Add to Favorites", "figura.gui.context.favorite.remove": "Remove from Favorites", "figura.gui.panels.title.profile": "Profile", "figura.gui.panels.title.browser": "Browser", "figura.gui.panels.title.wardrobe": "<PERSON><PERSON>", "figura.gui.panels.title.permissions": "Permissions", "figura.gui.panels.title.settings": "Settings", "figura.gui.panels.title.help": "Help", "figura.gui.panels.title.sound": "Avatar Sounds", "figura.gui.panels.title.keybind": "Avatar Keybinds", "figura.gui.panels.title.avatar": "Avatar Preview", "figura.gui.panels.title.docs": "Lua Docs", "figura.gui.wardrobe.expand_wardrobe.tooltip": "Expand wardrobe list", "figura.gui.wardrobe.minimize_wardrobe.tooltip": "Minimize wardrobe list", "figura.gui.wardrobe.upload.tooltip": "Upload avatar to the Cloud\nOther players only see you as your uploaded avatar", "figura.gui.wardrobe.reload.tooltip": "Reload avatar from the Cloud", "figura.gui.wardrobe.delete.tooltip": "Delete avatar from the Cloud", "figura.gui.wardrobe.sound.tooltip": "Avatar Sounds", "figura.gui.wardrobe.keybind.tooltip": "Avatar Keybinds", "figura.gui.wardrobe.new_avatar.tooltip": "Create new Avatar", "figura.gui.wardrobe.unselect.tooltip": "Unselect current Avatar", "figura.gui.wardrobe.folder.tooltip": "Open avatars root folder", "figura.gui.wardrobe.drop_files": "Do you want to copy the following files to your wardrobe?", "figura.gui.sound.play": "Play sound", "figura.gui.sound.stop": "Stop sound", "figura.gui.permissions.reset": "Reset All", "figura.gui.permissions.reload_all": "Reload All", "figura.gui.permissions.precise": "Precise Mode", "figura.gui.permissions.expand_permissions.tooltip": "Expand advanced permissions menu", "figura.gui.permissions.minimize_permissions.tooltip": "Minimize advanced permissions menu", "figura.gui.permissions.figura_only.tooltip": "Show only Figura players", "figura.gui.permissions.disconnected.tooltip": "Show disconnected avatars", "figura.gui.permissions.disconnected": "(disconnected)", "figura.gui.permissions.size": "Avatar Size", "figura.gui.permissions.complexity": "Model Complexity", "figura.gui.permissions.init": "Init Instructions", "figura.gui.permissions.init.root": "Root: %s", "figura.gui.permissions.init.entity": "Entity: %s", "figura.gui.permissions.tick": "Tick Instructions", "figura.gui.permissions.tick.world": "World: %s", "figura.gui.permissions.tick.entity": "Entity: %s", "figura.gui.permissions.render": "Render Instructions", "figura.gui.permissions.render.world": "World: %s", "figura.gui.permissions.render.entity": "Entity: %s", "figura.gui.permissions.render.post_entity": "Post Entity: %s", "figura.gui.permissions.render.post_world": "Post World: %s", "figura.gui.permissions.render.animations": "Animations: %s", "figura.gui.load_error": "Loading Error", "figura.gui.load_error.unknown": "Unknown error", "figura.gui.load_error.scripts": "Invalid script", "figura.gui.load_error.sounds": "Invalid custom sound", "figura.gui.load_error.models": "Invalid model", "figura.gui.load_error.metadata": "Invalid \"avatar.json\"", "figura.gui.status.reason": "Reason", "figura.gui.status.size": "Size", "figura.gui.status.size.0": "none", "figura.gui.status.size.1": "Avatar size over upload limit (%s)", "figura.gui.status.size.2": "Avatar size is over 75%% of the upload limit (%s)", "figura.gui.status.size.3": "ok", "figura.gui.status.texture": "Texture", "figura.gui.status.texture.0": "none", "figura.gui.status.texture.3": "ok", "figura.gui.status.script": "<PERSON><PERSON><PERSON>", "figura.gui.status.script.0": "none", "figura.gui.status.script.1": "This Avatar script contains an error", "figura.gui.status.script.2": "This Avatar script targets a newer version of Figura", "figura.gui.status.script.3": "ok", "figura.gui.status.backend": "Cloud", "figura.gui.status.backend.1": "Disconnected", "figura.gui.status.backend.2": "Connecting", "figura.gui.status.backend.3": "Connected", "figura.gui.avatar_wizard.meta": "Avatar <PERSON>ada<PERSON>", "figura.gui.avatar_wizard.name": "Name", "figura.gui.avatar_wizard.description": "Description", "figura.gui.avatar_wizard.authors": "Authors", "figura.gui.avatar_wizard.model": "Model", "figura.gui.avatar_wizard.dummy_model": "Include a Model", "figura.gui.avatar_wizard.player_model": "Player Model", "figura.gui.avatar_wizard.slim": "<PERSON> (Small) Arms", "figura.gui.avatar_wizard.cape": "Cape template", "figura.gui.avatar_wizard.elytra": "Elytra template", "figura.gui.avatar_wizard.pivots": "Pivots", "figura.gui.avatar_wizard.items_pivot": "Held Items Pivot", "figura.gui.avatar_wizard.spyglass_pivot": "Spyglass Pivot", "figura.gui.avatar_wizard.helmet_item_pivot": "Head <PERSON><PERSON>", "figura.gui.avatar_wizard.parrots_pivot": "Parrots Pivot", "figura.gui.avatar_wizard.armor_pivots": "Armor <PERSON>", "figura.gui.avatar_wizard.scripting": "<PERSON><PERSON><PERSON>", "figura.gui.avatar_wizard.dummy_script": "Include a <PERSON>ript", "figura.gui.avatar_wizard.hide_player": "<PERSON><PERSON> Vanilla Player", "figura.gui.avatar_wizard.hide_armor": "Hide Vanilla Armor", "figura.gui.avatar_wizard.hide_cape": "Hide Vanilla Cape", "figura.gui.avatar_wizard.hide_elytra": "Hide Vanilla Ely<PERSON>", "figura.gui.avatar_wizard.empty_events": "Include dummy events", "figura.gui.network_filter": "Network Filter", "figura.gui.network_filter.list.filter_mode.0": "Equals", "figura.gui.network_filter.list.filter_mode.0.tooltip": "Links will match this filter ONLY if they fully equals to filter source", "figura.gui.network_filter.list.filter_mode.1": "Contains", "figura.gui.network_filter.list.filter_mode.1.tooltip": "Links will match this filter if they fully contains filter source", "figura.gui.network_filter.list.filter_mode.2": "Starts with", "figura.gui.network_filter.list.filter_mode.2.tooltip": "Links will match this filter if they starts with filter source", "figura.gui.network_filter.list.filter_mode.3": "Ends with", "figura.gui.network_filter.list.filter_mode.3.tooltip": "Links will match this filter if they ends with filter source", "figura.gui.network_filter.list.filter_mode.4": "RegEx", "figura.gui.network_filter.list.filter_mode.4.tooltip": "Links will match this filter if they matches the RegEx pattern specified in filter source", "figura.gui.network_filter.list.add_filter_entry": "Add Filter Entry", "figura.gui.help.docs": "Docs", "figura.gui.help.links": "Links", "figura.gui.help.about": "About", "figura.gui.help.ingame_docs": "Figura Docs", "figura.gui.help.lua_manual": "Lua Manual", "figura.gui.help.external_wiki": "Online Wiki", "figura.gui.help.lua_version": "Lua Version: %s", "figura.gui.help.figura_version": "Figura Version: %s", "figura.config.nameplate": "Nameplate", "figura.config.nameplate.tooltip": "Nameplate settings", "figura.config.self_nameplate": "Enable Self Nameplate", "figura.config.self_nameplate.tooltip": "Toggles the rendering of your nameplate", "figura.config.nameplate_render": "Nameplate Render Mode", "figura.config.nameplate_render.tooltip": "Toggles the conditions to render entities nameplates", "figura.config.nameplate_render.1": "Vanilla", "figura.config.nameplate_render.2": "Vanilla + Looking At", "figura.config.nameplate_render.3": "Never", "figura.config.nameplate_render.1.tooltip": "Vanilla behavior", "figura.config.nameplate_render.2.tooltip": "You must be looking at the entity for its nameplate to render", "figura.config.nameplate_render.3.tooltip": "Nameplate will never render", "figura.config.preview_nameplate": "Nameplate in GUI", "figura.config.preview_nameplate.tooltip": "Toggles the rendering of nameplates in the GUI", "figura.config.sound_badge": "Sound Indicator", "figura.config.sound_badge.tooltip": "Adds a little indicator in the nameplate for when an avatar is playing a sound\nRequires badges to be enabled", "figura.config.chat_nameplate": "Chat customizations", "figura.config.chat_nameplate.tooltip": "Set the level of nameplate customizations on the CHAT", "figura.config.entity_nameplate": "Entity customizations", "figura.config.entity_nameplate.tooltip": "Set the level of nameplate customizations on the ENTITY", "figura.config.list_nameplate": "Tablist customizations", "figura.config.list_nameplate.tooltip": "Set the level of nameplate customizations on the TABLIST", "figura.config.nameplate_level.1": "None", "figura.config.nameplate_level.2": "<PERSON><PERSON><PERSON>", "figura.config.nameplate_level.3": "Script + Badges", "figura.config.nameplate_level.1.tooltip": "No customizations will be applied", "figura.config.nameplate_level.2.tooltip": "Allows for script customizations", "figura.config.nameplate_level.3.tooltip": "Allows the inclusion of special Badges provided by <PERSON><PERSON>ra", "figura.config.script": "<PERSON><PERSON><PERSON>", "figura.config.script.tooltip": "Script settings", "figura.config.log_location": "Print Output", "figura.config.log_location.tooltip": "Output location of script print functions", "figura.config.log_location.1": "Cha<PERSON>", "figura.config.log_location.2": "<PERSON><PERSON>", "figura.config.log_location.1.tooltip": "The in-game chat (the chat also prints in the logger)", "figura.config.log_location.2.tooltip": "The output log of your client", "figura.config.log_number_length": "Print Number Length", "figura.config.log_number_length.tooltip": "Changes the length of the decimal places that will be displayed within print functions", "figura.config.format_script": "Format Script", "figura.config.format_script.tooltip.1": "Selects if scripts should be compacted, removing out comments, whitespaces, and/or empty lines", "figura.config.format_script.tooltip.2": "Requires reselecting the avatar to take effect", "figura.config.format_script.1": "Disabled", "figura.config.format_script.2": "Light", "figura.config.format_script.3": "Heavy", "figura.config.format_script.4": "AST", "figura.config.format_script.1.tooltip": "No compacting", "figura.config.format_script.2.tooltip": "Minimal compacting, removes only comments and unnecessary whitespaces", "figura.config.format_script.3.tooltip": "Same as Light and also compacts everything into a single line", "figura.config.format_script.4.tooltip": "Parses code into Abstract Syntax Tree and reconstructs code into theoretically optimal representation", "figura.config.rendering": "Rendering", "figura.config.rendering.tooltip": "Rendering settings", "figura.config.iris_compatibility_fix": "Shader Pack Compatibility Fix", "figura.config.iris_compatibility_fix.tooltip": "Series of compatibility fixes to ensure compatibility with shader pack mods\nIf the mods are not installed, all fixes will be ignored", "figura.config.iris_compatibility_fix.1": "None", "figura.config.iris_compatibility_fix.2": "Offset", "figura.config.iris_compatibility_fix.3": "Offset + Emissives", "figura.config.iris_compatibility_fix.1.tooltip": "Don't apply any compatibility fix", "figura.config.iris_compatibility_fix.2.tooltip": "Offset special render types a tiny bit, to make sure they're rendered on top of other render types", "figura.config.iris_compatibility_fix.3.tooltip": "When a shader pack is active, change the way the emissive render type works to a more compatible system that uses fully-lit textures instead of the vanilla eyes type", "figura.config.allow_fp_hands": "First Person Hands", "figura.config.allow_fp_hands.tooltip": "Toggles if scripts can move around, rotate or scale the First Person Hand parent type groups", "figura.config.render_debug_parts_pivot": "Render Parts Pivot", "figura.config.render_debug_parts_pivot.tooltip": "Render the Pivots from %d and %d when the hitbox debug rendering is enabled", "figura.config.render_debug_parts_pivot.tooltip.cubes": "cubes", "figura.config.render_debug_parts_pivot.tooltip.groups": "groups", "figura.config.render_debug_parts_pivot.1": "Off", "figura.config.render_debug_parts_pivot.2": "Host Only", "figura.config.render_debug_parts_pivot.3": "Everyone", "figura.config.render_debug_parts_pivot.1.tooltip": "Do not render pivots", "figura.config.render_debug_parts_pivot.2.tooltip": "Render pivots of only the Host Avatar", "figura.config.render_debug_parts_pivot.3.tooltip": "Render pivots of all Avatars", "figura.config.inventory_scissor": "Render Inventory with Scissors", "figura.config.inventory_scissor.tooltip": "Enabling this stops the player from rendering outside of the inventory entity view box.", "figura.config.first_person_matrices": "First Person Matrices", "figura.config.first_person_matrices.tooltip": "Toggles if the model part matrices should be updated while in first person", "figura.config.action_wheel": "Action Wheel", "figura.config.action_wheel.tooltip": "Action Wheel settings", "figura.config.action_wheel_button": "Action Wheel Button", "figura.config.action_wheel_button.tooltip": "<PERSON><PERSON> to open the Action Wheel", "figura.config.action_wheel_mode": "Action Wheel Mode", "figura.config.action_wheel_mode.tooltip": "Changes the way to open and interact with the Action Wheel", "figura.config.action_wheel_mode.1": "Hold", "figura.config.action_wheel_mode.2": "Toggle", "figura.config.action_wheel_mode.3": "Hold (run on release)", "figura.config.action_wheel_mode.4": "Toggle (run on release)", "figura.config.action_wheel_mode.1.tooltip": "The Action Wheel will render as long as its button is pressed", "figura.config.action_wheel_mode.2.tooltip": "Pressing the Action Wheel button will change the Action Wheel visibility", "figura.config.action_wheel_mode.3.tooltip": "Same as Hold, but the selected Action will be run once the Action Wheel is closed", "figura.config.action_wheel_mode.4.tooltip": "Same as Toggle, but the selected Action will be run once the Action Wheel is closed", "figura.config.action_wheel_scale": "Action Wheel Size", "figura.config.action_wheel_scale.tooltip": "Changes the Action Wheel size", "figura.config.action_wheel_title": "Selected Action's Text", "figura.config.action_wheel_title.tooltip": "Changes where the place of the selected action's text is shown", "figura.config.action_wheel_title.1": "Mouse (Tooltip)", "figura.config.action_wheel_title.2": "Mouse", "figura.config.action_wheel_title.3": "Top", "figura.config.action_wheel_title.4": "Middle", "figura.config.action_wheel_title.5": "Bottom", "figura.config.action_wheel_title.6": "Icon", "figura.config.action_wheel_title.7": "Sides", "figura.config.action_wheel_title.1.tooltip": "Renders like the Figura GUI tooltips", "figura.config.action_wheel_title.2.tooltip": "Renders the text next to the mouse", "figura.config.action_wheel_title.3.tooltip": "Renders the text above the Action Wheel", "figura.config.action_wheel_title.4.tooltip": "Renders the text in the middle of the Action Wheel", "figura.config.action_wheel_title.5.tooltip": "Renders the text below the Action Wheel", "figura.config.action_wheel_title.6.tooltip": "Renders all Action's text below its Icon", "figura.config.action_wheel_title.7.tooltip": "Renders all Action's text in the sides of the Action Wheel", "figura.config.action_wheel_slots_indicator": "Slots Indicator Text", "figura.config.action_wheel_slots_indicator.tooltip": "Changes the position of this Page's Slots Indicator text, which is based on the slots shown in the action wheel\nIf the Selected Action Text is set to the same position as this, the action text will take priority when rendering", "figura.config.action_wheel_slots_indicator.1": "Top", "figura.config.action_wheel_slots_indicator.2": "Middle", "figura.config.action_wheel_slots_indicator.3": "Bottom", "figura.config.action_wheel_slots_indicator.1.tooltip": "Renders the text above the Action Wheel", "figura.config.action_wheel_slots_indicator.2.tooltip": "Renders the text in the middle of the Action Wheel", "figura.config.action_wheel_slots_indicator.3.tooltip": "Renders the text below the Action Wheel", "figura.config.action_wheel_decorations": "Item Decorations", "figura.config.action_wheel_decorations.tooltip": "Toggle the rendering of the item decorations, like the damage bar and the item count", "figura.config.ui": "UI Settings", "figura.config.ui.tooltip": "UI Settings", "figura.config.background_scroll_speed": "Background <PERSON><PERSON>", "figura.config.background_scroll_speed.tooltip": "Multiplier for scroll speed of the animated background", "figura.config.popup_scale": "Player Popup Scale", "figura.config.popup_scale.tooltip": "Scale multiplier for the player popup", "figura.config.popup_min_size": "Player Popup <PERSON>", "figura.config.popup_min_size.tooltip": "Minimum size of player popup as you get farther from the person", "figura.config.popup_max_size": "Player Popup <PERSON>", "figura.config.popup_max_size.tooltip": "Maximum size of player popup as you get closer to the person", "figura.config.avatar_portrait": "Avatar Portrait", "figura.config.avatar_portrait.tooltip": "Toggles if the player lists should render a portrait of their avatar instead of the player skin", "figura.config.figura_inventory": "Figura Inventory", "figura.config.figura_inventory.tooltip": "Toggles between vanilla and <PERSON><PERSON><PERSON>'s paperdoll rendering for the player inventory", "figura.config.toast_time": "Toast Display Time", "figura.config.toast_time.tooltip": "Changes how much time (in seconds) the Figura toasts should be visible", "figura.config.toast_title_time": "Toast Title Display Time", "figura.config.toast_title_time.tooltip": "Changes how much time (in seconds) long toasts should show its title before changing to the message", "figura.config.wardrobe_file_names": "Wardrobe File Names", "figura.config.wardrobe_file_names.tooltip": "Changes the wardrobe avatar list to use the file names instead of the metadata (avatar.json) name", "figura.config.preview_head_rotation": "GUI Head Rotation", "figura.config.preview_head_rotation.tooltip": "Toggles if the player head rotation should be applied in avatar previews through the GUI\nWhen off, the avatar will always be rendered looking forwards", "figura.config.text_scroll_speed": "Text Scroll Speed", "figura.config.text_scroll_speed.tooltip": "Changes how fast the Text should scroll", "figura.config.text_scroll_delay": "Text Sc<PERSON> Delay", "figura.config.text_scroll_delay.tooltip": "Changes the delay (in ticks) that the Text should wait before scrolling", "figura.config.reduced_motion": "Reduced Motion", "figura.config.reduced_motion.tooltip": "Disables all screen animations, making them instant, or a single frame\nAlso makes tooltips show in the bottom center of the screen, instead of following the mouse\nDoes not affect animations that have their own setting (like background animation and text scroll)", "figura.config.paperdoll": "Paperdoll", "figura.config.paperdoll.tooltip": "Paperdoll settings", "figura.config.has_paperdoll": "Enable paperdoll", "figura.config.has_paperdoll.tooltip": "Toggle the paperdoll rendering", "figura.config.paperdoll_always_on": "Always on", "figura.config.paperdoll_always_on.tooltip": "Toggle if the paperdoll should render always or only during poses", "figura.config.first_person_paperdoll": "First Person only", "figura.config.first_person_paperdoll.tooltip": "Toggles if the paperdoll should render only when in first person", "figura.config.paperdoll_invisible": "Remove Invisibility", "figura.config.paperdoll_invisible.tooltip": "Toggles if the paperdoll should remove the invisibility effect while rendering", "figura.config.paperdoll_scale": "Paperdoll Scale", "figura.config.paperdoll_scale.tooltip": "Changes the paperdoll scale", "figura.config.paperdoll_x": "Paperdoll X", "figura.config.paperdoll_x.tooltip": "Changes the paperdoll X position", "figura.config.paperdoll_y": "Paperdoll Y", "figura.config.paperdoll_y.tooltip": "Changes the paperdoll Y position", "figura.config.paperdoll_pitch": "Paperdoll Pitch", "figura.config.paperdoll_pitch.tooltip": "Changes the paperdoll PITCH rotation", "figura.config.paperdoll_yaw": "Paperdoll Yaw", "figura.config.paperdoll_yaw.tooltip": "Changes the paperdoll YAW rotation", "figura.config.misc": "Misc", "figura.config.misc.tooltip": "Unsorted/Unrelated settings", "figura.config.popup_button": "<PERSON><PERSON>", "figura.config.popup_button.tooltip": "Button to open a popup menu with quick actions for the targeted user", "figura.config.reload_button": "Reload Avatar", "figura.config.reload_button.tooltip": "Reloads current equipped avatar on press", "figura.config.panic_button": "Panic Button", "figura.config.panic_button.tooltip": "Stop the rendering of all avatars", "figura.config.wardrobe_button": "<PERSON><PERSON>", "figura.config.wardrobe_button.tooltip": "Opens the Figura Wardrobe screen", "figura.config.button_location": "<PERSON>u <PERSON> Location", "figura.config.button_location.tooltip": "Location of the figura button", "figura.config.button_location.1": "Icon", "figura.config.button_location.2": "Top Left", "figura.config.button_location.3": "Top Right", "figura.config.button_location.4": "Bottom Left", "figura.config.button_location.5": "Bottom Right", "figura.config.button_location.1.tooltip": "Renders the Figura button as a small Icon next to the Player Reporting, or Open to LAN, button", "figura.config.button_location.2.tooltip": "Renders the Figura button as text, anchored in the Top Left corner of the screen", "figura.config.button_location.3.tooltip": "Renders the Figura button as text, anchored in the Top Right corner of the screen", "figura.config.button_location.4.tooltip": "Renders the Figura button as text, anchored in the Bottom Left corner of the screen", "figura.config.button_location.5.tooltip": "Renders the Figura button as text, anchored in the Bottom Right corner of the screen", "figura.config.update_channel": "Figura Update Channel", "figura.config.update_channel.tooltip": "Changes the version mode when looking for new Figura updates", "figura.config.update_channel.1": "None", "figura.config.update_channel.2": "Releases (Stable)", "figura.config.update_channel.3": "Pre-releases (Unstable)", "figura.config.update_channel.1.tooltip": "Do not check for updates", "figura.config.update_channel.2.tooltip": "Check only for releases updates", "figura.config.update_channel.3.tooltip": "Check for pre-releases and releases updates", "figura.config.default_permission_level": "Default Permission Level", "figura.config.default_permission_level.tooltip": "Changes the default Permission Level that new players will be assigned into", "figura.config.emojis": "Emojis", "figura.config.emojis.tooltip": "Toggles the emoji parsing on chat messages and signs, that converts \":emoji_name:\" into emojis\nChanges on signs are only updated once reloading the world", "figura.config.emojis.1": "Disabled", "figura.config.emojis.2": "On", "figura.config.emojis.3": "On + Suggestions", "figura.config.emojis.1.tooltip": "No Emojis", "figura.config.emojis.2.tooltip": "Allow Emojis\nSuggestions can be given by pressing tab (like player names)", "figura.config.emojis.3.tooltip": "Allow Emojis and automatically suggest them on chat (like commands)", "figura.config.easter_eggs": "Enable Easter eggs", "figura.config.easter_eggs.tooltip": "Toggles fun and unexpected aspects of Figura (boring!)", "figura.config.dev": "<PERSON>", "figura.config.dev.tooltip": "Developer/Experimental settings", "figura.config.debug_mode": "Debug Mode", "figura.config.debug_mode.tooltip": "Toggles Figura's debug mode. Only has a few features, mostly tools for the mod's development", "figura.config.local_assets": "Local Assets", "figura.config.local_assets.tooltip": "Toggle whether Figura will load assets from the backend, or a local folder.\nThis local folder is called \"local_cache\"", "figura.config.connection_toasts": "Cloud Connection Toasts", "figura.config.connection_toasts.tooltip": "Toggles if the game should show up toasts related with the cloud connection status", "figura.config.log_others": "Log non-host scripts", "figura.config.log_others.tooltip": "Allows debug logging and errors of non-host scripts", "figura.config.log_pings": "Log Pings", "figura.config.log_pings.tooltip": "Toggle logging of pings messages\nThe logging output is controlled by the \"Print Output\" config", "figura.config.log_pings.1": "Off", "figura.config.log_pings.2": "Host Only", "figura.config.log_pings.3": "Everyone", "figura.config.log_pings.1.tooltip": "Do not log pings", "figura.config.log_pings.2.tooltip": "Log pings of only the Host Avatar", "figura.config.log_pings.3.tooltip": "Log pings of all Avatars", "figura.config.sync_pings": "Sync Pings", "figura.config.sync_pings.tooltip.1": "Toggles if host pings should be executed locally or received from the backend", "figura.config.sync_pings.tooltip.2": "Note that: if toggled, your pings will never run if you're not connected to the backend!", "figura.config.chat_messages": "Chat Messages", "figura.config.chat_messages.tooltip.1": "Toggles if scripts can change and/or send chat messages for you", "figura.config.chat_messages.tooltip.2": "This is a dangerous option, as the chat messages will still be signed", "figura.config.chat_messages.tooltip.3": "Figura does not take any responsibility for the usage of this feature", "figura.config.main_dir": "Figura Folder Location", "figura.config.main_dir.tooltip": "Changes the location of the Figura folder", "figura.config.server_ip": "Figura Cloud IP", "figura.config.server_ip.tooltip": "Sets the IP of the cloud used for Figura's multiplayer features", "figura.config.clear_cache": "<PERSON>ache", "figura.config.clear_cache.tooltip": "Clears the cache files\nRemoves things like cached Avatars and UI states\nWill not clear settings nor saved permissions", "figura.config.redownload_assets": "Redownload Assets", "figura.config.redownload_assets.tooltip": "This button allows for redownloading the runtime assets (emojis, translation files, etc) from the backend\nNormally the assets are downloaded only when the game launches\nThis process might hang the game for a while since a resource reload is executed", "figura.config.clear_avatar_data": "Clear Avatar Data", "figura.config.clear_avatar_data.tooltip": "Clears ALL saved Avatar Data from the ConfigAPI\nRemoves every file present there, from all avatars", "figura.config.force_smooth_avatar": "Force Smooth Avatar", "figura.config.force_smooth_avatar.tooltip": "Force Avatars to always use smooth shading normals", "figura.config.gui_fps": "GUI FPS", "figura.config.gui_fps.tooltip": "Displays the FPS count in the top left of Figura GUIs", "figura.config.networking": "Networking", "figura.config.networking.tooltip": "Section related to Networking features of Figura.\nEverything in this section is dangerous, which is why everything related to it is under 2 switches and a filter.\nFigura does not take any responsibility for the usage of these features.", "figura.config.allow_networking": "Allow Networking", "figura.config.allow_networking.tooltip": "Allow your Figura to work with networking if avatar using networking has needed permissions", "figura.config.networking_restriction": "Networking Restriction", "figura.config.networking_restriction.tooltip": "Sets the type of networking restriction", "figura.config.networking_restriction.1": "Whitelist", "figura.config.networking_restriction.1.tooltip": "Networking can use ONLY links that matches your networking filters", "figura.config.networking_restriction.2": "Blacklist", "figura.config.networking_restriction.2.tooltip": "Networking CAN'T use any of links that matches your networking filters", "figura.config.networking_restriction.3": "None", "figura.config.networking_restriction.3.tooltip": "No links restriction", "figura.config.network_filter": "Network Filter", "figura.config.network_filter.tooltip": "List of network filters", "figura.config.networking_logging": "Networking logging", "figura.config.networking_logging.tooltip": "Networking logging mode", "figura.config.networking_logging.1": "File", "figura.config.networking_logging.1.tooltip": "Logs will be write only in file", "figura.config.networking_logging.2": "File + Logger", "figura.config.networking_logging.2.tooltip": "Logs will be write in file and your logger", "figura.config.networking_logging.3": "File + Logger + Chat", "figura.config.networking_logging.3.tooltip": "Logs will be write in file, your logger, and chat", "figura.config.networking_logging.4": "None", "figura.config.networking_logging.4.tooltip": "No networking logs", "figura.command.no_avatar_error": "No avatar equipped!", "figura.command.no_script_error": "Equipped <PERSON><PERSON> has no active script!", "figura.command.no_renderer_error": "Equipped Avatar has no active renderer!", "figura.command.click_to_open": "Click here to open", "figura.command.load.loading": "Loading avatar!", "figura.command.load.invalid": "Invalid path \"%s\"", "figura.command.docs_export.success": "Exported Figura docs into the Figura directory", "figura.command.docs_export.error": "Failed to export Figura docs!", "figura.command.docs_type_hover": "Click to show %s docs", "figura.command.debug.success": "Saved debug data into the Figura directory", "figura.command.debug.error": "Failed to save debug data!", "figura.command.export_texture.success": "Exported texture into the Figura directory", "figura.command.export_texture.error": "Failed to export texture!", "figura.command.export_avatar.success": "Exported avatar into the Figura directory", "figura.command.export_avatar.error": "Failed to export avatar!", "figura.docs": "The \"/figura docs\" command allows you to access documentation for all types and functions Figura adds to its Lua scripting environment", "figura.docs.text.type": "Type", "figura.docs.text.description": "Description", "figura.docs.text.function": "Function", "figura.docs.text.syntax": "Syntax", "figura.docs.text.returns": "Returns", "figura.docs.text.field": "Field", "figura.docs.text.editable": "Editable", "figura.docs.text.not_editable": "Not Editable", "figura.docs.text.entries": "Entries", "figura.docs.text.aliases": "Aliases", "figura.docs.text.extends": "extends", "figura.docs.enum": "Contains all specific string values used in other functions", "figura.docs.enum.empty": "There are no values in this enumerator!", "figura.docs.enum.keybinds": "List of valid keybind keys\nUsed within the KeybindAPI", "figura.docs.enum.parent_types": "List of valid ParentTypes\nUsed within ModelParts", "figura.docs.enum.render_types": "List of valid RenderTypes\nUsed within ModelParts", "figura.docs.enum.texture_types": "List of valid TextureTypes\nUsed within ModelParts", "figura.docs.enum.key_ids": "List of valid KeyIDs\nUsed within the KeybindAPI", "figura.docs.enum.entity_poses": "List of valid EntityPoses\nUsed within the EntityAPI", "figura.docs.enum.item_display_modes": "List of valid DisplayModes\nUsed within the Item RenderTask", "figura.docs.enum.post_effects": "List of valid PostEffects\nUsed within the RendererAPI", "figura.docs.enum.play_states": "List of valid PlayStates\nUsed within Blockbench animations", "figura.docs.enum.loop_modes": "List of valid LoopModes\nUsed within Blockbench animations", "figura.docs.enum.colors": "List of custom colors\nUsed anywhere you can input a hex color", "figura.docs.enum.player_model_parts": "List of valid PlayerModelParts\nUsed within the PlayerAPI", "figura.docs.enum.use_actions": "List of valid UseActions\nUsed within the ItemStackAPI", "figura.docs.enum.render_modes": "List of valid RenderModes\nUsed within the RENDER event", "figura.docs.enum.string_encodings": "List of valid string encodings\nUsed within Buffers", "figura.docs.enum.block_raycast_types": "List of valid BlockRaycastTypes\nUsed to determine how raycast.block handles blocks", "figura.docs.enum.fluid_raycast_types": "List of valid FluidRaycastTypes\nUsed to determine how raycast.block handles fluids", "figura.docs.enum.heightmap_types": "List of valid HeightmapTypes\nUsed in world.getHeight to select the type of heightmap", "figura.docs.enum.registries": "A list of valid registries.\nUsed in client.getRegistry to select the type of registry", "figura.docs.globals": "Documentation for the various things <PERSON><PERSON><PERSON> adds to the global lua state", "figura.docs.globals.vec": "An alias for \"vectors.vec\"", "figura.docs.globals.require": "The require() function takes the name of one of your scripts, without the .lua extension\nIf this script has not been already run before, it will run that script and return the value that script returns\nIf it has been run before, then it will not run the file again, but it will return the same thing as the first time\nIf a required script has no returns, then require() will return true\nIf the name you give isn't any of your scripts, it will error\nScripts can be accessed relative to the executing script using `./` and `../`", "figura.docs.globals.list_files": "A function that returns a table with all script file names from the specified path\nIf no path is specified, it will fetch from the root folder\nA second argument, boolean, can be given to also list files inside subfolders\nFolders can be accessed relative to the executing script using `./` and `../`", "figura.docs.globals.type": "Figura overrides lua's type() function\nWhen used on Figura types, returns the type's name as seen in the docs and in the figuraMetatables global\nWhen called on a table that has a metatable with a __type key, returns the corresponding value", "figura.docs.globals.print": "A function that writes its arguments to chat\nEven though the syntax says only one argument, you can put as many as you want\nAll of them will print, and they'll be separated by a tab space\nReturns the string representation of all values", "figura.docs.globals.print_table": "The first argument is either a Table, or it's a Userdata, which refers to any of the added Figura types\nPrints the table out to chat, specially formatted\nIf userdata is passed in, it is automatically converted to a table or string, and displayed\nIn the case of tables inside of tables, the \"maxDepth\" parameter will be used to determine how far to go\nDefault value for maxDepth is 1\nThird argument, \"silent\", will skip the chat print and will only return the string", "figura.docs.globals.print_json": "Takes a Minecraft JSON string as an argument, and prints it to the chat formatted, without the lua print header\nEven though the syntax says only one argument, you can put as many as you want\nAll of them will print, and they'll be appended in the end of the previous argument\nReturns the formatted string", "figura.docs.globals.parse_json": "Takes a JSON string as an argument, and converts it into a lua value.\nObjects and arrays are converted to tables appropriately.", "figura.docs.globals.to_json": "Takes any lua value, and converts it into a json string.\nDoes not accept functions, they will be treated as null.", "figura.docs.globals.action_wheel": "The global instance of the ActionWheelAPI and its subtypes", "figura.docs.globals.animations": "The global instance of the AnimationsAPI and its subtypes", "figura.docs.globals.figura_metatables": "A table containing all the metatables for Figura's object types\nThe metatables are editable", "figura.docs.globals.nameplate": "The global instance of the NameplateAPI and its subtypes", "figura.docs.globals.world": "The global instance of the WorldAPI and its subtypes", "figura.docs.globals.vanilla_model": "The global instance of the VanillaModelAPI and its subtypes", "figura.docs.globals.models": "The global instance of the Avatar Models and its subtypes", "figura.docs.globals.player": "The global instance of the EntityAPI and its subtypes", "figura.docs.globals.events": "The global instance of the EventsAPI and its subtypes", "figura.docs.globals.keybinds": "The global instance of the KeybindAPI and its subtypes", "figura.docs.globals.vectors": "The global instance of the VectorsAPI and its subtypes", "figura.docs.globals.matrices": "The global instance of the MatricesAPI and its subtypes", "figura.docs.globals.particles": "The global instance of ParticleAPI", "figura.docs.globals.sounds": "The global instance of SoundAPI", "figura.docs.globals.client": "The global instance of ClientAPI", "figura.docs.globals.host": "The global instance of HostAPI", "figura.docs.globals.avatar": "The global instance of AvatarAPI", "figura.docs.globals.data": "The global instance of DataAPI", "figura.docs.globals.net": "The global instance of NetworkingAPI", "figura.docs.globals.json": "The global instance of JsonAPI", "figura.docs.globals.file": "The global instance of FileAPI", "figura.docs.globals.resources": "The global instance of ResourcesAPI", "figura.docs.globals.renderer": "The global instance of RendererAPI", "figura.docs.globals.user": "An alias for player, just in case the user of your avatar isn't a player\n(For<PERSON><PERSON><PERSON>?)", "figura.docs.globals.pings": "The global instance of PingAPI", "figura.docs.globals.textures": "The global instance of the TextureAPI", "figura.docs.globals.config": "The global instance of the ConfigAPI", "figura.docs.globals.raycast": "The global instance of RaycastAPI", "figura.docs.math": "Contains functions which <PERSON><PERSON><PERSON> adds to the default Lua \"math\" library table", "figura.docs.math.player_scale": "The constant of the player scaling related to the world", "figura.docs.math.world_scale": "The constant of the world scaling related with the player", "figura.docs.math.lerp": "Linearly interpolates from its first argument to its second argument, with the third argument as the parameter\nWorks on both regular numbers, vectors of the same type, and matrices of the same type", "figura.docs.math.clamp": "Clamps the given value between min and max", "figura.docs.math.round": "Rounds the given number to the nearest whole integer", "figura.docs.math.map": "Maps the given value from one range to another\nFor example, if you have a value of 20 in the range 0-200, and you want to map it to the range 100-200, the result will be 110", "figura.docs.math.short_angle": "Returns the shortest angle between two angles\nFor example, if you have an angle of 350 degrees and you want to get the shortest angle between it and 0 degrees, the result will be 10 degrees", "figura.docs.math.lerp_angle": "Similar to the default lerp function, but numbers are limited to the range of 0-360\nLerp is done towards the shortest angle\nFor example, a lerp of 340 and 20, with a factor of 0.75, will return 10", "figura.docs.math.sign": "Returns the sign of the given number\nReturns 1 if the number is positive, -1 if it's negative, and 0 if it's 0", "figura.docs.action_wheel": "A global API which is used for customizing your player's Action Wheel", "figura.docs.action_wheel.left_click": "Function that is executed when the left mouse button is clicked", "figura.docs.action_wheel.right_click": "Function that is executed when the right mouse button is clicked", "figura.docs.action_wheel.scroll": "Function that is executed when the mouse is scrolled", "figura.docs.action_wheel.execute": "Executes the action of the given index\nIf the index is null, it will execute the last selected action\nA second parameter can be given to specify if it should be executed the left or right action", "figura.docs.action_wheel.is_enabled": "Returns if the Action Wheel is being currently rendered or not", "figura.docs.action_wheel.get_selected": "Returns the index of the currently selected action", "figura.docs.action_wheel.get_selected_action": "Returns the Action that is currently selected in the Action Wheel", "figura.docs.action_wheel.new_action": "Creates a new Action that is not automatically assigned to a Page", "figura.docs.action_wheel.new_page": "Creates a new Page for the action wheel\nA Title can be given to store this page internally\nIf no Title is given, the Page will just be returned from this function", "figura.docs.action_wheel.set_page": "Sets the Page of the action wheel to the given Title or Page", "figura.docs.action_wheel.get_page": "Returns a stored Page by the given title\nIf no title is given, returns a table with all registered Pages", "figura.docs.action_wheel.get_current_page": "Returns the current set Page from the Action Wheel, or NIL if no Page has been set", "figura.docs.wheel_page": "An Action Wheel container which is used to store up to 8 actions", "figura.docs.wheel_page.keep_slots": "Whether or not this page's current slots should be reset when adding this page on the Action Wheel", "figura.docs.wheel_page.should_keep_slots": "Gets if this page's current slots should be reset when adding this page on the Action Wheel", "figura.docs.wheel_page.set_keep_slots": "Sets if this page's current slots should be reset when adding this page on the Action Wheel", "figura.docs.wheel_page.get_title": "Returns the title of this Page", "figura.docs.wheel_page.new_action": "Adds a new Action on the selected Page's index\nIf no index is given it is added in the first available index", "figura.docs.wheel_page.get_action": "Gets an Action from the Page's given index", "figura.docs.wheel_page.set_action": "Sets an Action in the Page's given index\nIf index is -1, the action will be set in the latest empty index", "figura.docs.wheel_page.get_slots_shift": "Gets this page's current slots shift\nThis value is the current shown slots, divided by 8, rounded up", "figura.docs.wheel_page.set_slots_shift": "Sets this page's current slots shift\nThe value is the current shown slots, divided by 8, rounded up", "figura.docs.wheel_page.get_actions": "Gets a table of the Actions shifted by the given number\nIf no shift is specified, it returns all Actions from this page\nNote that the table is indexed as its slot number as key, so blank slots will result in nil values", "figura.docs.wheel_action": "An action in the Figura Action Wheel\nActions are either interacted by clicking and scrolling, which also being able to be toggleable", "figura.docs.wheel_action.left_click": "Function that is executed when the left mouse button is clicked\nThe function has one argument\nThe first argument is this action itself", "figura.docs.wheel_action.right_click": "Function that is executed when the right mouse button is clicked\nThe function has one argument\nThe first argument is this action itself", "figura.docs.wheel_action.toggle": "Function that is executed when the Action is toggled\nThe function has two arguments\nThe first argument is toggle state of this action\nThe second argument is this action itself", "figura.docs.wheel_action.untoggle": "Function that is executed when the Action is untoggled\nThe function has two arguments\nThe first argument is toggle state of this action\nThe second argument is this action itself", "figura.docs.wheel_action.scroll": "Function that is executed when the mouse is scrolled\nThe function has two arguments\nThe first argument is mouse wheel direction\nThe second argument is this action itself", "figura.docs.wheel_action.get_title": "Gets this Action title", "figura.docs.wheel_action.set_title": "Sets the title of the Action", "figura.docs.wheel_action.get_color": "Gets this Action color", "figura.docs.wheel_action.set_color": "Sets the color of the Action", "figura.docs.wheel_action.get_hover_color": "Gets this Action hover color", "figura.docs.wheel_action.set_hover_color": "Sets the color of the Action when it is being hovered", "figura.docs.wheel_action.set_item": "Sets the item of the Action", "figura.docs.wheel_action.set_hover_item": "Sets the item of the Action when it is being hovered", "figura.docs.wheel_action.set_texture": "Sets a Custom Texture to render in this Action\nAll values are measured in pixels\nDefault UV is 0, 0, the default dimensions are the texture dimensions and the default scale of 1", "figura.docs.wheel_action.set_hover_texture": "Sets a Custom Texture to render while this Action is being hovered\nAll values are measured in pixels\nDefault UV is 0, 0, the default dimensions are the texture dimensions and the default scale of 1", "figura.docs.wheel_action.set_on_left_click": "Sets the function that is executed when the left mouse button is clicked\nThe function has one argument\nThe first argument is this action itself", "figura.docs.wheel_action.set_on_right_click": "Sets the function that is executed when the right mouse button is clicked\nThe function has one argument\nThe first argument is this action itself", "figura.docs.wheel_action.set_on_toggle": "Sets the function that is executed when the Action is toggled\nThe function has two arguments\nThe first argument is toggle state of this action\nThe second argument is this action itself", "figura.docs.wheel_action.set_on_untoggle": "Sets the function that is executed when the Action is untoggled\nThe function has two arguments\nThe first argument is toggle state of this action\nThe second argument is this action itself", "figura.docs.wheel_action.set_on_scroll": "Sets the function that is executed when the mouse is scrolled\nThe function has two arguments\nThe first argument is mouse wheel direction\nThe second argument is this action itself", "figura.docs.wheel_action.get_toggle_title": "Gets this Action toggled title", "figura.docs.wheel_action.set_toggle_title": "Sets the title of the Action when it is toggled", "figura.docs.wheel_action.get_toggle_color": "Gets this Action toggled color", "figura.docs.wheel_action.set_toggle_color": "Sets the color of the Action when it is toggled", "figura.docs.wheel_action.set_toggle_item": "Sets the item of the Action when it is toggled", "figura.docs.wheel_action.set_toggle_texture": "Sets a Custom Texture to render in this Action when it is toggled\nAll values are measured in pixels\nDefault UV is 0, 0, the default dimensions are the texture dimensions and the default scale of 1", "figura.docs.wheel_action.is_toggled": "Checks if the Action is toggled or not", "figura.docs.wheel_action.set_toggled": "Sets the toggle state of the Action", "figura.docs.animations": "A global API used for control of Blockbench Animations", "figura.docs.animations.get_animations": "Returns a table with all animations", "figura.docs.animations.get_playing": "Return a table with all playing animations\nTakes a boolean parameter, where if true, animations in the HOLDING play state will be included in the table", "figura.docs.animations.stop_all": "Stops all playing (and paused) animations", "figura.docs.animation": "A Blockbench animation", "figura.docs.animation.name": "This animation's name", "figura.docs.animation.is_playing": "Checks if this animation is being played", "figura.docs.animation.is_paused": "Checks if this animation is paused", "figura.docs.animation.is_stopped": "Checks if this animation is stopped", "figura.docs.animation.is_holding": "Checks if this animation is holding on its last frame", "figura.docs.animation.play": "Initializes the animation\nResume the animation if it was paused", "figura.docs.animation.pause": "Pause the animation's playback", "figura.docs.animation.stop": "Stop the animation", "figura.docs.animation.restart": "Restarts the animation\nPlays the animation if it was stopped\nThis behavior can also be reproduced by stopping then playing the animation", "figura.docs.animation.set_playing": "A function that merges \"play\" and \"stop\" together\nTakes a boolean parameter, where if true, the animation will play, and when false, the animation will stop", "figura.docs.animation.get_time": "Get the animation's playback current time", "figura.docs.animation.set_time": "Sets the animation's playback current time", "figura.docs.animation.get_play_state": "Get the animation's playback state", "figura.docs.animation.new_code": "Adds a string to run in a determinant time\nIf there's already code to run at that time, it is overwritten", "figura.docs.animation.get_blend": "Gets the animation's keyframe blend factor", "figura.docs.animation.set_blend": "Sets the animation's keyframe blend factor", "figura.docs.animation.get_offset": "Gets the animation's offset time", "figura.docs.animation.set_offset": "Sets how much time to skip for the animation\nThe time is skipped on every loop", "figura.docs.animation.get_start_delay": "Gets the animation's start delay", "figura.docs.animation.set_start_delay": "Set how much time to wait before this animation is initialized\nNote that while it is waiting, the animation is considered being played", "figura.docs.animation.get_loop_delay": "Gets the animation's loop delay", "figura.docs.animation.set_loop_delay": "Set how much time to wait in between the loops of this animation", "figura.docs.animation.get_length": "Gets the animation's length", "figura.docs.animation.set_length": "Set the animation's length, in seconds", "figura.docs.animation.set_override": "Set if this animation should override all of its parts vanilla transforms\nEquivalent of calling \"overrideRot\", \"overridePos\" and \"overrideScale\" altogether", "figura.docs.animation.get_override_rot": "Gets if this animation should override its parts vanilla rotation", "figura.docs.animation.get_override_pos": "Gets if this animation should override its parts vanilla position", "figura.docs.animation.get_override_scale": "Gets if this animation should override its parts vanilla scale", "figura.docs.animation.set_override_rot": "Set if this animation should override its parts vanilla rotation", "figura.docs.animation.set_override_pos": "Set if this animation should override its parts vanilla position", "figura.docs.animation.set_override_scale": "Set if this animation should override its parts vanilla scale", "figura.docs.animation.get_loop": "Gets the animation's loop mode", "figura.docs.animation.set_loop": "Sets the animation's loop mode", "figura.docs.animation.get_priority": "Gets the animation's priority", "figura.docs.animation.set_priority": "Sets the animation's priority\nInstead of blending, low priority animations are overridden by high priority ones", "figura.docs.animation.get_speed": "Gets the animation's speed", "figura.docs.animation.set_speed": "Sets the animation's playback speed\nNegative numbers can be used for an inverted animation", "figura.docs.animation.get_name": "Returns this animation's name", "figura.docs.avatar": "A global API containing functions to interact with your avatar's metadata, and also to get information about the current script environment", "figura.docs.avatar.store": "Store the given key-value pair inside your current avatar's metadata\nSomeone else can get this information from a different script with the avatarVars() function in World\nThe key must be a string", "figura.docs.avatar.get_uuid": "Returns the UUID of the owner of this avatar", "figura.docs.avatar.get_current_instructions": "Gets the current number of instructions that have been executed by your avatar\nResets to 0 at the beginning of certain events", "figura.docs.avatar.get_color": "Gets the current color string of your avatar, used as your avatar theme", "figura.docs.avatar.set_color": "Sets the current color string of your avatar, used as your avatar theme, if the user has a special badge, they can pass in the badge's name to set the color for it.", "figura.docs.avatar.get_badges": "Gets the user's badges in a string.", "figura.docs.avatar.get_version": "Gets the version string of this avatar", "figura.docs.avatar.get_authors": "Gets the authors string of this avatar", "figura.docs.avatar.get_name": "Gets the name string of this avatar", "figura.docs.avatar.get_entity_name": "Attempts to get the entity name of this avatar, defaulting to the avatar name", "figura.docs.avatar.get_size": "Gets the file size of this avatar in bytes", "figura.docs.avatar.has_texture": "Gets whether or not this avatar has a texture", "figura.docs.avatar.has_script_error": "Gets whether this script currently has stopped due to an error (kinda useless lol)", "figura.docs.avatar.get_permission_level": "Returns this avatar's current permission level", "figura.docs.avatar.get_init_count": "Gets the number of initialization instructions of this avatar", "figura.docs.avatar.get_entity_init_count": "Gets the number of events.ENTITY_INIT instructions of this avatar", "figura.docs.avatar.get_max_init_count": "Gets the maximum allowed instructions during initialization as permitted by the viewer", "figura.docs.avatar.get_tick_count": "Gets the number of events.TICK instructions of this avatar last tick", "figura.docs.avatar.get_max_tick_count": "Gets the maximum allowed instructions in events.TICK as permitted by the viewer", "figura.docs.avatar.get_render_count": "Gets the number of events.RENDER and events.POST_RENDER instructions of this avatar's last frame", "figura.docs.avatar.get_max_render_count": "Gets the maximum allowed instructions in events.RENDER and Events.POST_RENDER as permitted by the viewer", "figura.docs.avatar.get_world_tick_count": "Gets the number of events.WORLD_TICK instructions of this avatar's last tick", "figura.docs.avatar.get_max_world_tick_count": "Gets the maximum allowed instructions in events.WORLD_TICK as permitted by the viewer", "figura.docs.avatar.get_world_render_count": "Gets the number of events.WORLD_RENDER and events.POST_WORLD_RENDER instructions of this avatar's last frame", "figura.docs.avatar.get_max_world_render_count": "Gets the maximum allowed instructions in events.WORLD_RENDER and events.POST_WORLD_RENDER as permitted by the viewer", "figura.docs.avatar.get_complexity": "Gets the current complexity of this avatar", "figura.docs.avatar.get_max_complexity": "Gets the maximum allowed model complexity (number of faces) as permitted by the viewer", "figura.docs.avatar.get_remaining_particles": "Gets the remaining amount of particles this avatar can summon", "figura.docs.avatar.get_max_particles": "Gets the maximum allowed number of particles as permitted by the viewer", "figura.docs.avatar.get_remaining_sounds": "Gets the remaining amount of sound this avatar can play", "figura.docs.avatar.get_max_sounds": "Gets the maximum allowed number of sounds as permitted by the viewer", "figura.docs.avatar.get_animation_complexity": "Gets the current animation complexity of this avatar", "figura.docs.avatar.get_max_animation_complexity": "Gets the maximum allowed animation complexity (number of playing channels) as permitted by the viewer", "figura.docs.avatar.get_animation_count": "Gets the number of Lua instructions used within animations", "figura.docs.avatar.get_max_animation_count": "Gets the maximum allowed Lu<PERSON> instructions in animations as permitted by the viewer", "figura.docs.avatar.get_max_texture_size": "Gets the max dimensions of custom textures that this avatar can create", "figura.docs.avatar.get_buffers_count": "Returns count of buffers open by this avatar", "figura.docs.avatar.get_max_buffers_count": "Returns maximum allowed count of buffers can be open by this avatar", "figura.docs.avatar.get_sockets_count": "Returns count of sockets open by this avatar", "figura.docs.avatar.get_max_sockets_count": "Returns maximum allowed count of sockets can be open by this avatar", "figura.docs.avatar.can_edit_vanilla_model": "Gets whether or not the viewer allows your avatar to edit the vanilla models", "figura.docs.avatar.can_edit_nameplate": "Gets whether or not the viewer allows your avatar to edit your nameplate", "figura.docs.avatar.can_render_offscreen": "Gets whether or not the viewer allows you to render the avatar off-screen", "figura.docs.avatar.can_use_custom_sounds": "Gets whether or not the viewer allows your avatar to play custom sounds", "figura.docs.avatar.can_have_custom_skull": "Gets whether or not your avatar is allowed to have custom player skull", "figura.docs.avatar.get_nbt": "Gets the NBT data this avatar is stored as", "figura.docs.avatar.get_max_buffer_size": "Gets the max byte buffer size that this avatar can have", "figura.docs.biome": "A proxy for a Minecraft biome\nInstances are obtained through the WorldAPI\nThis proxy also contains a saved position for the Biome", "figura.docs.biome.id": "The id of this biome", "figura.docs.biome.get_id": "Returns the id of this biome", "figura.docs.biome.get_pos": "Returns the saved position for this Biome's proxy\nThe saved position is used in Biome functions that require a position", "figura.docs.biome.set_pos": "Sets the saved position for this Biome's proxy\nThe saved position is used in Biome functions that require a position", "figura.docs.biome.get_tags": "Gets the worldgen related tags from this Biome", "figura.docs.biome.get_temperature": "Gets the temperature of this biome", "figura.docs.biome.get_precipitation": "Gets the rain type of this biome\nThe type can be \"NONE\", \"RAIN\" or \"SNOW\"", "figura.docs.biome.get_sky_color": "Gets this biome's sky color as an RGB vector", "figura.docs.biome.get_foliage_color": "Gets this biome's foliage color as an RGB vector", "figura.docs.biome.get_grass_color": "Gets this biome's grass color as an RGB vector", "figura.docs.biome.get_fog_color": "Gets this biome's fog color as an RGB vector", "figura.docs.biome.get_water_color": "Gets this biome's water color as an RGB vector", "figura.docs.biome.get_water_fog_color": "Gets this biome's water fog color as an RGB vector", "figura.docs.biome.get_downfall": "Gets this biome's humidity", "figura.docs.biome.is_hot": "Checks if this biome is hot", "figura.docs.biome.is_cold": "Checks if this biome is cold", "figura.docs.blockstate": "A proxy for a block state from Minecraft\nInstances are obtained through the WorldAPI\nThis proxy also contains a saved position for the BlockState", "figura.docs.blockstate.id": "The identifier of the block this BlockState comes from", "figura.docs.blockstate.properties": "A table containing the properties of this BlockState\nIf this BlockState has no properties, it is nil", "figura.docs.blockstate.get_id": "Returns this BlockState identifier", "figura.docs.blockstate.get_properties": "Returns a table with the properties of this BlockState", "figura.docs.blockstate.get_pos": "Returns the saved position for this BlockState proxy\nThe saved position is used in BlockState functions that require a position", "figura.docs.blockstate.set_pos": "Sets the saved position for this BlockState proxy\nThe saved position is used in BlockState functions that require a position", "figura.docs.blockstate.is_translucent": "Gets whether or not the BlockState would propagate sky light downwards", "figura.docs.blockstate.get_opacity": "Gets the opacity of the BlockState, in terms of how much it affects light levels", "figura.docs.blockstate.get_map_color": "Gets the map color of this BlockState, as a Vector3 with R,G,B ranging 0 to 1", "figura.docs.blockstate.is_solid_block": "Gets whether or not the BlockState is considered a \"solid\" block by Minecraft", "figura.docs.blockstate.is_full_cube": "Gets whether or not the BlockState has a full cube as its collision hitbox", "figura.docs.blockstate.has_emissive_lighting": "Gets whether or not the BlockState uses emissive lighting", "figura.docs.blockstate.get_hardness": "Gets the hardness of the BlockState", "figura.docs.blockstate.get_comparator_output": "Gets the amount of signal strength a comparator would get from this BlockState", "figura.docs.blockstate.has_block_entity": "Gets whether or not this BlockState has an associated block entity", "figura.docs.blockstate.is_opaque": "Gets whether or not this BlockState is opaque", "figura.docs.blockstate.emits_redstone_power": "Gets whether or not this BlockState emits redstone power", "figura.docs.blockstate.get_luminance": "Gets the emission light level of this BlockState", "figura.docs.blockstate.get_friction": "Gets the friction of this BlockState\n(Slime blocks and ice in vanilla)", "figura.docs.blockstate.get_velocity_multiplier": "Gets the velocity multiplier of this BlockState\n(Only Soul sand, honey blocks in vanilla)", "figura.docs.blockstate.get_jump_velocity_multiplier": "Gets the jump velocity multiplier of this BlockState\n(Literally just honey blocks in vanilla)", "figura.docs.blockstate.get_blast_resistance": "Gets the blast resistance of this BlockState", "figura.docs.blockstate.as_item": "Returns an ItemStack representing this block in item form, whatever Minecraft deems that to be\nIf it cannot find an item for this block, it will return nil", "figura.docs.blockstate.get_tags": "Returns a table containing all the tags of this block, as strings", "figura.docs.blockstate.has_collision": "Returns true if this block has collision", "figura.docs.blockstate.get_collision_shape": "Returns a table representing the bounding boxes of the collision shape\nThe table a list of every shape, containing two Vector3, where the first vector is one corner of the box and the other vector is the other corner", "figura.docs.blockstate.get_outline_shape": "Returns a table representing the bounding boxes of the outline shape\nThe table a list of every shape, containing two Vector3, where the first vector is one corner of the box and the other vector is the other corner", "figura.docs.blockstate.get_sounds": "Gets the names of all the sounds which can play from this BlockState, as well as their pitch and volume\nStored in a table", "figura.docs.blockstate.get_fluid_tags": "Returns a table containing all the fluid tags of this block, as strings", "figura.docs.blockstate.get_entity_data": "Returns the nbt of the Block Entity associated with this BlockState, at its position, as a table\nSince the mod is only on client side, this NBT might not actually contain the real nbt, which is stored server-side", "figura.docs.blockstate.to_state_string": "Converts this BlockState into a string, like you'd see in a Minecraft command", "figura.docs.blockstate.get_textures": "Returns a List of this BlockState's textures\nThe keys represents the Culling Direction, while the values is another table with all texture paths (string)\nNote this only works for normal blocks, special blocks (water, beds, …) will return an empty table", "figura.docs.blockstate.is_air": "Checks if this block is air", "figura.docs.client": "A global API used to interact with the current Minecraft client\nMost of its functions return things that can be found in the F3 menu", "figura.docs.client.get_fps": "Gets the FPS of the client\nReturns 0 if the fps counter isn't ready yet (or if your PC is just that bad)", "figura.docs.client.get_fps_string": "Gets the FPS string of the client, displayed in the F3 menu\nContains info on the fps, the fps limit, vsync, cloud types, and biome blend radius", "figura.docs.client.is_paused": "Returns true if the client is paused", "figura.docs.client.get_version": "Returns the Minecraft version of your client", "figura.docs.client.get_version_name": "Returns the extended name of the Minecraft version of your client", "figura.docs.client.is_snapshot": "Returns if the client is running a snapshot or full release version", "figura.docs.client.get_client_brand": "Returns the \"version type\" of your client, usually the client modloader brand", "figura.docs.client.get_server_brand": "Returns the type of server you're on\nIn singleplayer, this is \"Integrated\"", "figura.docs.client.get_chunk_statistics": "Returns a string containing information about the player's chunk\nThis string appears in the F3 menu", "figura.docs.client.get_entity_statistics": "Returns a string containing information about the loaded entities on the client\nThis string appears in the F3 menu", "figura.docs.client.get_sound_statistics": "Returns a string containing information about the currently playing sounds on the client\nThis string appears in the F3 menu", "figura.docs.client.get_entity_count": "Returns the number of currently loaded entities", "figura.docs.client.get_particle_count": "Returns the number of currently loaded particles", "figura.docs.client.get_current_effect": "Returns the path to the currently applied shader, used when spectating an entity that has different vision than normal\nNormally returns nil", "figura.docs.client.get_java_version": "Returns your current Java version you're playing Minecraft with", "figura.docs.client.get_used_memory": "Returns the number of bytes of memory Minecraft is currently using", "figura.docs.client.get_max_memory": "Returns the maximum amount of memory that Minecraft will try to use", "figura.docs.client.get_allocated_memory": "Returns the maximum amount of memory that Minecraft could possibly use", "figura.docs.client.is_window_focused": "Returns true if the Minecraft window is currently focused", "figura.docs.client.is_hud_enabled": "Returns true if the hud is enabled (F1 disables the HUD)", "figura.docs.client.is_debug_overlay_enabled": "Returns true if the F3 screen is currently open", "figura.docs.client.get_window_size": "Returns the size of the Minecraft window in pixels, as {width, height}", "figura.docs.client.get_fov": "Returns the current FOV option of the client, not including additional effects such as speed or sprinting", "figura.docs.client.get_system_time": "Returns the current system time in milliseconds", "figura.docs.client.get_mouse_pos": "Returns the position of the mouse in pixels, relative to the top-left corner", "figura.docs.client.get_scaled_window_size": "Returns the size of the window in Minecraft's internal GUI units", "figura.docs.client.get_gui_scale": "Returns the current value of your Gui Scale setting\nIf you use auto, then it gets the actual current scale", "figura.docs.client.get_camera_pos": "Returns the position of the viewer's camera", "figura.docs.client.get_camera_rot": "Returns the rotation of the viewer's camera", "figura.docs.client.get_camera_dir": "Returns a unit vector pointing in the direction that the camera is facing", "figura.docs.client.get_text_width": "Returns the width of the given text in pixels\nIn case of multiple lines, return the largest width of all lines", "figura.docs.client.get_text_height": "Returns the height of the given text in pixels", "figura.docs.client.get_text_dimensions": "Returns the width and height of the given text, wrapping lines and trimming to a max width", "figura.docs.client.get_active_lang": "Returns a string representation of the current game language", "figura.docs.client.is_mod_loaded": "Checks if the client has loaded the given mod ID", "figura.docs.client.has_shader_pack_mod": "Checks if the client has a Shader Pack mod installed", "figura.docs.client.has_shader_pack": "Checks if the client is currently using a Shader Pack", "figura.docs.client.get_shader_pack_name": "Returns a string with the current shader pack name, empty if none is being used.", "figura.docs.client.has_resource": "Checks if the specific resource exists within the client resources", "figura.docs.client.get_active_resource_packs": "Returns a table with the active resource pack names", "figura.docs.client.get_figura_version": "Gets the client Figura version", "figura.docs.client.compare_versions": "Compares two versions if they are less than (-1), equals (0) or greater than (1)", "figura.docs.client.generate_uuid": "Generates a random UUID", "figura.docs.client.int_uuid_to_string": "Converts an int array UUID (as 4 separated arguments) into its hexadecimal string representation", "figura.docs.client.uuid_to_int_array": "Converts a string UUID into its int array representation, returning a varargs of 4 ints", "figura.docs.client.get_viewer": "Returns the player entity from the running client", "figura.docs.client.get_camera_entity": "Returns the entity the camera is currently targeting, so returns the entity you are currently spectating, including yourself", "figura.docs.client.get_server_data": "Returns a table with information on the currently connected server (also for singleplayer worlds)", "figura.docs.client.get_date": "Returns a table with information about the client's current time", "figura.docs.client.get_registry": "Returns a list of all values in the specified registry\nSee the `registries` enum for a list of valid registries", "figura.docs.client.get_frame_time": "Returns the current fraction between the last tick and the next tick\nThis is the value used as \"delta\" in the RENDER event", "figura.docs.client.get_actionbar": "Returns the current actionbar text, or nil if the action bar isn't visible", "figura.docs.client.get_title": "Returns the current title text, or nil if the title isn't visible", "figura.docs.client.get_subtitle": "Returns the current subtitle text, or nil if the title or subtitle isn't visible", "figura.docs.client.get_scoreboard": "Returns data about the current scoreboard(s). Multiple scoreboards can be visible at the same time (sidebar, team sidebar, list, and below name), so each scoreboard is grouped by its display location", "figura.docs.client.list_atlases": "Returns a list of all registered atlases paths", "figura.docs.client.get_atlas": "Returns a TextureAtlasAPI object with information about the given atlas\nReturns nil if the atlas was not found", "figura.docs.client.get_enum": "Return an array containing the entries in the given enum\nEnums can be found in /figura docs enums", "figura.docs.client.get_tab_list": "Returns a table with the text shown in the tablist", "figura.docs.client.get_translated_string": "Returns the translated string of the given key\nTranslation is done using the current client language\nOptionally takes a single argument, or a list with all arguments, that will populate the translation", "figura.docs.config": "A global API used to save and load avatar data between game sessions", "figura.docs.config.get_name": "Returns the name of the destination file", "figura.docs.config.set_name": "Sets the name of the destination file, data will be saved and loaded from that file\nDefaults to the avatar name", "figura.docs.config.save": "Save to disk a variable under the specific key\nIf the value is nil, the variable is removed from the file", "figura.docs.config.load": "Loads a saved variable under the specific key\nIf no key is given, it will return a table with all saved variables", "figura.docs.entity": "Acts as a proxy for an entity in the Minecraft world", "figura.docs.entity.is_loaded": "Checks if this entity object is still being updated and loaded\nA non-loaded entity would be someone or something which is in another dimension or out of the render distance for example", "figura.docs.entity.get_pos": "Gets the position of the entity in the world\nIf delta is passed in, then it will be used to linearly interpolate the position of the entity between the previous tick and the current tick\nThe default value of delta is 1", "figura.docs.entity.get_rot": "Gets the rotation of the entity in degrees\nIf delta is passed in, then it will be used to linearly interpolate the rotation of the entity between the previous tick and the current tick\nThe default value of delta is 1", "figura.docs.entity.get_uuid": "Gets the UUID of the proxied entity", "figura.docs.entity.get_type": "Gets the Minecraft identifier of this entity\nFor instance, \"minecraft:pig\"", "figura.docs.entity.get_velocity": "Gets the current velocity of this entity in world coordinates, calculated as its position this tick minus its position last tick", "figura.docs.entity.get_look_dir": "Returns a unit vector pointing in the direction that this entity is looking\nSee the blue line in the F3+B screen for an example", "figura.docs.entity.get_frozen_ticks": "Gets the number of ticks this entity has been freezing in powder snow for", "figura.docs.entity.get_max_air": "Gets the maximum amount of air this entity can have", "figura.docs.entity.get_dimension_name": "Gets the Minecraft identifier of the dimension this entity is in", "figura.docs.entity.get_pose": "Returns the current pose of the player\nThis can be one of: \"STANDING\", \"FALL_FLYING\", \"SLEEPING\", \"SWIMMING\", \"SPIN_ATTACK\", \"CROUCHING\", \"LONG_JUMPING\", or \"DYING\"", "figura.docs.entity.get_vehicle": "Returns a proxy for the entity that this player is currently riding\nIf the player isn't riding anything, returns nil", "figura.docs.entity.is_on_ground": "Returns whether or not this entity is currently on the ground", "figura.docs.entity.get_eye_height": "Returns the current eye height of this entity", "figura.docs.entity.get_bounding_box": "Returns the size of this entity's bounding box as a Vector3\n{x, y, z} are the width, height, and width\nMinecraft entity hitboxes always have square bases", "figura.docs.entity.get_name": "Gets the name of this entity, if it has a custom name\nIf it doesn't, returns a translated form of getType()", "figura.docs.entity.is_wet": "Returns true in any of three conditions: if the entity is in water, if the entity is in rain, or if the entity is in a bubble column\nOtherwise, returns false", "figura.docs.entity.is_in_water": "Returns true if this entity is currently in a water block, including waterlogging", "figura.docs.entity.is_underwater": "Returns true if this entity's eyes are touching water", "figura.docs.entity.is_in_lava": "Returns true if this entity is currently in lava", "figura.docs.entity.is_in_rain": "Returns true if this entity is currently standing in rain", "figura.docs.entity.has_avatar": "Returns true if <PERSON><PERSON><PERSON> has an avatar loaded for this entity", "figura.docs.entity.is_sprinting": "Returns true if this entity is currently sprinting", "figura.docs.entity.get_eye_y": "Returns the Y level of this entity's eyes\nNot to be confused with getEyeHeight, this function also takes the entity itself's Y position into account", "figura.docs.entity.is_glowing": "Returns true if this entity is currently glowing", "figura.docs.entity.is_invisible": "Returns true if this entity is invisible, for one reason or another", "figura.docs.entity.is_silent": "Returns true if this entity is silent", "figura.docs.entity.is_sneaking": "Returns true if this entity is logically sneaking (can't fall from blocks edges, can't see nameplate behind walls, etc)", "figura.docs.entity.is_crouching": "Returns true if this entity is visually sneaking", "figura.docs.entity.is_moving": "Returns true if this entity has some velocity\nTakes a boolean parameter, where if true, the y velocity is ignored", "figura.docs.entity.is_falling": "Returns true if this entity has negative Y-velocity and is not on the ground", "figura.docs.entity.get_item": "Gets an ItemStack for the item in the given slot\nFor the player, slots are indexed with 1 as the main hand, 2 as the off hand, and 3,4,5,6 as the 4 armor slots from the boots to the helmet\nIf an invalid slot number is given, this will return nil", "figura.docs.entity.get_nbt": "Gets a table containing the NBT of this entity\nPlease note that not all values in the entity's NBT may be synced, as some are handled only on the server side", "figura.docs.entity.is_on_fire": "Returns true if this entity is currently on fire", "figura.docs.entity.is_alive": "Returns whether this entity is alive or not", "figura.docs.entity.get_permission_level": "Returns the permission level number of this entity\nServer Operators, by default, have the permission level of 4", "figura.docs.entity.get_passengers": "Returns a List of entities of all passengers this entity has", "figura.docs.entity.get_controlling_passenger": "Returns the entity that is controlling this entity", "figura.docs.entity.get_controlled_vehicle": "Return the vehicle that this entity is controlling", "figura.docs.entity.has_container": "Checks if the entity has a container (Chest Boats, Minecarts with Chests, …)", "figura.docs.entity.has_inventory": "Checks if the entity has an inventory (Horses, Camels, LLamas, …)", "figura.docs.entity.get_targeted_block": "Returns a proxy for your currently targeted BlockState\nThis BlockState appears on the F3 screen\nThe maximum (and default) distance is 20, minimum is -20\nReturns the block, the hit position, and the targeted block face as three separate values", "figura.docs.entity.get_targeted_entity": "Returns a proxy for your currently targeted Entity\nThis Entity appears on the F3 screen\nMaximum and Default distance is 20, Minimum is 0", "figura.docs.entity.get_nearest_entity": "Returns the closest entity to this entity\nIf `type` is an entity id, (e.g. `minecraft:bee`), only entities of that type will be considered\nRadius defaults to 20, and controls the size of the area for checking entities as a box expanding in every direction from the player", "figura.docs.entity.get_variable": "Gets the value of a variable this entity stored in themselves using the Avatar API's store() function", "figura.docs.entity.is_living": "Gets if this entity is a Living Entity", "figura.docs.entity.is_player": "Gets if this entity is a Player Entity", "figura.docs.living_entity": "Acts as a proxy for a living entity in the Minecraft world", "figura.docs.living_entity.get_body_yaw": "Gets the yaw of this entity's body in degrees\nIf delta is passed in, then it will be used to linearly interpolate the rotation of the body between the previous tick and the current tick\nThe default value of delta is 1", "figura.docs.living_entity.get_held_item": "Returns an ItemStack representing the item in this entity's main hand\nIf true is passed in for \"offhand\", then it will instead look at the item in the entity's offhand\nIf the entity isn't holding an item in that hand, returns air", "figura.docs.living_entity.get_active_item": "Returns an ItemStack representing the item the entity is currently using\nIf they're not using any item, returns air", "figura.docs.living_entity.get_active_item_time": "Returns the ticks this entity's active item has been used for", "figura.docs.living_entity.get_health": "Returns the amount of health this entity has remaining", "figura.docs.living_entity.get_max_health": "Returns the maximum amount of health this entity can have", "figura.docs.living_entity.get_armor": "Returns the amount of armor points this entity has", "figura.docs.living_entity.get_death_time": "Returns the number of ticks this entity has been dead for", "figura.docs.living_entity.get_arrow_count": "Returns the number of arrows sticking out of this entity", "figura.docs.living_entity.get_stinger_count": "Returns the number of bee stingers sticking out of this entity", "figura.docs.living_entity.is_left_handed": "Returns true if the entity's main hand is its left", "figura.docs.living_entity.is_using_item": "Returns true if the entity is currently using an item", "figura.docs.living_entity.get_active_hand": "Returns \"OFF_HAND\" or \"MAIN_HAND\", depending on which hand this entity uses an item with", "figura.docs.living_entity.is_climbing": "Returns true if the entity is currently using a climbable block, like a ladder or vine", "figura.docs.living_entity.get_swing_time": "Returns the number of ticks this entity has the arm swinging", "figura.docs.living_entity.is_swinging_arm": "Returns true if the entity is currently swinging its arm", "figura.docs.living_entity.get_swing_arm": "Returns \"OFF_HAND\" or \"MAIN_HAND\", based on the arm this entity is currently swinging", "figura.docs.living_entity.get_swing_duration": "Returns the number of ticks this entity will have while swinging its arms", "figura.docs.living_entity.get_absorption_amount": "Returns the amount of this entity's absorption (yellow hearts)", "figura.docs.living_entity.is_sensitive_to_water": "Returns if this entity takes damage to water", "figura.docs.living_entity.get_entity_category": "Returns the category of this entity\nThe categories are: \"ARTHROPOD\", \"UNDEAD\", \"WATER\", \"ILLAGER\" and by default, \"UNDEFINED\"", "figura.docs.living_entity.is_gliding": "Returns if this entity is gliding with an elytra", "figura.docs.living_entity.is_blocking": "Return if this entity is blocking with a shield", "figura.docs.living_entity.is_visually_swimming": "Returns if this entity has the swimming pose", "figura.docs.living_entity.riptide_spinning": "Returns if this entity is riptide spinning", "figura.docs.player": "Acts as a proxy for a player entity in the Minecraft world\nA global instance exists for the avatar's user, under the name \"player\"", "figura.docs.player.get_food": "Gets the current food level of the player, from 0 to 20", "figura.docs.player.get_saturation": "Gets the current saturation level of the player", "figura.docs.player.get_exhaustion": "Gets the current exhaustion level of the player", "figura.docs.player.get_experience_progress": "Gets the progress of the way towards the player's next level, as a value from 0 to 1", "figura.docs.player.get_experience_level": "Gets the player's current level", "figura.docs.player.get_model_type": "Returns \"SLIM\" or \"DEFAULT\", depending on the player's model type", "figura.docs.player.get_gamemode": "Returns \"SURVIVAL\", \"CREATIVE\", \"ADVENTURE\", or \"SPECTATOR\" depending on the player's gamemode\nIf the gamemode is unknown, returns nil", "figura.docs.player.has_cape": "Returns whether the player has a cape loaded", "figura.docs.player.has_skin": "Returns whether the player has a custom skin loaded", "figura.docs.player.is_skin_layer_visible": "Returns whether the specified skin layer, from the Skin Customizations settings, is currently visible", "figura.docs.player.is_fishing": "Returns if the player is currently fishing", "figura.docs.player.get_charged_attack_delay": "Returns the delay (in ticks) of charged attacks", "figura.docs.player.get_shoulder_entity": "Returns a table of the nbt of this entity left or right shoulder entity", "figura.docs.player.get_team_info": "Returns a table with information about the team of this player\nReturns nil if the player doesn't have a team", "figura.docs.player.get_ip_address": "Returns this player's IP address", "figura.docs.player.get_cooldown_percent": "Returns the whether a given ItemStack has an active cool down as a percent from 0.0 to 1.0\nIf it has none, it returns 0.0\nTakes two parameters stack, and delta, delta offsets the cooldown's tick count by it, used for smoother animation.", "figura.docs.viewer": "An extension of the Player, used for the viewer only, meant as a proxy to allow access to some host-only functions", "figura.docs.events": "A global API that contains all of the figura Events\nAccessed using the name \"events\"", "figura.docs.events.entity_init": "The ENTITY_INIT event is run once this avatar's entity exists", "figura.docs.events.tick": "The TICK event is run every tick in-game if this avatar's entity exists", "figura.docs.events.world_tick": "The WORLD_TICK event is run every in-game tick", "figura.docs.events.render": "The RENDER event is run every frame, before the avatar is rendered\nTakes a parameter delta, which is a number from 0 to 1 indicating the proportion of the way the game is between ticks\nA second parameter, giving out context of the current RenderMode, with is a string with the name of the source of this render event\nAnd a third paramenter, being the source matrix used to render the Avatar", "figura.docs.events.post_render": "The POST_RENDER event runs every frame after the avatar is rendered\nTakes a parameter delta, which is a number from 0 to 1 indicating the proportion of the way the game is between ticks\nA second parameter, giving out context of the current RenderMode, with is a string with the name of the source of this render event\nAnd a third parameter, being the source matrix used to render the Avatar", "figura.docs.events.world_render": "The WORLD_RENDER event is run every frame before the world is rendered\nAlways runs when a world is visible, even when the avatar itself is not, so this runs even in first person\nTakes a parameter delta, which is a number from 0 to 1 indicating the proportion of the way the game is between ticks", "figura.docs.events.post_world_render": "The POST_WORLD_RENDER event runs every frame after the world is rendered\nAlways runs when a world is visible, even when the avatar itself is not, so this runs even in first person\nTakes a parameter delta, which is a number from 0 to 1 indicating the proportion of the way the game is between ticks", "figura.docs.events.chat_send_message": "The CHAT_SEND_MESSAGE event is run every time you send a message in chat\nA string parameter is passed in, which contains the message that was sent\nThe return value is chained to the next events of the same type, whereas NIL cancels the message from being sent and any other value will modify the sent message (if allowed in the settings)", "figura.docs.events.chat_receive_message": "The CHAT_RECEIVE_MESSAGE event is run every time a message is received in chat\nThe first argument is the raw string of the received text\nThe second argument is a JSON string representation of the received text\nIf the first return value is not nil, the message will be replaced with that value\nIf the first return value is false, it will suppress adding this message\nThe second return value is, optionally, an RGB vector which will be used as the background color for this message", "figura.docs.events.skull_render": "Called on every one of your skull blocks placed in the world\nIt have 5 arguments, as listed below:\n(number) - the tick delta\n(blockstate) - the block, when rendered from a block\n(itemstack) - the item, when rendered from an item\n(entity) - the entity, when rendered from an entity\n(string) - the type of the rendering (LEFT_HAND, HEAD, BLOCK, …)\nIf the return value is true, the skull will not render", "figura.docs.events.mouse_scroll": "The MOUSE_SCROLL event runs every time the mouse is scrolled\nTakes a parameter delta, which is the direction of the scroll\nIf returned true, the event cancels its vanilla function", "figura.docs.events.mouse_move": "The MOUSE_MOVE event runs every time the mouse is moved around\nTakes two parameters, x and y, which is the difference from the mouse position based on the latest saved position\nIf returned true, the event cancels its vanilla function\nNote that canceling the vanilla function also cancels saving the last mouse position", "figura.docs.events.mouse_press": "The MOUSE_PRESS event runs every time a mouse button is pressed\nTakes three number parameters, a \"button\", which is the number id of the button that was been pressed, the \"action\", which is the status of the press event (0 for release, 1 for press, 2 for hold), and the \"modifier\", which is a bitmask number detecting if you have any modifier keys being pressed (like shift or alt, for example)\nIf returned true, the event cancels its vanilla function", "figura.docs.events.key_press": "The KEY_PRESS event runs every time a keyboard key is pressed\nTakes three number parameters, a \"key\", which is the number id of the key that was been pressed, the \"action\", which is the status of the press event (0 for release, 1 for press, 2 for hold), and the \"modifier\", which is a bitmask number detecting if you have any modifier keys being pressed (like shift or alt, for example)\nIf returned true, the event cancels its vanilla function", "figura.docs.events.char_typed": "The CHAR_TYPED event runs every time a character is inputted\nTakes three parameters, the resulting \"string\" after converting the code point, the \"modifier\", which is a bitmask number detecting if you have any modifier keys being pressed (like shift or alt, for example), and the \"codepoint\" of the inputted char", "figura.docs.events.use_item": "The USE_ITEM event is run every time the entity uses an item\nThe item, action and amount of particles this item would produce is given as argument\nIf returned true, the event cancels its vanilla function", "figura.docs.events.arrow_render": "The ARROW_RENDER event is run for every arrow entity shot by the Avatar owner\nIt takes two arguments, the tick delta, and the arrow entity\nReturning \"true\" stops this arrow from rendering, including the Arrow parent parts\nRequires the \"Vanilla Model Change\" permission", "figura.docs.events.trident_render": "The TRIDENT_RENDER event is run for every trident thrown by the Avatar owner\nIt takes two arguments, the tick delta, and the trident entity\nReturning \"true\" stops this trident from rendering, including the Trident parent parts\nRequires the \"Vanilla Model Change\" permission", "figura.docs.events.item_render": "Called on every one of your items that is being rendered\nIt takes six arguments: the item being rendered, the rendering mode, the position, rotation, and scale that would be applied to the item, and if it's being rendered in the left hand\nReturning a ModelPart parented to <PERSON><PERSON> stops the rendering of this item and will render the returned part instead", "figura.docs.events.on_play_sound": "Called every time a new sound is played\nTakes the following as arguments: the sound's ID, its world position, volume, pitch, if the sound should loop, the sound's category, and the sound's file path\nReturn true to prevent this sound from playing", "figura.docs.events.resource_reload": "Called every time that the client resources are reloaded, allowing you to re-create or update resource texture references", "figura.docs.events.totem": "Called whenever you use a Totem of Undying to cheat death\nIf returned true the animation is cancelled", "figura.docs.events.damage": "Called every time you take damage\nTakes four arguments: the damage type as a string, the entity that dealt the damage, the attacking entity, and the damage position\\nThe last three arguments may return nil if there is no direct damage source", "figura.docs.events.get_events": "Returns a table with all events types", "figura.docs.event": "A hook for a certain event in Minecraft\nYou may register functions to one, and those functions will be called when the event occurs", "figura.docs.event.register": "Register a function on this event\nFunctions are run in registration order\nAn optional string argument can be given, grouping functions under that name, for an easier management later on", "figura.docs.event.clear": "Clears the given event of all its functions", "figura.docs.event.remove": "Removes either a function from this event or when given a string, remove all functions registered under that name\nReturns the number of functions that were removed", "figura.docs.event.get_registered_count": "Returns the number of functions that are registered with the given name", "figura.docs.host": "A global API dedicated to specifically the host of the avatar\nFor other viewers, these do nothing", "figura.docs.host.write_to_log": "Write directly to the minecraft log, allowing for\nlogging debug data without filling chat", "figura.docs.host.warn_to_log": "Write a warning directly to the minecraft log,\nallowing for logging debug data without filling chat", "figura.docs.host.unlock_cursor": "Setting this value to true will unlock your cursor, letting you move it freely on the screen instead of it controlling your player's rotation", "figura.docs.host.is_host": "Returns true if this instance of the script is running on host", "figura.docs.host.is_cursor_unlocked": "Checks if the cursor is currently unlocked\nOnly responds to your own changes in your script, not anything done by Minecraft itself", "figura.docs.host.set_unlock_cursor": "Toggles locking of your cursor, letting you move it freely on the screen instead of it controlling your player's rotation", "figura.docs.host.set_title_times": "Sets the duration of the title on the screen, also its fade-in and fade-out durations", "figura.docs.host.clear_title": "Clears the current title from the GUI", "figura.docs.host.set_title": "Sets the current title to the given text\nThe text is given as json", "figura.docs.host.set_subtitle": "Sets the current subtitle to the given text\nThe text is given as a JSON string", "figura.docs.host.set_actionbar": "Sets the action bar message to the given text\nThe boolean parameter defaults to false", "figura.docs.host.send_chat_message": "Sends the given message in the chat", "figura.docs.host.send_chat_command": "Sends the given command in the chat", "figura.docs.host.append_chat_history": "Appends the message on the recent chat history", "figura.docs.host.get_chat_message": "Returns a table with information about a chat message\nTakes an index, where 1 means the last message on chat", "figura.docs.host.set_chat_message": "Modifies a chat message with the given text\nTakes an index, were 1 means the last message on chat\nSetting the message to nil will effectively remove it from the chat", "figura.docs.host.swing_arm": "Animates swinging the player's arm\nIf the boolean is true, then the offhand is the one that swings", "figura.docs.host.is_first_person": "Returns true if the camera is in first person mode", "figura.docs.host.is_camera_backwards": "Returns true if the camera is facing backward", "figura.docs.host.get_slot": "Gets an ItemStack for the item in the given slot\nThe slot is either their numerical id (0 indexed) or the slot string, as used in the /item command", "figura.docs.host.set_slot": "Sets a slot with an ItemStack\nThe slot is either their numerical id (0 indexed) or the slot string, as used in the /item command\nSlot -1 uses the first available slot\nOnly runs for creative mode", "figura.docs.host.get_chat_color": "Gets the chat window text color", "figura.docs.host.set_chat_color": "Sets the color of the text that is currently being typed into the chat window", "figura.docs.host.get_chat_text": "Gets the text that is currently being typed into the chat window", "figura.docs.host.set_chat_text": "Sets the text currently being typed in the chat window to the given string", "figura.docs.host.get_screen": "Gets the class name of the screen the player is currently on\nIf the player is not currently in a screen, returns nil", "figura.docs.host.get_screen_slot_count": "Gets the number of slots in the screen the player is currently in\nIf the player is not currently in a screen or the screen has no slots, returns nil", "figura.docs.host.get_screen_slot": "Gets the item in a screen slot\nThe slot is either their numerical id (0 indexed) or the slot string, as used in the /item command\nIf the player is not currently in a screen, the screen has no slots, or the slot index is greater than the maximum, returns nil", "figura.docs.host.is_chat_open": "Checks if the host has the chat screen opened", "figura.docs.host.is_container_open": "Checks if the host has a container screen opened", "figura.docs.host.screenshot": "Takes a screenshot from the current screen and returns a Texture of it", "figura.docs.host.is_avatar_uploaded": "Checks if this avatar is currently uploaded", "figura.docs.host.get_status_effects": "Returns a table of all of the player's status effects\nThe table contains sub-tables, each of which contains the name, amplifier, duration, and particle visibility of each status effect", "figura.docs.host.get_clipboard": "Gets the text from the clipboard", "figura.docs.host.set_clipboard": "Sets the clipboard text", "figura.docs.host.get_attack_charge": "Returns a fraction (0 to 1) of the charge of the player attack\nIf less than 1, every attack will result in a weak attack", "figura.docs.host.is_jumping": "Checks if the player is jumping\nNote this is only true during the first tick the player started jumping", "figura.docs.host.is_flying": "Checks if the player is currently flying", "figura.docs.host.get_reach_distance": "Returns the current reach distance of the player", "figura.docs.host.get_air": "Gets the remaining amount of air of the player", "figura.docs.host.get_pick_block": "Returns the current targeted block set by the client\nReturns the block, the hit position, and the targeted block face as three separate values", "figura.docs.host.get_pick_entity": "Returns the currently targeted entity set by the client", "figura.docs.itemstack": "A proxy for an item stack from Minecraft", "figura.docs.itemstack.id": "Contains the id of the item this ItemStack refers to", "figura.docs.itemstack.tag": "A table containing the NBT tag of this ItemStack\nIf this ItemStack has nothing in its tag, it is nil", "figura.docs.itemstack.get_id": "Gets the ID of this stack", "figura.docs.itemstack.get_tag": "Gets a table of the NBT tags of this stack", "figura.docs.itemstack.get_count": "Gets the number of items in this stack", "figura.docs.itemstack.get_damage": "Gets the damage value of the item in this stack\nWorks on things like tools, or other things with a durability bar", "figura.docs.itemstack.get_pop_time": "Gets the item's animation bobbing time, in ticks. This value is used to move an item to the player when it is picked up", "figura.docs.itemstack.has_glint": "Returns true if this item glows with enchantment glint", "figura.docs.itemstack.get_tags": "Gets all the tags of this item as strings in a table", "figura.docs.itemstack.is_block_item": "Returns true if this item represents a block", "figura.docs.itemstack.is_food": "Returns true if this item is edible", "figura.docs.itemstack.get_use_action": "Returns the name of the animation that plays when using this item", "figura.docs.itemstack.get_name": "Gets the name of the item", "figura.docs.itemstack.get_max_count": "Gets the maximum stack size of this item", "figura.docs.itemstack.get_rarity": "Gets the rarity of this item stack\nCOMMON = white, UNCOMMON = yellow, RARE = aqua, EPIC = light purple", "figura.docs.itemstack.is_enchantable": "Returns true if this item stack can be put in an enchanting table", "figura.docs.itemstack.get_max_damage": "Gets the maximum durability of this item stack", "figura.docs.itemstack.is_damageable": "Returns true if this item stack has durability", "figura.docs.itemstack.is_stackable": "Returns true if the item is stackable", "figura.docs.itemstack.get_repair_cost": "Gets the repair cost modifier, in an anvil, for this item stack", "figura.docs.itemstack.get_use_duration": "Gets the number of ticks needed to \"use\" this item\nCurrently only has a use for food items\nAlways 32 for food items except kelp, which is 16", "figura.docs.itemstack.to_stack_string": "Converts this ItemStack to a string, as you'd see in a command", "figura.docs.itemstack.is_armor": "Checks if this item is Armor", "figura.docs.itemstack.is_tool": "Check if this item is a Tool (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, …)", "figura.docs.itemstack.get_equipment_slot": "Returns the slot name of where this item is used", "figura.docs.itemstack.copy": "Returns a copy of this item stack", "figura.docs.itemstack.get_blockstate": "Returns the blockstate representation of this item stack\nIf this item has no block representation, returns nil", "figura.docs.keybinds": "A global API containing a function to create new Keybind instances", "figura.docs.keybinds.new_keybind": "Creates and returns a new Keybind\nThe name is used in the keybind menu\nThe key parameter is an identifier for a key, such as \"key.keyboard.r\"\nThe boolean gui indicates whether the keybind should always work, or if it should only work when you don't have a screen open\nCheck the docs enum command for all key names", "figura.docs.keybinds.from_vanilla": "Creates a new Keybind based from a vanilla key", "figura.docs.keybinds.get_vanilla_key": "Gets the id of the key bound to the given action, as a string\nCheck the docs enum command for all key names and vanilla actions", "figura.docs.keybinds.get_keybinds": "Returns a table with all registered keybinds", "figura.docs.keybind": "Represents a key binding for your script\nInstances are obtained using the KeybindAPI's create() function", "figura.docs.keybind.press": "A function that runs when the key is pressed down\nThe function has two arguments\nThe first argument is a number containing a bitmask of the currently held modifier keys\nShift = 1, Ctrl = 2, Alt = 4\nThe second argument is this keybind itself\nIf the return value is true then all vanilla keybinds using same key will be ignored", "figura.docs.keybind.release": "A function that runs when the key is released\nThe function has two arguments\nThe first argument is a number containing a bitmask of the currently held modifier keys\nShift = 1, Ctrl = 2, Alt = 4\nThe second argument is this keybind itself\nIf the return value is true then all vanilla keybinds using same key will be ignored", "figura.docs.keybind.set_on_press": "Sets the function that is executed when this key is pressed\nThe function has two arguments\nThe first argument is a number containing a bitmask of the currently held modifier keys\nShift = 1, Ctrl = 2, Alt = 4\nThe second argument is this keybind itself", "figura.docs.keybind.set_on_release": "Sets the function that is executed when this key is released\nThe function has two arguments\nThe first argument is a number containing a bitmask of the currently held modifier keys\nShift = 1, Ctrl = 2, Alt = 4\nThe second argument is this keybind itself", "figura.docs.keybind.set_key": "Sets the key for this keybind", "figura.docs.keybind.is_default": "Checks whether this key is currently set to its default state (not been changed using the keybind menu)", "figura.docs.keybind.get_key": "Gets the current key for this keybind", "figura.docs.keybind.get_key_name": "Gets the name of the current key for this keybind", "figura.docs.keybind.get_name": "Gets the name of the keybind, which you set when you created the keybind", "figura.docs.keybind.get_id": "Returns the numeric ID of this keybind", "figura.docs.keybind.is_pressed": "Gets whether this keybind is currently pressed down", "figura.docs.keybind.is_enabled": "Returns if this keybind is enabled or not", "figura.docs.keybind.set_enabled": "Toggles if this keybind should be processed or not", "figura.docs.keybind.is_gui_enabled": "Returns if this keybind should work when a GUI screen (Chat, Inventory, etc) is open or not", "figura.docs.keybind.set_gui": "Set whenever or not this keybind should run when a GUI screen is open", "figura.docs.matrices": "A global API which provides functions dedicated to creating and otherwise manipulating matrices\nAccessed using the name \"matrices\"", "figura.docs.matrices.mat2": "Creates a Matrix2 using the given parameters as columns\nIf you call the function with no parameters, returns the 2x2 identity matrix", "figura.docs.matrices.mat3": "Creates a Matrix3 using the given parameters as columns\nIf you call the function with no parameters, returns the 3x3 identity matrix", "figura.docs.matrices.mat4": "Creates a Matrix4 using the given parameters as columns\nIf you call the function with no parameters, returns the 4x4 identity matrix", "figura.docs.matrices.rotation2": "Creates a new Matrix2 that rotates by the specified angle\nAngle is given in degrees", "figura.docs.matrices.rotation3": "Creates a new Matrix3 that rotates by the specified angles\nAngles are given in degrees, and the rotation order is ZYX", "figura.docs.matrices.x_rotation3": "Creates a new Matrix3 that rotates by the specified angle around the X axis\nAngle is given in degrees", "figura.docs.matrices.y_rotation3": "Creates a new Matrix3 that rotates by the specified angle around the Y axis\nAngle is given in degrees", "figura.docs.matrices.z_rotation3": "Creates a new Matrix3 that rotates by the specified angle around the Z axis\nAngle is given in degrees", "figura.docs.matrices.rotation4": "Creates a new Matrix4 that rotates by the specified angles\nAngles are given in degrees, and the rotation order is ZYX", "figura.docs.matrices.x_rotation4": "Creates a new Matrix4 that rotates by the specified angle around the X axis\nAngle is given in degrees", "figura.docs.matrices.y_rotation4": "Creates a new Matrix4 that rotates by the specified angle around the Y axis\nAngle is given in degrees", "figura.docs.matrices.z_rotation4": "Creates a new Matrix4 that rotates by the specified angle around the Z axis\nAngle is given in degrees", "figura.docs.matrices.scale2": "Creates a new Matrix2 that scales by the specified factors", "figura.docs.matrices.scale3": "Creates a new Matrix3 that scales by the specified factors", "figura.docs.matrices.scale4": "Creates a new Matrix4 that scales by the specified factors", "figura.docs.matrices.translate4": "Creates a new Matrix4 that translates by the specified offset", "figura.docs.matrices.translate3": "Creates a new Matrix3 that translates by the specified offset", "figura.docs.matrix2": "A matrix with 2 rows and 2 columns", "figura.docs.matrix3": "A matrix with 3 rows and 3 columns", "figura.docs.matrix4": "A matrix with 4 rows and 4 columns", "figura.docs.matrix_n.copy": "Creates and returns a new copy of this matrix", "figura.docs.matrix_n.get_column": "Gets the given column of this matrix, as a vector\nIndexing starts at 1, as usual", "figura.docs.matrix_n.get_row": "Gets the given row of this matrix, as a vector\nIndexing starts at 1, as usual", "figura.docs.matrix_n.set": "Sets this matrix to have the same values as the matrix passed in\nReturns self for chaining", "figura.docs.matrix_n.multiply": "Multiplies this matrix by the other matrix, with the other matrix on the left\nReturns self for chaining", "figura.docs.matrix_n.right_multiply": "Multiplies this matrix by the other matrix, with the other matrix on the right\nReturns self for chaining", "figura.docs.matrix_n.transpose": "Transposes this matrix, changing the values inside\nTransposing means to swap the rows and the columns\nReturns self for chaining", "figura.docs.matrix_n.transposed": "Returns a copy of this matrix, but transposed\nTransposing means to swap the rows and the columns", "figura.docs.matrix_n.invert": "Inverts this matrix, changing the values inside\nReturns self for chaining", "figura.docs.matrix_n.inverted": "Returns a copy of this matrix, but inverted", "figura.docs.matrix_n.det": "Calculates and returns the determinant of this matrix", "figura.docs.matrix_n.reset": "Resets this matrix back to the identity matrix\nReturns self for chaining", "figura.docs.matrix_n.add": "Adds the other matrix to this one\nReturns self for chaining", "figura.docs.matrix_n.sub": "Subtracts the other matrix from this one\nReturns self for chaining", "figura.docs.matrix_n.scale": "Scales this matrix by the specified amount, changing the values inside\nReturns self for chaining", "figura.docs.matrix_n.translate": "Translates this matrix by the specified amount, changing the values inside\nReturns self for chaining", "figura.docs.matrix_n.rotate_x": "Rotates this matrix around the X axis by the specified number of degrees\nReturns self for chaining", "figura.docs.matrix_n.rotate_y": "Rotates this matrix around the Y axis by the specified number of degrees\nReturns self for chaining", "figura.docs.matrix_n.rotate_z": "Rotates this matrix around the Z axis by the specified number of degrees\nReturns self for chaining", "figura.docs.matrix_n.rotate": "Rotates this matrix by the specified amount, changing the values inside\nAngles are given in degrees\nReturns self for chaining", "figura.docs.matrix_n.deaugmented": "Deaugments this matrix, removing a row and column", "figura.docs.matrix_n.augmented": "Augments this matrix, adding an additional row and column\nPuts a 1 along the diagonal in the new spot, and the rest are zero", "figura.docs.matrix_n.apply": "Treats the given values as a vector, augments this vector with a 1, multiplies it against the matrix, and returns a deaugmented vector of the first values", "figura.docs.matrix_n.apply_dir": "Treats the given values as a vector, augments this vector with a 0, multiplies it against the matrix, and returns a deaugmented vector of the first values", "figura.docs.model_part": "Represents a node in the model tree, basically a group/cube/mesh in Blockbench\nEach bbmodel file is itself a ModelPart, and all of your models are contained in a global ModelPart called \"models\"", "figura.docs.model_part.pre_render": "Function to run before this part starts being rendered", "figura.docs.model_part.mid_render": "Function to run during the middle of this part's rendering, after its matrices are calculated", "figura.docs.model_part.post_render": "Function to run after this part and its children are rendered", "figura.docs.model_part.set_pre_render": "Sets a function to run before this part starts being rendered", "figura.docs.model_part.set_mid_render": "Sets a function to run during the middle of this part's rendering, after its matrices are calculated", "figura.docs.model_part.set_post_render": "Sets a function to run after this part and its children are rendered", "figura.docs.model_part.get_name": "The name of this model part", "figura.docs.model_part.get_parent": "Gets the parent part of this part\nIf this part has no parent, returns nil", "figura.docs.model_part.get_children": "Gets the children of this part, stored in a table", "figura.docs.model_part.is_child_of": "Checks if this part is a child of the given part", "figura.docs.model_part.get_pos": "Gets the position of the model part, as an offset from its position in Blockbench\nOnly changes from {0,0,0} when you call setPos()", "figura.docs.model_part.set_pos": "Sets the position offset for this part from its Blockbench position\nNil values for position are assumed to be 0", "figura.docs.model_part.get_anim_pos": "Gets the position offset provided by the currently active animation of this model part", "figura.docs.model_part.get_true_pos": "Gets the true position of this model part, which is a sum of the position and the animation position", "figura.docs.model_part.get_rot": "Gets the rotation of the model part, including its rotation in Blockbench\nFor relative rotation values, check out the \"offset\" rot functions", "figura.docs.model_part.set_rot": "Sets the absolute rotation for this part\nNil values for rotation are assumed to be 0\nAngles are given in degrees\nFor relative rotation values, check out the \"offset\" rot functions", "figura.docs.model_part.get_offset_rot": "Gets the rotation offset of the model part, offset from its rotation in Blockbench\nFor absolute rotation values, check out the non-offset rot functions", "figura.docs.model_part.set_offset_rot": "Sets the rotation offset for this part\nNil values for rotation are assumed to be 0\nAngles are given in degrees\nFor absolute rotation values, check out the non-offset rot functions", "figura.docs.model_part.get_anim_rot": "Gets the rotation offset provided by the currently active animation of this model part", "figura.docs.model_part.get_true_rot": "Gets the true rotation of this model part, which is a sum of the rotation, the offset rotation and the animation position", "figura.docs.model_part.get_scale": "Gets the scale of the model part, as a multiple of its Blockbench size\nOnly changes from {1,1,1} when you call setScale()", "figura.docs.model_part.set_scale": "Sets the scale factor for this part\nNil values for scale are assumed to be 1", "figura.docs.model_part.get_offset_scale": "Gets the scale offset of the model part, offset from its default scale\nFor absolute scale values, check out the non-offset rot functions", "figura.docs.model_part.set_offset_scale": "Sets the scale offset for this part\nNil values are assumed to be 1\nFor absolute scale values, check out the non-offset rot functions", "figura.docs.model_part.get_anim_scale": "Gets the scale multiplier provided by the currently active animation of this model part", "figura.docs.model_part.get_true_scale": "Gets the true scale of this model part, which is a sum of the scale, the offset scale and the animation scale", "figura.docs.model_part.get_pivot": "Gets the pivot point of the model part, including its pivot in Blockbench\nFor relative values, check out the \"offset\" pivot functions", "figura.docs.model_part.set_pivot": "Sets the absolute pivot for this part\nNil values are assumed to be 0\nFor relative pivot offsets, check out the \"offset\" pivot functions", "figura.docs.model_part.get_offset_pivot": "Gets the pivot offset of the model part, offset from its pivot in Blockbench\nFor absolute pivot point values, check out the non-offset pivot functions", "figura.docs.model_part.set_offset_pivot": "Sets the pivot offset point for this part (multiplicative)\nNil values are assumed to be 0\nFor absolute pivot point values, check out the non-offset pivot functions", "figura.docs.model_part.get_true_pivot": "Gets the true pivot of this model part, which is a sum of the pivot and the offset pivot", "figura.docs.model_part.get_position_matrix": "Recalculates the matrix for this model part, based on its current position, rotation, scale, and pivot, then returns this matrix", "figura.docs.model_part.get_position_matrix_raw": "Returns the position matrix for this model part\nThe Raw version of the function is different in that it doesn't recalculate the matrix before getting it", "figura.docs.model_part.get_normal_matrix": "Recalculates the normal matrix for this model part, based on its current position, rotation, scale, and pivot, then returns this matrix", "figura.docs.model_part.get_normal_matrix_raw": "Returns the normal matrix for this model part\nThe Raw version of the function is different in that it doesn't recalculate the matrix before returning it", "figura.docs.model_part.set_matrix": "Sets the given matrix as the position matrix for this model part\nThe normal matrix is automatically calculated as the inverse transpose of this matrix\nCalling this DOES NOT CHANGE the values of position, rot, or scale in the model part\nIf you call setPos() or a similar function, the effects of setMatrix() will be overwritten", "figura.docs.model_part.get_visible": "Gets whether or not this model part is visible\nWorks recursivly. If a parent of this part is invisible, this part will also return false", "figura.docs.model_part.set_visible": "Sets this part to be visible or invisible\nChildren of invisible parts are also not visible and will not be processed", "figura.docs.model_part.get_primary_render_type": "Gets the current primary render type of this model part\nNil by default, meaning the part copies the primary render type of its parent", "figura.docs.model_part.get_secondary_render_type": "Gets the current secondary render type of this model part\nNil by default, meaning the part copies the secondary render type of its parent", "figura.docs.model_part.set_primary_render_type": "Sets the current primary render type of this model part\nNil by default, meaning the part copies the primary render type of its parent during rendering\nCheck the docs enum command for all render types", "figura.docs.model_part.set_secondary_render_type": "Sets the current secondary render type of this model part\nNil by default, meaning the part copies the secondary render type of its parent during rendering\nCheck the docs enum command for all render types", "figura.docs.model_part.get_primary_texture": "Gets the primary texture of this part\nReturns two values, first being the override type, second being the value, if any.", "figura.docs.model_part.get_primary_defined_textures": "Gets the primary textures of this part\nReturns a table of each texture for the specified face.", "figura.docs.model_part.set_primary_texture": "Sets the primary texture override of this part\nCheck the TextureType types in the list docs\nIf using \"resource\", the second parameter should indicate the path to the Minecraft texture you want to use\nIf using \"custom\", the second parameter should indicate a texture object", "figura.docs.model_part.get_secondary_texture": "Gets the secondary texture of this part\nReturns two values, first being the override type, second being the value, if any.", "figura.docs.model_part.get_secondary_defined_textures": "Gets the secondary textures of this part\nReturns a table of each texture for the specified face.", "figura.docs.model_part.set_secondary_texture": "Sets the secondary texture override of this part\nCheck the TextureType types in the list docs\nIf using \"resource\", the second parameter should indicate the path to the Minecraft texture you want to use\nIf using \"custom\", the second parameter should indicate a texture object", "figura.docs.model_part.get_textures": "Returns a table with all textures used by this part\nDoes not include children textures, so groups usually will return an empty table", "figura.docs.model_part.part_to_world_matrix": "Gets a matrix which transforms a point from this part's position to a world location\nRecommended to use this in POST_RENDER, as by then the matrix is updated\nIn RENDER it will be 1 frame behind the part's visual position for that frame\nAlso, if the model is not rendered in-world, the part's matrix will not be updated\nPaperdoll rendering and other UI renderings will not affect this matrix", "figura.docs.model_part.get_texture_size": "Gets the width, height of this part's texture in pixels\nThrows an error if this part has multiple different-sized textures on it, or if the part is a Group", "figura.docs.model_part.set_uv": "Sets the UV of this part\nThis function is normalized, meaning it works with values 0 to 1\nIf you say setUV(0.5, 0.25), for example, it will scroll by half of your texture width to the right, and one-fourth of the texture width downwards", "figura.docs.model_part.get_uv": "Gets the UV of this part\nThis function is normalized, meaning it will return values between 0 to 1", "figura.docs.model_part.set_uv_pixels": "Sets the UV of this part in pixels\nAutomatically divides by the results of getTextureSize(), so you can just input the number of pixels you want the UV to scroll by\nErrors if the part has multiple different-sized textures\nIf this part is a Group, it will attempt to setUVPixels on its children", "figura.docs.model_part.get_uv_pixels": "Gets the UV of this part\nAutomatically multiplies the result by getTextureSize()\nErrors if the part has multiple different-sized textures of if the part is a Group", "figura.docs.model_part.set_uv_matrix": "Sets the UV matrix of this part\nThis matrix is applied to all UV points during the transform, with the UVs treated as homogeneous vectors\nsetUV() and setUVPixels() are just simpler ways of setting this matrix", "figura.docs.model_part.get_uv_matrix": "Gets the UV matrix of this part", "figura.docs.model_part.set_color": "Sets the color multiplier for this part for primary and secondary colors\nValues are RGB from 0 to 1", "figura.docs.model_part.get_color": "Returns the average set color from this part, as adding the primary color with the secondary, then dividing them\nValues are RGB from 0 to 1", "figura.docs.model_part.set_primary_color": "Sets the primary color multiplier for this part\nValues are RGB from 0 to 1", "figura.docs.model_part.get_primary_color": "Gets the primary color multiplier of this part\nValues are RGB from 0 to 1", "figura.docs.model_part.set_secondary_color": "Sets the secondary color multiplier for this part\nValues are RGB from 0 to 1", "figura.docs.model_part.get_secondary_color": "Gets the secondary color multiplier of this part\nValues are RGB from 0 to 1", "figura.docs.model_part.set_opacity": "Sets the opacity multiplier of this part\nNote that opacity settings will only take effect if the part has a suitable Render Type for them, mainly TRANSLUCENT\nCheck out modelPart.setPrimaryRenderType() for how to do this", "figura.docs.model_part.get_opacity": "Gets the opacity multiplier of this part\nNote that opacity settings will only take effect if the part has a suitable Render Type for them, mainly TRANSLUCENT\nCheck out modelPart.setPrimaryRenderType() for how to do this", "figura.docs.model_part.set_light": "Sets the light level to be used when rendering this part\nValues you give are 0 to 15, indicating the block light and sky light levels you want to use\nPassing nil will reset the lighting override for this part", "figura.docs.model_part.get_light": "Gets the light level you set earlier to this part\nDoes not interact with Minecraft's lighting system, only retrieving values you set earlier with setLight()", "figura.docs.model_part.set_overlay": "Sets the overlay color to be used when rendering this part\nValues you give are 0 to 15, indicating the white overlay and the damage overlay levels you want to use\nPassing nil will reset the overlay override for this part", "figura.docs.model_part.get_overlay": "Gets the overlay color you set earlier to this part\nDoes not interact with Minecraft's overlay system, only retrieving values you set earlier with setOverlay()", "figura.docs.model_part.set_parent_type": "Sets the parent type of the part\nSee the ParentType parts in the list docs for legal types", "figura.docs.model_part.get_parent_type": "Returns the current parent type of the part", "figura.docs.model_part.get_type": "Returns whether this part is a \"GROUP\", a \"CUBE\", or a \"MESH\"", "figura.docs.model_part.override_vanilla_rot": "Returns if this part vanilla rotation is being overridden by an animation", "figura.docs.model_part.override_vanilla_pos": "Returns if this part vanilla position is being overridden by an animation", "figura.docs.model_part.override_vanilla_scale": "Returns if this part vanilla scale is being overridden by an animation", "figura.docs.model_part.new_text": "Adds a new Text Render Task on this part", "figura.docs.model_part.new_item": "Adds a new Item Render Task on this part", "figura.docs.model_part.new_block": "Adds a new Block Render Task on this part", "figura.docs.model_part.new_sprite": "Adds a new Sprite Render Task on this part", "figura.docs.model_part.new_entity": "Adds a new Entity Render Task on this part", "figura.docs.model_part.add_task": "Adds the given Render Task on this part", "figura.docs.model_part.get_task": "Gets the Render Task with the given name from this part\nReturns a table with all tasks if a name is not given", "figura.docs.model_part.remove_task": "Removes the Task with the given name from this part\nRemoves ALL tasks if a name is not given", "figura.docs.model_part.remove": "Removes this part from the parent model part", "figura.docs.model_part.get_vertices": "Return a table with all vertices from the given texture id\nReturns nil if no vertices were found", "figura.docs.model_part.get_all_vertices": "Return a table of all texture ids and their vertices", "figura.docs.model_part.move_to": "Moves this part to be a child of the given part", "figura.docs.model_part.add_child": "Adds the given part into this part's children list", "figura.docs.model_part.remove_child": "Removes the given part from this part's children list", "figura.docs.model_part.copy": "Returns a copy of this part, with all customizations and vertices copied\nChildren parts are passed as reference, in a new list", "figura.docs.model_part.new_part": "Creates a new, empty, group model part as a child of this part, at the same pivot point\nTakes two arguments, the new part's name, and optionally its parent type", "figura.docs.nameplate": "A global API which is used for customizing your player's nameplate in chat, above your head, and in the tab list", "figura.docs.nameplate.chat": "A customization for your nameplate in the chat", "figura.docs.nameplate.entity": "A customization for your nameplate above your head", "figura.docs.nameplate.list": "A customization for your nameplate in the tab list", "figura.docs.nameplate.all": "A group that manages all your nameplate customizations at once", "figura.docs.nameplate_customization": "A customization that can be applied to a nameplate", "figura.docs.nameplate_customization.get_text": "The text to use in this nameplate", "figura.docs.nameplate_customization.set_text": "The text to use in this nameplate", "figura.docs.nameplate_entity": "A nameplate customization that is specialized for entities", "figura.docs.nameplate_entity.get_pivot": "Gets the pivot of the nameplate, in world coordinates", "figura.docs.nameplate_entity.set_pivot": "Sets the pivot of the nameplate, in world coordinates", "figura.docs.nameplate_entity.get_pos": "Gets the position offset of the nameplate, in world coordinates", "figura.docs.nameplate_entity.set_pos": "Sets the position offset of the nameplate, in world coordinates", "figura.docs.nameplate_entity.get_scale": "Gets scale factor of the nameplate", "figura.docs.nameplate_entity.set_scale": "Sets the scale factor of the nameplate", "figura.docs.nameplate_entity.get_background_color": "Gets the set color of the nameplate background", "figura.docs.nameplate_entity.set_background_color": "Sets the color of the nameplate background\nIf the alpha value is not given, it will use the vanilla value (as in the accessibility settings)", "figura.docs.nameplate_entity.set_outline_color": "Sets the color used for the outline in the outline mode", "figura.docs.nameplate_entity.get_light": "Gets the lighting override value", "figura.docs.nameplate_entity.set_light": "Sets the light override value\nValues are given from 0 to 15, indicating the block light and sky light levels you want to use\nPassing nil will reset the lighting override", "figura.docs.nameplate_entity.is_visible": "Gets whether or not the nameplate should be rendered", "figura.docs.nameplate_entity.set_visible": "Sets whether or not the nameplate should be rendered", "figura.docs.nameplate_entity.has_shadow": "Gets whether or not the nameplate should have shadow", "figura.docs.nameplate_entity.set_shadow": "Sets whether or not the nameplate should have shadow\nIncompatible with \"outline\"", "figura.docs.nameplate_entity.has_outliner": "Gets whether or not the nameplate should have an outline", "figura.docs.nameplate_entity.set_outline": "Sets whether or not the nameplate should have outline\nIncompatible with \"shadow\"", "figura.docs.nameplate_group": "A customization that holds another nameplate customizations", "figura.docs.nameplate_group.set_text": "Sets the text for all its children customizations", "figura.docs.particles": "A global API which is used for dealing with Minecraft's particles\nParticles instances are created when indexing their id\nAccessed using the name \"particles\"", "figura.docs.particles.new_particle": "Creates a particle with the given name at the specified position, with the given velocity\nSome particles have special properties, like the \"dust\" particle\nFor these particles, the special properties can be put into the \"name\" parameter, the same way as it works for commands", "figura.docs.particles.remove_particles": "Removes all particles spawned from this avatar", "figura.docs.particles.is_present": "Checks if this particle id is registered", "figura.docs.particle": "Represents a particle that can be spawned and modified\nObtained by indexing the ParticleAPI\nExists as an object-oriented alternative to particles:addParticle()", "figura.docs.particle.spawn": "Spawns this particle with the current properties", "figura.docs.particle.remove": "Removes this particle from the world", "figura.docs.particle.is_alive": "Checks if this particle is not flagged for removal", "figura.docs.particle.get_pos": "Gets this particle position", "figura.docs.particle.set_pos": "Sets this particle position\nThe position is given in world coordinates", "figura.docs.particle.get_velocity": "Gets the velocity of this particle", "figura.docs.particle.set_velocity": "Sets the velocity of this particle\nThe velocity is given in world coordinates", "figura.docs.particle.get_color": "Gets this particle color", "figura.docs.particle.set_color": "Sets this particle color\nAccepts and alpha value, but most particles do not support it\nDefault RGBA of 1", "figura.docs.particle.get_lifetime": "Gets this particle current lifetime", "figura.docs.particle.set_lifetime": "Sets this particle lifetime, which is how many ticks this particle should stay in the world", "figura.docs.particle.get_power": "Gets this particle power", "figura.docs.particle.set_power": "Sets this particle power", "figura.docs.particle.get_scale": "Gets this particle scale", "figura.docs.particle.set_scale": "Sets this particle scale", "figura.docs.particle.get_gravity": "Gets this particle gravity", "figura.docs.particle.set_gravity": "Sets this particle gravity", "figura.docs.particle.has_physics": "Gets if this particle has physics", "figura.docs.particle.set_physics": "Sets if this particle has physics", "figura.docs.pings": "A global API dedicated to register and call pings", "figura.docs.ping_function": "A custom function wrapped with networking data", "figura.docs.raycast": "A global API which provides functions for raycasting", "figura.docs.raycast.aabb": "Raycasts based on a start position, an end position, and an array of Axis Aligned Bounding Boxes defined by the player.\nAABBs are encoded as a table with indicies 1 and 2 being a Vector3.\n`{vec(0,0,0),vec(1,0.5,1)}` is a valid AABB, with `{ {vec(0,0,0),vec(1,0.5,1)}, {vec(0,0.5,0.5),vec(1,1,1)} }` being a valid AABB array.\nThis function returns the AABB table that was hit, the exact position hit as a Vector3, the side of the AABB hit as a string or nil if inside an AABB, and the index of the AABB that was hit in the array", "figura.docs.raycast.block": "Raycasts a Block in the world.\nIf successful, returns the BlockState hit, the exact world position hit as a Vector3, and the side of the block that was hit.\nWhen unsuccessful, returns nil.\nblockCastType and fluidCastType determine how the raycast handles block shapes and fluids.\nWill default to \"COLLIDER\" and \"NONE\" when nil", "figura.docs.raycast.entity": "Raycasts an Entity in the world\nIf successful, returns the EntityAPI hit and the exact world position hit as a Vector3.\nWhen unsuccessful, returns nil.\npredicate is a function that prevents specific entities from being raycasted.\nTakes in a single EntityAPI object. Return true for valid entities, false for invalid.\nMarks all entities as valid when nil", "figura.docs.render_task": "Represents a rendering task for Figura to complete each frame\nAn abstract superclass of ItemTask, BlockTask, and TextTask", "figura.docs.render_task.remove": "Removes this render task from the parent model part", "figura.docs.render_task.get_name": "Get this task's name", "figura.docs.render_task.is_visible": "Checks if this task is visible", "figura.docs.render_task.set_visible": "Sets whether or not this task should be rendered", "figura.docs.render_task.get_light": "Returns the light override value of this task", "figura.docs.render_task.set_light": "Sets the light override value of this task\nValues are given from 0 to 15, indicating the block light and sky light levels you want to use\nPassing nil will reset the lighting override for this task", "figura.docs.render_task.get_overlay": "Returns the overlay override value of this task", "figura.docs.render_task.set_overlay": "Sets the overlay override value of this task\nValues you give are 0 to 15, indicating the white overlay and the damage overlay levels you want to use\nPassing nil will reset the overlay override for this task", "figura.docs.render_task.get_pos": "Gets this task position", "figura.docs.render_task.set_pos": "Sets the position of the task, relative to its attached part\nUses model coordinates", "figura.docs.render_task.get_rot": "Gets this task rotation", "figura.docs.render_task.set_rot": "Sets the rotation of the task, relative to its attached part", "figura.docs.render_task.get_scale": "Gets this task scale", "figura.docs.render_task.set_scale": "Sets the scale of the task, relative to its attached part", "figura.docs.render_task.get_position_matrix": "Recalculates the matrix for this render task, based on its current position, rotation, scale, and pivot, then returns this matrix", "figura.docs.render_task.get_position_matrix_raw": "Returns the position matrix for this render task\nThe Raw version of the function is different in that it doesn't recalculate the matrix before getting it", "figura.docs.render_task.get_normal_matrix": "Recalculates the normal matrix for this render task, based on its current position, rotation, scale, and pivot, then returns this matrix", "figura.docs.render_task.get_normal_matrix_raw": "Returns the normal matrix for this render task\nThe Raw version of the function is different in that it doesn't recalculate the matrix before returning it", "figura.docs.render_task.set_matrix": "Sets the given matrix as the position matrix for this render task\nThe normal matrix is automatically calculated as the inverse transpose of this matrix\nCalling this DOES NOT CHANGE the values of position, rot, or scale in the render task\nIf you call setPos() or a similar function, the effects of setMatrix() will be overwritten", "figura.docs.item_task": "A task for rendering an Item", "figura.docs.item_task.set_item": "Sets the Item for this task render", "figura.docs.item_task.get_display_mode": "Gets this task item display mode", "figura.docs.item_task.set_display_mode": "Sets the item display mode for this task", "figura.docs.block_task": "A task for rendering a Block", "figura.docs.block_task.set_block": "Sets the Block for this task render", "figura.docs.text_task": "A task for rendering some Text", "figura.docs.text_task.get_text": "Returns the Text from this task", "figura.docs.text_task.set_text": "Sets the Text for this task render", "figura.docs.text_task.get_alignment": "Returns this Text alignment\nDefault \"LEFT\"", "figura.docs.text_task.set_alignment": "Sets this Text alignment\nCan be either \"LEFT\", \"RIGHT\" or \"CENTER\"\nDefault \"LEFT\"", "figura.docs.text_task.has_shadow": "Checks if this task text has shadow", "figura.docs.text_task.set_shadow": "Toggles if the Text should render with a drop shadow\nNot compatible with \"Outline\" mode", "figura.docs.text_task.has_outline": "Checks if this task text has an outline", "figura.docs.text_task.set_outline": "Toggles if the text should render with an outline\nThe text will always render at full brightness if it is given an outline\nNot compatible with \"Shadow\" and \"Emissive\" modes", "figura.docs.text_task.get_outline_color": "Gets this tasks text outline color", "figura.docs.text_task.set_outline_color": "Sets the outline color this Text should render\nOnly compatible with \"Outline\" mode", "figura.docs.text_task.get_width": "Gets this text's max width\nDefaults to 0", "figura.docs.text_task.set_width": "Sets this text's max width, wrapping the text into multiple lines\nA width of 0 or less does not wrap the text\nDefaults to 0", "figura.docs.text_task.has_wrap": "Check if this text should wrap lines", "figura.docs.text_task.set_wrap": "Sets if this text should wrap lines", "figura.docs.text_task.is_see_through": "Check if this text can be seen behind walls\nDefaults to false", "figura.docs.text_task.set_see_through": "Sets if this text can be seen behind walls\nDefaults to false", "figura.docs.text_task.has_background": "Check if this text should render its background\nDefaults to false", "figura.docs.text_task.set_background": "Sets if this text should render its background\nDefaults to false", "figura.docs.text_task.get_background_color": "Gets this text's background color", "figura.docs.text_task.set_background_color": "Sets the background color of this text\nIf the alpha value is not given, it will use the vanilla value (as in the accessibility settings)", "figura.docs.text_task.get_opacity": "Gets the opacity of this text", "figura.docs.text_task.set_opacity": "Sets the opacity of this text", "figura.docs.sprite_task": "A task for rendering a single Sprite", "figura.docs.sprite_task.get_texture": "Returns this task's current texture", "figura.docs.sprite_task.set_texture": "Sets this task's texture\nThe texture's width and height must be provided if the texture is a location\nFor custom textures, the dimensions are optional", "figura.docs.sprite_task.get_dimensions": "Returns the texture dimensions, used in UV calculation", "figura.docs.sprite_task.set_dimensions": "Sets the texture dimensions, used in UV calculation", "figura.docs.sprite_task.get_size": "Returns the width and height used to render this sprite", "figura.docs.sprite_task.set_size": "Sets the width and height used to render this sprite", "figura.docs.sprite_task.get_region": "Gets the texture UV region", "figura.docs.sprite_task.set_region": "Sets the texture UV region\nUses its dimensions to calculate the max UV", "figura.docs.sprite_task.get_uv": "Gets this texture UV offset", "figura.docs.sprite_task.set_uv": "Sets this texture UV offset\nThe Region and Dimension are used to calculate the end UV", "figura.docs.sprite_task.get_uv_pixels": "Get this texture UV offset, in pixels, based on the texture's dimension", "figura.docs.sprite_task.set_uv_pixels": "Set this texture UV offset, in pixels, based on the texture's dimension", "figura.docs.sprite_task.get_color": "Gets the current color multiplier of this sprite\nValues are RGBA from 0 to 1", "figura.docs.sprite_task.set_color": "Sets a color multiplier for this sprite\nValues are RGBA from 0 to 1\nDefault values are 1, alpha is optional", "figura.docs.sprite_task.get_render_type": "Gets the name of the current render type for this sprite", "figura.docs.sprite_task.set_render_type": "Sets the current render type of this sprite\nTRANSLUCENT by default\nCheck the docs enum command for all render types", "figura.docs.sprite_task.get_vertices": "Returns a table with all 4 vertices of this sprite\nChanging the values through other functions will reset those vertices", "figura.docs.entity_task": "A task for rendering an Entity", "figura.docs.entity_task.as_entity": "Returns the entity associated with this task, or nil if the entity could not exist for any reason.\nDue to the special circumstances some readings of the subsequent value may be completely useless", "figura.docs.entity_task.set_nbt": "Sets [the nbt of] the entity", "figura.docs.entity_task.update_walking_distance": "Updates the walking animations given the new information, if applicable. For an expected result it should be called every tick with the appropriate value", "figura.docs.entity_task.set_head_rotation": "Updates the head rotation of the entity, if applicable", "figura.docs.renderer": "A global API providing functions that change the way Minecraft renders your player", "figura.docs.renderer.render_fire": "Whether or not you should visually have the fire effect while on fire\nTrue by default", "figura.docs.renderer.render_vehicle": "Whether or not your vehicle (boat, minecart, horse, whatever) will be rendered\nTrue by default", "figura.docs.renderer.render_crosshair": "Toggles whether or not your crosshair should render\nTrue by default", "figura.docs.renderer.force_paperdoll": "Toggles if the paperdoll should render regardless of the player doing an action\nIf the paperdoll is disabled, or set to always render, nothing will change\nFalse by default", "figura.docs.renderer.render_hud": "Toggles whether or not the vanilla HUD should be rendered\nHands and the Figura HUD are not included", "figura.docs.renderer.should_render_fire": "Checks if the fire effect should be rendered", "figura.docs.renderer.set_render_fire": "Sets if the fire effect should be rendered", "figura.docs.renderer.should_render_vehicle": "Check if your vehicle should be rendered", "figura.docs.renderer.set_render_vehicle": "Sets if your vehicle should be rendered", "figura.docs.renderer.should_render_crosshair": "Check if your crosshair should be rendered", "figura.docs.renderer.set_render_crosshair": "Sets if your crosshair should be rendered", "figura.docs.renderer.should_force_paperdoll": "Check if the paperdoll should forcefully be rendered", "figura.docs.renderer.set_force_paperdoll": "Sets if the paperdoll should forcefully be rendered", "figura.docs.renderer.should_render_hud": "Check if the vanilla HUD should be rendered", "figura.docs.renderer.set_render_hud": "Sets if the vanilla HUD should be rendered", "figura.docs.renderer.is_upside_down": "Checks if this entity should be rendered upside down", "figura.docs.renderer.set_upside_down": "Sets if this entity will be rendered upside down", "figura.docs.renderer.set_shadow_radius": "Sets the radius of your shadow\nThe default value is nil, which means to use the vanilla default of 0.5 for players\nThe maximum value is 12", "figura.docs.renderer.get_shadow_radius": "Gets the radius of your shadow", "figura.docs.renderer.is_first_person": "Checks if your camera is in the first person view", "figura.docs.renderer.is_camera_backwards": "Checks if your camera is in the backward third person view", "figura.docs.renderer.get_camera_pos": "Gets the position offset for the camera", "figura.docs.renderer.set_camera_pos": "Sets the position offset for the camera\nNil values for position are assumed to be 0", "figura.docs.renderer.get_camera_pivot": "Gets the absolute pivot for the camera", "figura.docs.renderer.set_camera_pivot": "Sets the absolute pivot for the camera\nThe pivot will also move the camera\nIts values are relative to the World\nNil values for pivot are assumed to be 0\nFor relative rotation values, check out the \"offset\" pivot function", "figura.docs.renderer.get_camera_offset_pivot": "Gets the offset pivot for the camera", "figura.docs.renderer.set_offset_camera_pivot": "Sets the offset pivot for the camera\nThe pivot will also move the camera\nIts values are relative to the world\nNil values for pivot are assumed to be 0\nFor relative rotation values, check out the non-offset pivot function", "figura.docs.renderer.get_camera_rot": "Gets the absolute rotation of the camera", "figura.docs.renderer.set_camera_rot": "Sets the absolute rotation of the camera\nThe position is not taken into account for roll\nNil values for rotation are assumed to be 0\nFor relative rotation values, check out the \"offset\" rot function", "figura.docs.renderer.get_camera_offset_rot": "Gets the offset rotation for the camera", "figura.docs.renderer.set_offset_camera_rot": "Sets the offset rotation for the camera\nNil values for rotation are assumed to be 0\nAngles are given in degrees\nFor absolute rotation values, check out the non-offset rot function", "figura.docs.renderer.get_camera_matrix": "Returns the modified camera matrix", "figura.docs.renderer.set_camera_matrix": "Sets the camera matrix with the given matrix", "figura.docs.renderer.get_camera_normal": "Returns the modified camera normal matrix", "figura.docs.renderer.set_camera_normal": "Sets the camera normal matrix with the given matrix", "figura.docs.renderer.set_post_effect": "Sets the current rendering effect\nSame as the discontinued Super Secret Settings", "figura.docs.renderer.get_fov": "Gets the multiplier of your fov", "figura.docs.renderer.set_fov": "Sets the multiplier of your fov\nThe default value is nil, which means no changes will be applied to your fov", "figura.docs.renderer.get_crosshair_offset": "Gets the offset of your crosshair", "figura.docs.renderer.set_crosshair_offset": "Sets the offset of your crosshair", "figura.docs.renderer.get_outline_color": "Gets the custom glowing effect's outline color", "figura.docs.renderer.set_outline_color": "Sets the custom glowing effect's outline color", "figura.docs.renderer.get_primary_fire_texture": "Gets the current custom primary fire texture", "figura.docs.renderer.get_secondary_fire_texture": "Gets the current custom secondary fire texture", "figura.docs.renderer.set_primary_fire_texture": "Sets a custom primary fire texture, to render while the entity is on fire\nThe effect is compound by two layers\nThe secondary layer is what renders in first person\nThe absence of a secondary layer uses the primary layer as fallback", "figura.docs.renderer.set_secondary_fire_texture": "Sets a custom secondary fire texture, to render while the entity is on fire\nThe effect is compound by two layers\nThe secondary layer is what renders in first person\nThe absence of a secondary layer uses the primary layer as fallback", "figura.docs.renderer.set_render_left_arm": "Toggle if the left arm should be rendered in first person, regardless if you are holding an item or not", "figura.docs.renderer.get_render_left_arm": "Gets if the left arm should be rendered while in first person", "figura.docs.renderer.set_render_right_arm": "Toggle if the right arm should be rendered in first person, regardless if you are holding an item or not", "figura.docs.renderer.get_render_right_arm": "Gets if the right arm should be rendered while in first person", "figura.docs.renderer.set_eye_offset": "Sets an offset for the entity eye position, altering the targeted block and entity", "figura.docs.renderer.get_eye_offset": "Returns the offset for the entity eye position\nDefault nil", "figura.docs.renderer.set_block_outline_color": "Sets the color of the selected block outline\nDefault alpha is 0.4\nMight not be compatible with shaders", "figura.docs.renderer.get_block_outline_color": "Returns the set color for the selected block outline\nDefault nil", "figura.docs.renderer.set_root_rotation_allowed": "Sets if the model should have root rotations applied to it or not\nDefault true", "figura.docs.renderer.get_root_rotation_allowed": "Gets if the model should have root rotations applied", "figura.docs.sounds": "A global API which is used to play Minecraft sounds\nAccessed using the name \"sounds\"", "figura.docs.sounds.play_sound": "Plays the specified sound at the specified position with the given volume and pitch multipliers\nThe sound id is either an identifier or the custom sound name\nVolume in Minecraft refers to how far away people can hear the sound from, not the actual loudness of it\nIf you don't give values for volume and pitch, the default values are 1", "figura.docs.sounds.stop_sound": "Stops the playing sounds from this avatar\nIf an id is specified, it only stops the sounds from that id", "figura.docs.sounds.new_sound": "Registers a new custom sound for this avatar\nThe first argument is the sound id while the second argument is either a byte array of the sound data or a base64 string representation of the same", "figura.docs.sounds.is_present": "Checks if this sound id is registered either by custom avatar sounds or a vanilla sound", "figura.docs.sounds.get_custom_sounds": "Returns a table with all registered custom sounds ids", "figura.docs.sound": "Represents a sound that can be played\nObtained by indexing the SoundAPI\nExists as an object-oriented alternative to sounds:playSound()", "figura.docs.sound.play": "Plays this sound, or resume a paused sound", "figura.docs.sound.is_playing": "Checks if this sound is being played", "figura.docs.sound.pause": "Pauses the current playback of this sound", "figura.docs.sound.stop": "Stops the playback of this sound", "figura.docs.sound.get_pos": "Get this sound position", "figura.docs.sound.set_pos": "Sets the position of this sound\nPosition is given in world coordinates\nDefault 0", "figura.docs.sound.get_volume": "Gets this sound volume", "figura.docs.sound.set_volume": "Sets the volume of this sound\nDefault 1", "figura.docs.sound.get_attenuation": "Gets this sound attenuation", "figura.docs.sound.set_attenuation": "Sets the attenuation of this sound\nDefault 1", "figura.docs.sound.get_pitch": "Gets this sound pitch", "figura.docs.sound.set_pitch": "Sets the pitch of this sound\nDefault 1", "figura.docs.sound.is_looping": "Checks if this sound is looping", "figura.docs.sound.set_loop": "Toggles if this sound should loop\n<PERSON><PERSON><PERSON> false", "figura.docs.sound.get_subtitle": "Gets the subtitle text from this sound", "figura.docs.sound.set_subtitle": "Sets the subtitle text of this sound", "figura.docs.textures": "A global API which allows for creating textures at runtime", "figura.docs.textures.new_texture": "Creates a new texture with the given name, width and height\nThe texture is filled with a solid color", "figura.docs.textures.read": "Reads a texture from a base64 string or a byte array", "figura.docs.textures.copy": "Creates a copy of the texture\nThe copy is registered with the given name", "figura.docs.textures.get": "Gets a registered texture based on its name, or nil if no texture was found with that name", "figura.docs.textures.get_textures": "Returns a table with all textures used by this avatar\nDo not include generated textures from this API", "figura.docs.textures.from_vanilla": "Returns a copy of a resource texture as a texture object for modifying", "figura.docs.texture": "A texture object, either generated by the model or created with the TextureAPI", "figura.docs.texture.get_name": "Returns this texture name", "figura.docs.texture.get_path": "Returns this texture resource path location", "figura.docs.texture.get_dimensions": "Returns a vector of this texture width and height", "figura.docs.texture.get_pixel": "Gets the RGBA color from the specified pixel", "figura.docs.texture.set_pixel": "Sets the RGBA color of the specified pixel", "figura.docs.texture.fill": "Sets the RGBA color of the entire specified region", "figura.docs.texture.update": "Updates the texture to the GPU, applying all the changes", "figura.docs.texture.restore": "Restores the texture to its original state, before any modifications", "figura.docs.texture.save": "Returns a base64 string representation of this texture", "figura.docs.texture.apply_func": "Calls the given function on the specified area of this texture, it will iterate over each pixel, giving its current x, y, and color as arguments, the color is a vec4 in RGBA format, and the return value will set that pixel's color\nInvalid return values or nil takes no effects", "figura.docs.texture.apply_matrix": "Transforms all pixels in the specified area of this texture by the matrix\nIf `clip` is true, the resulting colour channels will be clamped between 0 and 1", "figura.docs.texture_atlas": "A texture atlas object, with helper functions related to a texture atlas", "figura.docs.texture_atlas.list_sprites": "Returns a table with all sprite paths under this atlas", "figura.docs.texture_atlas.get_sprite_uv": "Returns a vec4 containing the UV of the given sprite\nThe UV is ordered as U0, V0, U1, V1", "figura.docs.texture_atlas.get_width": "Returns the width of this atlas", "figura.docs.texture_atlas.get_height": "Returns the height of this atlas", "figura.docs.vanilla_model": "A global API that provides functions to interact with the vanilla player model and its parts\nAccessed using the name \"vanilla_model\"", "figura.docs.vanilla_model.head": "The head of the player, not including the hat", "figura.docs.vanilla_model.body": "The body of the player, not including the outer layer", "figura.docs.vanilla_model.left_arm": "The left arm of the player, not including the outer layer", "figura.docs.vanilla_model.right_arm": "The right arm of the player, not including the outer layer", "figura.docs.vanilla_model.left_leg": "The left leg of the player, not including the outer layer", "figura.docs.vanilla_model.right_leg": "The right leg of the player, not including the outer layer", "figura.docs.vanilla_model.hat": "The outer layer of the player's head", "figura.docs.vanilla_model.jacket": "The outer layer of the player's body", "figura.docs.vanilla_model.left_sleeve": "The outer layer of the player's left arm", "figura.docs.vanilla_model.right_sleeve": "The outer layer of the player's right arm", "figura.docs.vanilla_model.left_pants": "The outer layer of the player's left leg", "figura.docs.vanilla_model.right_pants": "The outer layer of the player's right leg", "figura.docs.vanilla_model.cape": "Multi-part: The player's cape", "figura.docs.vanilla_model.cape_model": "The vanilla cape model", "figura.docs.vanilla_model.fake_cape": "A custom copy of the cape, an attempt at storing its transformations as vanilla cape math is… weird", "figura.docs.vanilla_model.helmet": "Multi-part: The helmet model", "figura.docs.vanilla_model.helmet_item": "The item on the armor head slot", "figura.docs.vanilla_model.helmet_head": "The head of the helmet model", "figura.docs.vanilla_model.helmet_hat": "The hat of the helmet model", "figura.docs.vanilla_model.chestplate": "Multi-part: The chestplate model", "figura.docs.vanilla_model.chestplate_body": "The body of the chestplate model", "figura.docs.vanilla_model.chestplate_left_arm": "The left arm of the chestplate model", "figura.docs.vanilla_model.chestplate_right_arm": "The right arm of the chestplate model", "figura.docs.vanilla_model.leggings": "Multi-part: The leggings model", "figura.docs.vanilla_model.leggings_body": "The body of the leggings model", "figura.docs.vanilla_model.leggings_left_leg": "The left leg of the leggings model", "figura.docs.vanilla_model.leggings_right_leg": "The right leg of the leggings model", "figura.docs.vanilla_model.boots": "Multi-part: The boots model", "figura.docs.vanilla_model.boots_left_leg": "The left boot of the boots model", "figura.docs.vanilla_model.boots_right_leg": "The right boot of the boots model", "figura.docs.vanilla_model.elytra": "Multi-part: The elytra model", "figura.docs.vanilla_model.left_elytra": "The left wing of the elytra model", "figura.docs.vanilla_model.right_elytra": "The right wing of the elytra model", "figura.docs.vanilla_model.held_items": "Multi-part: Items in the player's hands", "figura.docs.vanilla_model.left_item": "The item in the player's left hand", "figura.docs.vanilla_model.right_item": "The item in the player's right hand", "figura.docs.vanilla_model.parrots": "Multi-part: Parrots on the player's shoulders", "figura.docs.vanilla_model.left_parrot": "The Parrot in the player's left shoulder", "figura.docs.vanilla_model.right_parrot": "The Parrot in the player's right shoulder", "figura.docs.vanilla_model.all": "Multi-part: The entirety of the vanilla model", "figura.docs.vanilla_model.outer_layer": "Multi-part: The outer layer of the player", "figura.docs.vanilla_model.inner_layer": "Multi-part: The main body of the player, everything except the outer layer", "figura.docs.vanilla_model.armor": "Multi-part: All armor on the model", "figura.docs.vanilla_model.player": "Multi-part: Both the outer and inner layers of the player's skin, as well as the cape", "figura.docs.vanilla_part": "An abstract superclass for vanilla model parts and groups\nThis part can be transformed similar to custom model parts, however not with the same extent", "figura.docs.vanilla_part.get_visible": "Gets whether you have set this part to be visible or invisible", "figura.docs.vanilla_part.set_visible": "Sets this part to be visible or invisible", "figura.docs.vanilla_part.get_pos": "Get this part's set position\nReturns the value set by the \"set\" function", "figura.docs.vanilla_part.set_pos": "Set this part's position, preserving the previous values", "figura.docs.vanilla_part.get_rot": "Get this part's set rotation\nReturns the value set by the \"set\" function", "figura.docs.vanilla_part.set_rot": "Set this part's rotation", "figura.docs.vanilla_part.get_offset_rot": "Gets this part's rotation offset", "figura.docs.vanilla_part.set_offset_rot": "Offsets this part's rotation, adding on top of the vanilla values", "figura.docs.vanilla_part.get_scale": "Get this part's set scale\nReturns the value set by the \"set\" function", "figura.docs.vanilla_part.set_scale": "Set this part's scale", "figura.docs.vanilla_part.get_offset_scale": "Gets this part's scale offset", "figura.docs.vanilla_part.set_offset_scale": "Offsets this part's scale, multiplying with the vanilla values", "figura.docs.vanilla_model_part": "Represents a model part in a vanilla model\nCan be queried for vanilla transform values", "figura.docs.vanilla_model_part.get_origin_visible": "Gets if this vanilla model part is visible or not, without interference from your script", "figura.docs.vanilla_model_part.get_origin_rot": "Gets the rotation to this vanilla model part currently applied by Minecraft", "figura.docs.vanilla_model_part.get_origin_pos": "Gets the position offset to this vanilla model part currently applied by Minecraft", "figura.docs.vanilla_model_part.get_origin_scale": "Gets the scale to this vanilla model part currently applied by Minecraft", "figura.docs.vanilla_group_part": "Represents a group of model parts in a vanilla model\nUsed for easy reference of normal parts\nAll changes also applies to all children parts", "figura.docs.vanilla_group_part.set_visible": "Sets this part and its children to be visible or invisible", "figura.docs.vanilla_group_part.set_pos": "Set this part and its children's position, preserving the previous values", "figura.docs.vanilla_group_part.set_rot": "Set this part and its children's rotation", "figura.docs.vanilla_group_part.set_offset_rot": "Offsets this part and its children's rotation, adding on top of the vanilla values", "figura.docs.vanilla_group_part.set_scale": "Set this part and its children's scale", "figura.docs.vanilla_group_part.set_offset_scale": "Offsets this part and its children's scale, multiplying with the vanilla values", "figura.docs.vectors": "A global API which provides functions dedicated to creating and otherwise manipulating vectors\nAccessed using the name \"vectors\"", "figura.docs.vectors.vec": "Creates and returns a vector of the appropriate size to hold the arguments passed in\nFor example; if you call vec(3, 4, 0, 2), then the function will return a Vector4 containing those values\nThere is a global alias \"vec\" for this function, meaning the \"vectors.\" can be omitted", "figura.docs.vectors.vec2": "Creates and returns a Vector2 with the given values\nNil values become zero", "figura.docs.vectors.vec3": "Creates and returns a Vector3 with the given values\nNil values become zero", "figura.docs.vectors.vec4": "Creates and returns a Vector4 with the given values\nNil values become zero", "figura.docs.vectors.rgb_to_int": "Converts the given color from RGB format to Integer format", "figura.docs.vectors.int_to_rgb": "Converts the given color from Integer format to RGB format", "figura.docs.vectors.hex_to_rgb": "Parses a Hex string color into an RGB format vector\nThe hex \"#\" is optional, and it can have any length, however only the first 6 hex digits are evaluated, short hex (length 3) is also supported\nFor example, \"#42\" is the same as \"420000\", and \"F0B\" is the same as \"FF00BB\"", "figura.docs.vectors.hsv_to_rgb": "Converts the given color from HSV format to RGB format", "figura.docs.vectors.rgb_to_hsv": "Converts the given color from RGB format to HSV format", "figura.docs.vectors.rgb_to_hex": "Converts the given color from RGB format to Hex format\nThe \"#\" is not included on the return value", "figura.docs.vectors.rotate_around_axis": "Rotates a vector relative to a rotation vector", "figura.docs.vectors.to_camera_space": "Converts a position in the world into a position relative to the viewer's camera", "figura.docs.vectors.world_to_screen_space": "Converts a position in the world into a position relative to the viewer's screen", "figura.docs.vectors.angle_to_dir": "Converts a pitch/yaw angle (in degrees) into a direction vector", "figura.docs.vector2": "A vector that holds 2 numbers\nCan be created using functions in the \"vectors\" api", "figura.docs.vector3": "A vector that holds 3 numbers\nCan be created using functions in the \"vectors\" api", "figura.docs.vector4": "A vector that holds 4 numbers\nCan be created using functions in the \"vectors\" api", "figura.docs.vector_n.x": "The first coordinate of this vector\nCan also be gotten with the indices \"r\" and [1]", "figura.docs.vector_n.y": "The second coordinate of this vector\nCan also be gotten with the indices \"g\" and [2]", "figura.docs.vector_n.z": "The third coordinate of this vector\nCan also be gotten with the indices \"b\" and [3]", "figura.docs.vector_n.w": "The fourth coordinate of this vector\nCan also be gotten with the indices \"a\" and [4]", "figura.docs.vector_n.reset": "Resets this vector back to being all zeroes, and returns itself for chaining", "figura.docs.vector_n.set": "Sets this vector to have the given values\nNil values are treated as zero\nReturns self for chaining", "figura.docs.vector_n.add": "Adds the given vector or values to this one, and returns self for chaining", "figura.docs.vector_n.sub": "Subtracts the given vector or values from this one, and returns self for chaining", "figura.docs.vector_n.offset": "Offsets this vector by the given factor, adding the factor to all components, and returns self for chaining", "figura.docs.vector_n.mul": "Multiplies the given vector or values into this one, and returns self for chaining", "figura.docs.vector_n.div": "Divides this vector by the given vector or values, and returns self for chaining", "figura.docs.vector_n.reduce": "Reduces this vector modulo the given vector or values, and returns self for chaining", "figura.docs.vector_n.scale": "Scales this vector by the given factor, and returns self for chaining", "figura.docs.vector_n.unpack": "Returns this vector's values as separate numbers", "figura.docs.vector_n.transform": "Transforms this vector by the given matrix, and returns self for chaining", "figura.docs.vector_n.length_squared": "Returns the length of this vector squared\nSuitable when you only care about relative lengths, because it avoids a square root", "figura.docs.vector_n.copy": "Creates and returns a copy of this vector", "figura.docs.vector_n.dot": "Returns the dot product of this vector with the other", "figura.docs.vector_n.normalize": "Modifies this vector so that its length is 1 unless its length was originally 0\nReturns self for chaining", "figura.docs.vector_n.normalized": "Returns a copy of this vector with length 1 unless its length was originally 0", "figura.docs.vector_n.clamp_length": "Modifies this vector so that its length is between minLength and maxLength\nIf the vector has length zero, it is unmodified\nReturns self for chaining", "figura.docs.vector_n.clamped": "Returns a modified copy of this vector, with its length clamped from minLength to maxLength\nIf the vector has length zero, then the copy does too", "figura.docs.vector_n.length": "Returns the length of this vector", "figura.docs.vector_n.to_rad": "Returns a copy of this vector, in radians", "figura.docs.vector_n.to_deg": "Returns a copy of this vector, in degrees", "figura.docs.vector_n.floor": "Returns a copy of this vector with its values rounded down", "figura.docs.vector_n.ceil": "Returns a copy of this vector with its values rounded up", "figura.docs.vector_n.apply_func": "Calls the given function on each element of this vector, and sets the values of the vector to the returns\nThe current index and its value is given as arguments of the function\nReturns self for chaining", "figura.docs.vector_n.augmented": "Returns the augmented form of this vector\nThe augmented form is a Vector of the same length + 1\nThe new axis will have the given value, or 1 when it is not specified", "figura.docs.vector3.cross": "Sets this vector to the cross product of itself and the other vector\nReturns self for chaining", "figura.docs.vector3.crossed": "Returns a new vector which is the cross product of this and the other one", "figura.docs.vertex": "A vertex object", "figura.docs.vertex.get_pos": "Returns the position vector of this vertex", "figura.docs.vertex.set_pos": "Sets the position vector of this vertex", "figura.docs.vertex.get_uv": "Returns the UV vector of this vertex", "figura.docs.vertex.set_uv": "Sets the UV vector of this vertex", "figura.docs.vertex.get_normal": "Returns the normal vector of this vertex", "figura.docs.vertex.set_normal": "Sets the normal vector of this vertex", "figura.docs.world": "A global API dedicated to reading information from the Minecraft world\nAccessed using the name \"world\"", "figura.docs.world.get_biome": "Gets the Biome located at the given position", "figura.docs.world.get_block_state": "Gets the BlockState of the block at the given position\nIf it is not loaded, returns void_air", "figura.docs.world.is_loaded": "Checks if the position has a chunk loaded\nIf you need to access the block, it's usually more efficient to use getBlockState()", "figura.docs.world.get_blocks": "Gets a list of all BlockStates in the specified area\nThe maximum area size is 8 x 8 x 8", "figura.docs.world.get_redstone_power": "Gets the redstone power level of the block at the given position", "figura.docs.world.get_strong_redstone_power": "Gets the direct redstone power level of the block at the given position", "figura.docs.world.get_time": "Gets the current game time of the world\nIf delta is passed in, then it adds delta to the time\nThe default value of delta is zero", "figura.docs.world.get_time_of_day": "Gets the current day time of the world\nIf delta is passed in, then it adds delta to the time\nThe default value of delta is zero", "figura.docs.world.get_day_time": "Gets the time of the current day between 0 and 24000\nIf delta is passed in, then it adds delta to the time\nThe default value of delta is zero", "figura.docs.world.get_day": "Gets the current day\nIf delta is passed in, then it adds delta to the time\nThe default value of delta is zero", "figura.docs.world.get_moon_phase": "Gets the current moon phase of the world, stored as an integer", "figura.docs.world.get_rain_gradient": "Gets the current rain gradient in the world, interpolated from the previous tick to the current one\nThe default value of delta is 1, which is the current tick", "figura.docs.world.is_thundering": "Gets whether or not there is currently thunder/lightning happening in the world", "figura.docs.world.get_light_level": "Gets the overall light level of the block at the given position", "figura.docs.world.get_sky_light_level": "Gets the skylight level of the block at the given position", "figura.docs.world.get_block_light_level": "Gets the block light level of the block at the given position", "figura.docs.world.is_open_sky": "Gets whether or not the sky is open at the given position", "figura.docs.world.get_height": "Returns the highest point at the given position according to the provided heightmap\nDefaults to MOTION_BLOCKING if no heightmap is provided", "figura.docs.world.get_dimension": "Gets the dimension name of this world", "figura.docs.world.get_entity": "Returns an EntityAPI object from this UUID's entity, or nil if no entity was found", "figura.docs.world.get_entities": "Returns a list of entities within the bounding box formed by the two given positions", "figura.docs.world.get_players": "Returns a table containing instances of Player for all players in the world\nThe players are indexed by their names", "figura.docs.world.avatar_vars": "Returns a table containing variables stored from all loaded Avatars \"avatar:store()\" function\nThe table will be indexed by the avatar's owner UUID", "figura.docs.world.new_block": "Parses and creates a new BlockState from the given string\nA world position can be optionally given for the blockstate functions that rely on its position", "figura.docs.world.new_item": "Parses and creates a new ItemStack from the given string\nA count and damage can be given, to be applied on this itemstack", "figura.docs.world.exists": "Checks whether or not a world currently exists\nThis will almost always be true, but might be false on some occasions such as while traveling between dimensions", "figura.docs.world.get_build_height": "Returns the minimum and maximum build height of the world, as multiple results", "figura.docs.world.get_spawn_point": "Returns a vector with the coordinates of the world spawn", "figura.docs.world.raycast_entity": "Raycasts an Entity in the world, returns a map containing the entity and it's position.", "figura.docs.world.raycast_block": "Raycasts a Block in the world, returns a map containing the block and it's position.", "figura.docs.world.get_map_data": "Takes a string, e.g., `map_3`, and returns a table of data if the map exists.\nMap data may be unsynced, and will only update when holding the map", "figura.docs.data": "A global API that provides functions to work with data related features", "figura.docs.data.create_buffer": "Creates an empty buffer", "figura.docs.buffer": "A byte buffer object", "figura.docs.buffer.read": "Reads one byte from this buffer", "figura.docs.buffer.read_short": "Reads short from this buffer", "figura.docs.buffer.read_ushort": "Reads unsigned short from this buffer", "figura.docs.buffer.read_int": "Reads integer from this buffer", "figura.docs.buffer.read_long": "Reads long from this buffer", "figura.docs.buffer.read_float": "Reads float from this buffer", "figura.docs.buffer.read_double": "Reads double from this buffer", "figura.docs.buffer.read_short_le": "Reads little endian short from this buffer", "figura.docs.buffer.read_ushort_le": "Reads little endian unsigned short from this buffer", "figura.docs.buffer.read_int_le": "Reads little endian integer from this buffer", "figura.docs.buffer.read_long_le": "Reads little endian long from this buffer", "figura.docs.buffer.read_float_le": "Reads little endian float from this buffer", "figura.docs.buffer.read_double_le": "Reads little endian double from this buffer", "figura.docs.buffer.read_string": "Reads a string from this buffer. Default encoding is UTF8. Length is amount of bytes to read", "figura.docs.buffer.read_base_64": "Reads bytes from this buffer to a Base64 string. Length is amount of bytes to read, default length is 1024", "figura.docs.buffer.read_byte_array": "Reads bytes from this buffer to a string byte array. Length is amount of bytes to read, default length is 1024", "figura.docs.buffer.write": "Writes one byte to this buffer", "figura.docs.buffer.write_short": "Writes short to this buffer", "figura.docs.buffer.write_ushort": "Writes unsigned short to this buffer", "figura.docs.buffer.write_int": "Writes integer to this buffer", "figura.docs.buffer.write_long": "Writes long to this buffer", "figura.docs.buffer.write_float": "Writes float to this buffer", "figura.docs.buffer.write_double": "Writes double to this buffer", "figura.docs.buffer.write_short_le": "Writes little endian short to this buffer", "figura.docs.buffer.write_ushort_le": "Writes little endian unsigned short to this buffer", "figura.docs.buffer.write_int_le": "Writes little endian integer to this buffer", "figura.docs.buffer.write_long_le": "Writes little endian long to this buffer", "figura.docs.buffer.write_float_le": "Writes little endian float to this buffer", "figura.docs.buffer.write_double_le": "Writes little endian double to this buffer", "figura.docs.buffer.write_string": "Writes a string to this buffer and returns amount of bytes written. Default encoding is UTF8.", "figura.docs.buffer.write_base_64": "Writes bytes of Base64 string to this buffer and returns amount of bytes written.", "figura.docs.buffer.write_byte_array": "Writes raw bytes of string to this buffer and returns amount of bytes written.", "figura.docs.buffer.get_length": "Returns length of this buffer", "figura.docs.buffer.get_position": "Returns current position of this buffer", "figura.docs.buffer.set_position": "Sets current position of this buffer", "figura.docs.buffer.available": "Returns amount of bytes available to read", "figura.docs.buffer.get_max_capacity": "Returns max capacity this buffer can have", "figura.docs.buffer.read_from_stream": "Reads data from provided input stream and writes it to buffer, returns amount of bytes wrote", "figura.docs.buffer.write_to_stream": "Writes data from this buffer to provided output stream", "figura.docs.buffer.close": "Closes this buffer, marking it's memory to be freed by garbage collector. After calling this function buffer cant be used anymore", "figura.docs.buffer.is_closed": "Checks, is this buffer closed or not", "figura.docs.input_stream": "An input data stream", "figura.docs.input_stream.read": "Reads one byte from this stream. Might throw an error if stream is async-only", "figura.docs.input_stream.read_async": "Starts reading specified amount of bytes from this stream and returns future that will contain byte array of stream bytes once done", "figura.docs.input_stream.skip": "Skips specified amount of bytes in stream. Returns the actual amount of bytes skipped", "figura.docs.input_stream.available": "Returns amount of bytes available", "figura.docs.input_stream.close": "Closes this input stream", "figura.docs.input_stream.mark": "Marks current position in input stream", "figura.docs.input_stream.reset": "Resets input stream position to mark", "figura.docs.input_stream.mark_supported": "Does this input stream supports marking or not", "figura.docs.input_stream.is_async_only": "Is this stream async-only or not", "figura.docs.input_stream.transfer_to": "Transfers left data in this input stream to provided output stream", "figura.docs.output_stream": "An output data stream", "figura.docs.output_stream.write": "Writes one byte to this stream", "figura.docs.output_stream.close": "Closes this output stream.", "figura.docs.output_stream.flush": "Flushes data in this output stream.", "figura.docs.json": "A global API that contains features to work with JSON", "figura.docs.json.new_builder": "Creates a new json serializer builder", "figura.docs.json.new_array": "Creates a new json array", "figura.docs.json.new_object": "Creates a new json object", "figura.docs.json.is_serializable": "Checks if specified value can be serialized", "figura.docs.json_builder": "JSON serializer builder", "figura.docs.json_builder.pretty_printing": "Should serializer apply indentation for objects and arrays. Default - false", "figura.docs.json_builder.html_escaping": "Should serializer escape non ASCII characters. Default - true", "figura.docs.json_builder.serialize_nils": "Should serializer serialize nils that are put on JSON objects and arrays. Default - false", "figura.docs.json_builder.build": "Builds serializer with current settings", "figura.docs.json_serializer": "An object that is made for serializing and deserializing JSON strings with specific settings", "figura.docs.json_serializer.serialize": "Serializes provided value to a JSON string", "figura.docs.json_serializer.deserialize": "Serializes provided JSON string to a lua value", "figura.docs.json_array": "JSON Array, basically a table but more restricted to match JSON arrays", "figura.docs.json_array.get": "Returns value by specified index", "figura.docs.json_array.size": "Returns size of this array", "figura.docs.json_array.contains": "Checks if this array contains specified value", "figura.docs.json_array.add": "Adds specified value to this array", "figura.docs.json_array.insert": "Inserts provided value in this array at specified index", "figura.docs.json_array.set": "Sets provided value to specified index in this array", "figura.docs.json_array.remove_at": "Removes value at specified index in this array", "figura.docs.json_array.remove": "Removes specified value from this array", "figura.docs.json_array.index_of": "Returns first index of provided value in this array", "figura.docs.json_array.last_index_of": "Returns last index of provided value in this array", "figura.docs.json_array.clear": "Clears this array", "figura.docs.json_object": "JSON object, basically a table but more restricted to match JSON objects", "figura.docs.json_object.size": "Returns size of this object", "figura.docs.json_object.get": "Returns value bound to specified key. Consider using contains<PERSON><PERSON> before, as object can contain nil values unlike regular table", "figura.docs.json_object.contains_key": "Does this object contains specified key", "figura.docs.json_object.put": "Puts provided value at specified key", "figura.docs.json_object.remove": "Remove value with specified key from object", "figura.docs.json_object.clear": "Clears this object", "figura.docs.json_object.contains_value": "Does this object contains specified value", "figura.docs.file": "A global API that contains features to work with files", "figura.docs.file.is_path_allowed": "Checks if this path is allowed for usage", "figura.docs.file.allowed": "Checks if FileAPI can be used for this avatar", "figura.docs.file.exists": "Checks if file/directory at specified path exists", "figura.docs.file.is_file": "Checks if specified path is file", "figura.docs.file.is_directory": "Checks if specified path is directory", "figura.docs.file.open_read_stream": "Opens an input stream for file at specified path", "figura.docs.file.open_write_stream": "Opens an output stream for file at specified path", "figura.docs.file.read_string": "Reads whole file as string", "figura.docs.file.write_string": "Writes a string to a file", "figura.docs.file.write": "Writes value with specified provider in file", "figura.docs.file.read": "Reads value with specified reader from file", "figura.docs.file.mkdir": "Creates a directory at specified path. Returns true if folder was successfully created", "figura.docs.file.mkdirs": "Creates a directory at specified path including all parent directories. Returns true if folder was successfully created", "figura.docs.file.delete": "Deletes file/directory at specified path. Returns true if successful", "figura.docs.file.list": "Lists all files and directories at specified path, or returns null if directory does not exist or path is not a directory", "figura.docs.resources.get_paths": "Returns table with paths to all resources stored in avatar", "figura.docs.resources.get": "Returns input stream with data for resource at specified path", "figura.docs.net": "A global API that contains networking related features", "figura.docs.net.http": "Instance of HttpAPI", "figura.docs.net.socket": "Instance of SocketAPI", "figura.docs.net.is_networking_allowed": "Checks if your avatar can use networking features. Always false if networking is OFF in settings", "figura.docs.net.is_link_allowed": "Checks if specified link allowed for usage in networking api", "figura.docs.http": "A global API that contains HTTP related features", "figura.docs.http.request": "Creates request builder for specified URI", "figura.docs.http_request_builder": "A builder for HTTP request", "figura.docs.http_request_builder.uri": "Sets URI for this request, returns itself for chaining", "figura.docs.http_request_builder.method": "Sets method for this request, returns itself for chaining. If method is nil default value - \"GET\", will be used", "figura.docs.http_request_builder.body": "Sets body for this request, returns itself for chaining. If data is nil request will be sent without body", "figura.docs.http_request_builder.header": "Sets header for this request, returns itself for chaining. If value is nil header will be removed", "figura.docs.http_request_builder.get_uri": "Returns URI of this request", "figura.docs.http_request_builder.get_method": "Returns method of this request", "figura.docs.http_request_builder.get_body": "Returns body of this request", "figura.docs.http_request_builder.get_headers": "Returns table with all headers set for this request", "figura.docs.http_request_builder.send": "Sends this request and returns Future object that will contain response object once request is done", "figura.docs.future": "Object that contains result of operation that cant be finished immediately", "figura.docs.future.is_done": "Checks if future is done, either successfully or with error", "figura.docs.future.has_error": "Checks if error occurred while this future execution", "figura.docs.future.get_value": "Returns value of this future object if future was executed successfully", "figura.docs.future.get_or_error": "Throws error if it occurred while execution of this future, returns value otherwise", "figura.docs.future.throw_error": "Throws an error if it occurred while execution of this future.", "figura.docs.http_response": "Object that contains HTTP response", "figura.docs.http_response.get_data": "Returns input stream with response data", "figura.docs.http_response.get_response_code": "Returns response code", "figura.docs.http_response.get_headers": "Returns headers of this response", "figura.docs.socket_api": "A global API that is made for working with TCP sockets", "figura.docs.socket_api.open": "Opens connection to specified host and port. Returns future with Socket", "figura.docs.socket": "A TCP socket", "figura.docs.socket.get_input_stream": "Returns input stream bound to this socket", "figura.docs.socket.get_output_stream": "Returns output stream bound to this socket", "figura.docs.socket.get_port": "Returns port this socket is connected to", "figura.docs.socket.get_host": "Returns host this socket is connected to", "figura.docs.socket.is_connected": "Checks if this socket is connected", "figura.docs.socket.is_closed": "Checks if this socket is closed", "figura.docs.socket.close": "Closes this socket", "figura.network.header_disabled": "The %s header is disabled, skipping!"}