--<code by SciFi<PERSON><PERSON><PERSON><PERSON>>--

--Please ignore the variable names. This code was written while I was very tired.

--Setting variable defaults
local zoomKey = keybinds:newKeybind("Set this to your zoom key!" , "key.keyboard.c")
local isAwooga = false
local AwoogaAmount = 1

--nameplate.ALL:setText("Keralis1")    --Uncomment this line to set your nameplate to Keralis1

--Ping for pressing zoom
function pings.zoomPing()
  isAwooga = true
  animations.model.unawooga:stop()
  animations.model.awooga:play()
end

--Ping for releasing zoom and resetting all values
function pings.zoomReleasePing()
  isAwooga = false
  AwoogaAmount = 1
  animations.model.awooga:stop()
  animations.model.unawooga:play()
  models.model.root.Head.Eyes:setScale(1,1,1)
end

--Linking key presses to pings
zoomKey.press = pings.zoomPing
zoomKey.release  = pings.zoomReleasePing 

--Ping for scrolling up while zoomed to change eye size
function pings.AwoogaAdd()
  AwoogaAmount = AwoogaAmount + 1
  models.model.root.Head.Eyes:setScale(1,1,1 + 0.25*(math.clamp(AwoogaAmount, 1, 15)))
  AwoogaAmount = math.clamp(AwoogaAmount, 1, 15)
end

--Ping for scrolling down while zoomed to change eye size
function pings.AwoogaSubtract()
  AwoogaAmount = AwoogaAmount - 1
  models.model.root.Head.Eyes:setScale(1,1,1 + 0.25*(math.clamp(AwoogaAmount, 1, 15)))
  AwoogaAmount = math.clamp(AwoogaAmount, 1, 15)
end

--Function to activate the Add and Subtract pings when scrolling while zooming
function events.mouse_scroll(delta)
  if isAwooga == true then
    if delta > 0 then
      pings.AwoogaAdd()
    elseif delta < 0 then
      pings.AwoogaSubtract()
    end
  end
end