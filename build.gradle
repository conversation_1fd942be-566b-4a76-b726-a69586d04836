plugins {
	id "architectury-plugin" version "3.4-SNAPSHOT"
	id "dev.architectury.loom" version "1.5-SNAPSHOT" apply false
}

architectury {
	minecraft = rootProject.minecraft_version
}

subprojects {
	apply plugin: "dev.architectury.loom"

	loom {
		silentMojangMappingsLicense()
	}

	repositories {
		maven { url 'https://maven.terraformersmc.com/releases/' }
		maven { url 'https://api.modrinth.com/maven' }
		maven { url 'https://maven.quiltmc.org/repository/release/' }
		maven {
			name = 'GeckoLib'
			url 'https://dl.cloudsmith.io/public/geckolib3/geckolib/maven/'
			content {
				includeGroupByRegex("software\\.bernie.*")
				includeGroup("com.eliotlash.mclib")
			}
		}
	}

	dependencies {
		minecraft "com.mojang:minecraft:${rootProject.minecraft_version}"
		// The following line declares the mojmap mappings, you may use other mappings as well

		// Mods
		modCompileOnly "maven.modrinth:iris:$iris"
		modCompileOnly "maven.modrinth:immediatelyfast:$immediately_fast"
		modCompileOnly "maven.modrinth:iris:$iris"
	}
}

allprojects {
	apply plugin: "java"
	apply plugin: "architectury-plugin"
	apply plugin: "maven-publish"

	java_version = rootProject.java_version
	archivesBaseName = rootProject.archives_base_name
	version = rootProject.mod_version + "+" + rootProject.minecraft_version
	group = rootProject.maven_group
	if (project.hasProperty("release")) {
		jarVersion = version
	} else {
		jarVersion = version + "-" + 'git rev-parse --short HEAD'.execute().getText().trim()
	}

	repositories {
		// Add repositories to retrieve artifacts from in here.
		// You should only use this when depending on other mods because
		// Loom adds the essential maven repositories to download Minecraft and libraries from automatically.
		// See https://docs.gradle.org/current/userguide/declaring_repositories.html
		// for more information about repositories.
		maven { url 'https://jitpack.io' }
	}

	tasks.withType(JavaCompile) {
		options.encoding = "UTF-8"
		options.release = Integer.valueOf(java_version)
	}

	java {
		sourceCompatibility = JavaVersion.VERSION_17
		targetCompatibility = JavaVersion.VERSION_17
		withSourcesJar()
	}
}