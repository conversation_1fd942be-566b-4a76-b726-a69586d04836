# Gradle Configuration
org.gradle.jvmargs = -Xmx3G

# Environment Properties
# https://lambdaurora.dev/tools/import_quilt.html
java_version = 17
minecraft_version = 1.20.1
mappings = 1
enabled_platforms = fabric,forge

# Mod Properties
mod_version = 0.1.5-rc.6
maven_group = org.figuramc
archives_base_name = figura
assets_version = v2

# Libraries
# https://github.com/FiguraMC/luaj
# https://github.com/TakahikoKawa<PERSON>/nv-websocket-client
luaj = 3.0.8
nv_websocket = 2.14

# Fabric Properties
# https://fabricmc.net/develop
# https://modrinth.com/mod/fabric-api
fabric_api = 0.86.1*****.1
fabric_loader_version = 0.14.25

# Mod Dependencies
# https://modrinth.com/mod/modmenu
# https://modrinth.com/mod/iris
# https://modrinth.com/mod/immediatelyfast
# https://modrinth.com/mod/geckolib
# https://github.com/bernie-g/geckolib/blob/1.20.1/gradle.properties
modmenu = 7.2.1
iris = 1.7.1*****.1
immediately_fast = 1.2.0*****.1
geckolib_version = 1.20.1:4.4
mclib_version=20

# Forge Properties
# https://files.minecraftforge.net/net/minecraftforge/forge
forge_version = 1.20.1-47.1.43

# Quilt properties
# https://lambdaurora.dev/tools/import_quilt.html
# https://modrinth.com/mod/qsl
quilt_loader_version = 0.20.0-beta.5
quilt_fabric_api_version = 7.1.0+0.86.1-1.20.1

# Extra properties
run_on_quilt = false
run_with_modmenu = false
run_with_geckolib = false
jarVersion

# To run on quilt you must set this to true and go to fabric/gradle.properties to uncomment "loom.platform = quilt",
# and to clarify; This is for testing Figura on Quilt in a development environment only and not for building,
# therefore it will not produce a native quilt mod in this case.
