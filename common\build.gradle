architectury {
    common(rootProject.enabled_platforms.split(","))
}

loom {
    accessWidenerPath = file("src/main/resources/figura.accesswidener")
}

repositories {
    flatDir {
        dirs "$rootProject.projectDir/libs"
    }
}

dependencies {
    mappings loom.layered {
        mappings("org.quiltmc:quilt-mappings:$minecraft_version+build.$mappings:intermediary-v2")
        officialMojangMappings()
    }

    // Libraries
    implementation("com.github.FiguraMC.luaj:luaj-core:$luaj-figura")
    implementation("com.github.FiguraMC.luaj:luaj-jse:$luaj-figura")
    implementation("com.neovisionaries:nv-websocket-client:$nv_websocket")

    // Mods
    modCompileOnly ("software.bernie.geckolib:geckolib-fabric-$geckolib_version") {
        exclude group: "net.fabricmc"
    }

    implementation(annotationProcessor("io.github.llamalad7:mixinextras-common:0.3.6"))

    // We depend on fabric loader here to use the fabric @Environment annotations and get the mixin dependencies
    // Do NOT use other classes from fabric loader
    modImplementation "net.fabricmc:fabric-loader:${rootProject.fabric_loader_version}"

    // Test compile only is used to mount sources on an IDE without overriding vanilla classes with Optifine's
    testCompileOnly fileTree(dir: "$rootProject.projectDir/libs", include: '*.jar')
}

publishing {
    publications {
        maven(MavenPublication) {
            artifactId = "${project.archivesBaseName}-common-intermediary"
            version = project.version
            artifact(remapJar) {
                builtBy remapJar
                classifier ''
            }
            artifact(sourcesJar) {
                builtBy sourcesJar
                classifier 'sources'
            }
        }
    }
    setupRepositories(repositories)
}

void setupRepositories(RepositoryHandler repositories) {
    if (project.hasProperty("mavenUrl")) {
        repositories.maven {
            name = "figuraMaven"
            url project.mavenUrl
            credentials(PasswordCredentials)
            authentication {
                basic(BasicAuthentication)
            }
        }
    }
}

